// import { useSearchParams } from 'next/navigation';
import React, { useRef, MutableRefObject, useEffect, useCallback } from 'react';
import type { IAIChatInputStateContext } from '@bika/types/ai/context';
import { snackbarShow } from '@bika/ui/snackbar';
import { useSendAutoChatState } from './use-send-auto-chat-state';
import { AIChatView, type AIChatViewProps, type IAIChatViewHandle } from '../chat/ai-chat-view';
import { AIWelcome, type AIWelcomeProps } from '../chat/ai-welcome';

type Props = Pick<
  AIChatViewProps,
  // | 'selector'
  'inputState' | 'config' | 'forceLocale' | 'initAIIntent' | 'displayMode'
  // | 'context' | 'allowContextMenu'
> &
  Pick<
    AIWelcomeProps,
    | 'title'
    | 'description'
    | 'initPrompts'
    // | 'options'
    | 'customBottom'
    | 'avatars'
  > & {
    // redirect?: () => void;
    fullscreen?: boolean;
    skillsetIcons?: React.ReactNode;
  };

export interface IAIChatWithWelcomeHandle {
  stage: 'welcome' | 'chat';
  startNewChat: () => void;
}
/**
 * AI Wizard Launchpad
 * Just a textbox, before start a wizard
 *
 * @returns
 */
function InternalAIWizardWithWelcome(props: Props, ref: React.Ref<IAIChatWithWelcomeHandle>) {
  const [stage, setStage] = React.useState<'welcome' | 'chat'>('welcome');
  const chatUIHandle: MutableRefObject<IAIChatViewHandle | null> = useRef<IAIChatViewHandle>(null);

  const [chatLoaded, setChatLoaded] = React.useState(false);

  // 避免开发环境 useEffect 执行两次的问题
  const hasInit = React.useRef(false);

  const shouldSkipSendMessage = React.useRef(false);

  const reset = useCallback(() => {
    shouldSkipSendMessage.current = false;
    props.inputState.clear();
    setChatLoaded(false);
    chatUIHandle.current = null;
    setStage('welcome');
  }, [props.inputState]);

  const { data: autoSendInputState, setData: setAutoSendChatInputState } = useSendAutoChatState();

  React.useImperativeHandle(ref, () => ({
    stage,
    startNewChat: () => {
      reset();
    },
  }));

  const send = () => {
    if (!chatUIHandle.current || !chatUIHandle.current.doAppendMessage) {
      return;
    }
    chatUIHandle.current.doAppendMessage(props.inputState.input || '', props.inputState.contexts);
  };

  useEffect(() => {
    if (!chatLoaded) {
      return;
    }
    if (autoSendInputState) {
      send();
      return;
    }

    if (shouldSkipSendMessage.current) {
      return;
    }
    send();
  }, [chatLoaded, autoSendInputState]);

  const startChat = React.useCallback(() => {
    setStage('chat');
  }, []);

  // 自动设置 Stage 阶段
  React.useEffect(() => {
    if (hasInit.current) {
      return;
    }
    if (autoSendInputState) {
      if (!autoSendInputState) {
        hasInit.current = true;
        return;
      }
      props.inputState.patchAIChatInputState({
        contexts: autoSendInputState.contexts ?? [],
        input: autoSendInputState.input ?? '',
      });

      setTimeout(() => {
        startChat();
        hasInit.current = true;
        shouldSkipSendMessage.current = false;
      }, 20);
      setAutoSendChatInputState(undefined);
      return;
    }

    if (props.inputState.chatId) {
      // 有强制初始化的 chat？ 马上开始
      setStage('chat');
      // 这种不需要自动发送 chat 自动搞一条
      shouldSkipSendMessage.current = true;
      hasInit.current = true;
    }
  }, [props.inputState, autoSendInputState, startChat]);

  const { startNewChat } = useStartAiChat();

  if (stage === 'welcome') {
    return (
      <AIWelcome
        inputState={props.inputState}
        config={props.config}
        initPrompts={props.initPrompts}
        title={props.title} // t.ai_consultant.title}
        description={props.description} // t.ai_consultant.description}
        onSubmit={(_newInputState: IAIChatInputStateContext) => {
          console.log('start to Chat');

          const newChat = startNewChat(props.initAIIntent);

          snackbarShow({
            content: t.wizard.new_wizard_created,
            color: 'success',
          });
          setData({
            input: inputState.input ?? '',
            contexts: inputState.contexts ?? [],
          });

          inputState.setChatId(data.id);
        }}
        avatars={props.avatars}
        skillsetIcons={props.skillsetIcons}
        customBottom={props.customBottom}
        fullscreen={props.fullscreen}
      />
    );
  }
  return (
    <AIChatView
      ref={(instance) => {
        if (instance?.doAppendMessage) {
          setChatLoaded(true);
          chatUIHandle.current = instance;
        }
      }}
      inputState={props.inputState}
      config={props.config}
      displayMode={props.displayMode}
      forceLocale={props.forceLocale}
      initAIIntent={props.initAIIntent}
      startNewChat={reset}
      skillsetIcons={props.skillsetIcons}
    />
  );
}

export const AIWizardWithWelcome = React.memo(React.forwardRef(InternalAIWizardWithWelcome));
