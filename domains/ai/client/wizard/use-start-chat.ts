import { useTRPCQuery } from '@bika/api-caller/context';
import type { AIIntentParams } from '@bika/types/ai/bo';
import type { AIWizardVO } from '@bika/types/ai/vo';
import { useSpaceContext } from '@bika/types/space/context';

export const useStartAiChat = () => {
  const trpcQuery = useTRPCQuery();
  const spaceContext = useSpaceContext();

  const createNewWizard = trpcQuery.ai.newWizard.useMutation();

  const startNewChat = async (intent?: AIIntentParams): Promise<AIWizardVO> => {
    const result = await createNewWizard.mutateAsync({
      spaceId: spaceContext?.data?.id,
      intent: intent || {
        type: 'SEARCH', // default ai intent
      },
    });

    return result;
  };

  return {
    startNewChat,
    isLoading: createNewWizard.isPending,
    error: createNewWizard.error,
    reset: createNewWizard.reset,
  };
};
