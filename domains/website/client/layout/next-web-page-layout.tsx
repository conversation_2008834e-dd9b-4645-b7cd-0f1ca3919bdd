'use client';

import { usePathname, useSearchParams } from 'next/navigation';
import React, { useMemo } from 'react';
import type { FooterColumn } from '@bika/contents/config/client/footer/types';
import { Locale } from 'basenext/i18n';
import type { WebsiteNotificationBarsRemoteStorageProperty } from '@bika/types/system/remote-storage';
import { useGlobalContext } from '@bika/types/website/context';
import { Drawer } from '@bika/ui/drawer';
import { useUIFrameworkContext } from '@bika/ui/framework/context';
import DragPage from './drag-page/index';
import style from './index.module.css';
import { Footer } from '../footer';
import { Header } from '../header';
import { MobileHeader } from '../header/mobile-header';
import { NotificationBarsArea } from '../redirect-site-tips/notification-bars-area';
import { BikaWebsiteLeftSidebar } from '../website-left-sidebar';

// 顶部header？侧边栏sidebar？无header？
export type WebPageMenuMode = 'TOP_MENU' | 'SIDEBAR' | null;

/**
 * WebPage的Header有两种模式
 */
function getWebPageMenuMode(
  pathname: string,
  searchParams: URLSearchParams,
  isMobile: boolean,
  lang: Locale,
  homepage: string | null,
): WebPageMenuMode {
  const path = lang === 'en' ? `/en${pathname}` : pathname;
  if (homepage === 'TEMPLATE_CENTER') {
    return 'SIDEBAR';
  }

  if (
    path === `/${lang}/template` ||
    path.startsWith(`/${lang}/template/`) ||
    path.startsWith(`/${lang}/contact-service`) ||
    path.startsWith(`/${lang}/integration`) ||
    path.startsWith(`/${lang}/ai-app-builder`) ||
    isMobile
  ) {
    return 'SIDEBAR';
  }
  if (
    path.startsWith(`/${lang}/template-detail/`) ||
    path.startsWith(`/${lang}/template-architecture/`) ||
    path.startsWith(`/${lang}/template-workflow/`)
  ) {
    return null;
  }
  return 'TOP_MENU';
}

interface PageProps {
  notificationBars?: WebsiteNotificationBarsRemoteStorageProperty;

  params: {
    lang: Locale;
  };
  config: {
    footer: FooterColumn[];
  };
  homepage: string | null;
  children: React.ReactNode;
}

// 顶部header？侧边栏sidebar？无header？

/**
 * 网页的next layout
 * @param props
 * @returns
 */
export function NextWebPageLayout(props: PageProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const frameworkCtx = useUIFrameworkContext();

  const ctx = useGlobalContext();
  const [open, setOpen] = React.useState(false);

  const isSelfHosted = ctx.appEnv === 'SELF-HOSTED';

  const menuMode = useMemo(
    () => getWebPageMenuMode(pathname, searchParams, frameworkCtx.isMobile, props.params.lang, props.homepage),
    [frameworkCtx.isMobile, pathname, props.params.lang, searchParams],
  );

  const { params, children } = props;

  if (menuMode === 'SIDEBAR') {
    if (frameworkCtx.isMobile) {
      return (
        <div className={style.root}>
          <NotificationBarsArea bars={props.notificationBars} />
          <MobileHeader
            onClick={() => {
              setOpen(!open);
            }}
          />
          <Drawer open={open} onClose={() => setOpen(false)}>
            <BikaWebsiteLeftSidebar onClickItem={() => setOpen(false)} />
          </Drawer>
          {children}
        </div>
      );
    }

    return (
      <div className={style.root}>
        {!isSelfHosted && <NotificationBarsArea bars={props.notificationBars} />}
        <div className={style['layout-sidebar']}>
          <DragPage
            localId="website-sidebar"
            left={<BikaWebsiteLeftSidebar />}
            middle={<main className={style['layout-sidebar-main']}>{children}</main>}
            maxWidth={280}
            minWidth={280}
            defaultWidth={280}
            allowLess={false}
          />
        </div>
      </div>
    );
  }
  if (menuMode === 'TOP_MENU') {
    return (
      <div className={style.root}>
        {!isSelfHosted && <NotificationBarsArea bars={props.notificationBars} />}
        <Header />
        <main className={style.main}>{children}</main>
        <Footer
          locale={props.params.lang}
          data={props.config.footer}
          isFromCNHost={ctx.isFromCNHost}
          appEnv={ctx.appEnv}
        />
      </div>
    );
  }
  return children;
}
