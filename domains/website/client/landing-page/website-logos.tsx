/* eslint-disable no-script-url */

'use client';

import Box from '@mui/joy/Box';
import Grid from '@mui/joy/Grid';
import { iStringParse, type Locale } from 'basenext/i18n';
import type { FC } from 'react';
import * as React from 'react';
import { clientLogosConfig } from '@bika/contents/testimonials/client-logos';
import { partnersLogosConfig } from '@bika/contents/testimonials/partners-logos';
import { Stack } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/website/typography/index';
import style from './index.module.css';

interface Props {
  locale: Locale;
  kind: 'clients' | 'partners';
  display?: 'GRID' | 'CAROUSEL' | 'LIST';
  title?: string;
  // 是否屏蔽不可点击
  disableLinks?: boolean;
}

export function WebsiteLogos(props: Props) {
  const { locale, display = 'GRID' } = props;
  const ref = React.useRef<HTMLDivElement>(null);

  const logosConfig = props.kind === 'clients' ? clientLogosConfig : partnersLogosConfig;

  const title = props.title ? (
    <div
      style={{
        textAlign: 'center',
      }}
    >
      <Typography level={3}>{props.title}</Typography>
    </div>
  ) : null;

  React.useEffect(() => {
    if (display === 'CAROUSEL' && ref.current) {
      const scrollInterval = setInterval(() => {
        if (ref.current) {
          ref.current.scrollLeft += 1;
          if (ref.current.scrollLeft >= ref.current.scrollWidth - ref.current.clientWidth) {
            ref.current.scrollLeft = 0;
          }
        }
      }, 20);

      return () => clearInterval(scrollInterval);
    }
    return undefined;
  }, [display]);

  const renderLogo = (Logo: FC): React.ReactNode => (
    // @ts-expect-error - Logo component expects color prop but type definitions may not be available
    <Logo color={'var(--text-primary)'} />
  );

  if (display === 'CAROUSEL') {
    return (
      <>
        {title}

        <Stack
          ref={ref}
          my={2}
          onWheel={(e) => {
            e.preventDefault();
            e.stopPropagation();
            e.currentTarget.scrollLeft += e.deltaY;
          }}
          sx={{
            position: 'relative',
            width: 'calc(100vw - 20px)',
            left: '50%',
            transform: 'translateX(-50%)',
            overflowX: 'auto',
            paddingTop: '10px',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              width: 'max-content',
            }}
          >
            {logosConfig.map((logo, index) => (
              <Box
                key={index}
                sx={{
                  width: '200px',
                  height: '100px', // 限制高度
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  '& svg': {
                    maxWidth: '160px',
                    maxHeight: '80px',
                    width: 'auto',
                    height: 'auto',
                  },
                }}
              >
                {props.disableLinks === true ? (
                  renderLogo(logo.logoUrl)
                ) : (
                  <a href={logo.url || 'javascript:;'} target="_blank">
                    {renderLogo(logo.logoUrl)}
                  </a>
                )}
              </Box>
            ))}
          </Box>
        </Stack>
      </>
    );
  }

  if (display === 'LIST') {
    return (
      <>
        {title}
        <Box
          my={2}
          sx={{
            display: 'grid',
            gridTemplateColumns: {
              xs: '1fr',
              sm: 'repeat(2, 1fr)',
              md: 'repeat(3, 1fr)',
            },
            gap: 3,
            gridAutoRows: 'min-content',
          }}
        >
          {logosConfig.map((logo, index) => {
            const descriptionLength = iStringParse(logo.description, locale).length;
            const gridRowSpan = Math.ceil(descriptionLength / 100) + 1; // Dynamic height based on content

            return (
              <Box
                key={index}
                sx={{
                  p: 3,
                  border: '1px solid',
                  borderColor: 'divider',
                  borderRadius: 'md',
                  transition: 'all 0.2s ease',
                  gridRowEnd: `span ${gridRowSpan}`,
                  '&:hover': {
                    borderColor: 'primary.500',
                    boxShadow: 'sm',
                    transform: 'translateY(-2px)',
                  },
                }}
              >
                <Stack spacing={2} sx={{ textAlign: 'center' }}>
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      height: { xs: '80px', sm: '100px' },
                      mx: 'auto',
                      mb: 2,
                      '& svg': {
                        maxWidth: { xs: '300px', sm: '300px' },
                        width: 'auto',
                        height: 'auto',
                      },
                    }}
                  >
                    <a
                      target="_blank"
                      href={logo.url}
                      style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                    >
                      {renderLogo(logo.logoUrl)}
                    </a>
                  </Box>

                  <span
                    style={{
                      textAlign: 'left',
                    }}
                  >
                    <a
                      target="_blank"
                      href={logo.url}
                      style={{
                        textDecoration: 'none',
                        color: 'inherit',
                        fontSize: '20px',
                        fontWeight: 600,
                      }}
                    >
                      {iStringParse(logo.name, locale)}
                    </a>
                  </span>
                  <span
                    style={{
                      textAlign: 'left',
                    }}
                  >
                    {iStringParse(logo.description, locale)}
                  </span>
                </Stack>
              </Box>
            );
          })}
        </Box>
      </>
    );
  }

  if (display === 'GRID') {
    return (
      <div className={style.logoGrid}>
        {title}
        <Box my={2} mt={6} sx={{ flexGrow: 1 }}>
          <Grid container spacing={2}>
            {logosConfig.map((logo, index) => (
              <Grid
                key={index}
                xs={12}
                sm={6}
                md={3}
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: 80, // 原来是140，改小
                }}
              >
                <Box
                  sx={{
                    maxWidth: 160, // 原来120，改小
                    // maxHeight: 60, // 原来60，改小
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    '& svg': {
                      maxWidth: '100%',
                      maxHeight: '100%',
                      width: 'auto',
                      height: 'auto',
                    },
                  }}
                >
                  {props.disableLinks === true ? (
                    renderLogo(logo.logoUrl)
                  ) : (
                    <a href={logo.url || 'javascript:;'} target="_blank">
                      {renderLogo(logo.logoUrl)}
                    </a>
                  )}
                </Box>
              </Grid>
            ))}
          </Grid>
        </Box>
      </div>
    );
  }
}
