import { ToolDetailRenderer, PackageConfigurations } from '@toolsdk.ai/sdk-ts/react';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import React from 'react';
import { SpacePlanQuestion } from '@bika/contents/config/server/pricing/plan-question';
import { Locale } from 'basenext/i18n';
import type { LandingPage as LandingPageType } from '@bika/contents/pages/type';
import { FlowEditor } from '@bika/ui/editor/flow-editor/flow-editor';
import { Text } from '@bika/ui/text';
import { Typography } from '@bika/ui/website/typography/index';
import { VoiceOfCustomers } from '@bika/ui/website/voice-of-customers';
import ButtonGroup from './button/group';
import { CategoryTemplates } from './category-templates';
import Gallery from './gallery';
import IconBox from './iconbox';
import style from './index.module.css';
import Media from './media';
import NumberCard from './number-card';
import Pricing from './pricing';
import QABlock from './qa';
import Template from './template';
import { Testimonial } from './testimonial';
import { WebsiteLogos } from './website-logos';

const LandingPageTypeAnimation = dynamic(() =>
  import('./type-animation').then((module) => module.LandingPageTypeAnimation),
);

interface Props {
  data: LandingPageType;
  lang: Locale;
}

export function LandingPage(props: Props) {
  const { data, lang } = props;

  if (!data) {
    return null;
  }

  const render = () =>
    data?.blocks?.map((section, index) => {
      if (section.type === 'heading') {
        return (
          <section className={style.heading} key={index} style={{ textAlign: section.textAlign || 'center' }}>
            <Typography key={index} level={section.level}>
              {section.title}
            </Typography>
          </section>
        );
      }
      if (section.type === 'testimonial') {
        return <Testimonial key={index} block={section} />;
      }

      if (section.type === 'logos') {
        return (
          <WebsiteLogos
            kind={section.logoType}
            key={index}
            locale={section.locale}
            display={section.display}
            title={section.title}
            disableLinks={section.disableLinks}
          />
        );
      }

      if (section.type === 'number-card') {
        return <NumberCard key={index} data={section} />;
      }

      if (section.type === 'voc') {
        return <VoiceOfCustomers title={section.title} key={index} locale={section.locale} />;
      }
      if (section.type === 'skills') {
        const { title, description, data: skills } = section;
        return (
          <section className={style.skills} key={index}>
            <Typography level={2}>{title}</Typography>
            {description && (
              <Text level={1} className={style.text} color="var(--text-secondary)">
                {description}
              </Text>
            )}
            <div className={style.skillsList}>
              {skills.map((skill, idx) => (
                <ToolDetailRenderer
                  key={idx}
                  tool={{
                    name: skill.name,
                    description: skill.description,
                    logo: skill.logo,
                    inputFields: [],
                    key: skill.key,
                    kind: 'TOOL',
                  }}
                />
              ))}
            </div>
          </section>
        );
      }

      if (section.type === 'skillset-configuration') {
        const { title, description, data } = section;
        return (
          <section className={style.skillsetConfiguration} key={index}>
            <Typography level={2}>{title}</Typography>
            {description && (
              <Text level={1} className={style.text} color="var(--text-secondary)">
                {description}
              </Text>
            )}
            {/* @ts-ignore */}
            <PackageConfigurations config={data} />
          </section>
        );
      }

      if (section.type === 'feature') {
        return (
          <section
            className={style.feature}
            data-direction={section.textPosition === 'right' ? 'right' : 'left'}
            key={index}
          >
            <div className={style.left}>
              <Typography level={2}>{section.title}</Typography>
              <Text level={1} className={style.text} color="var(--text-secondary)">
                {section.description}
              </Text>
              <Text level={3} className={style.addonText} color="var(--text-secondary)">
                {section.addonText}
              </Text>
              {section.addonLogos && (
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    flexDirection: 'row',
                    flexWrap: 'wrap',
                  }}
                >
                  {section.addonLogos.map((logo, idx) => (
                    <a href={logo.url} target="_blank" key={idx}>
                      <Image width={123} height={48} alt={logo.img} src={logo.img} key={idx} />
                    </a>
                  ))}
                </div>
              )}
              {section.buttons && (
                <ButtonGroup
                  buttonClassName={style.button}
                  className={style.buttons}
                  data={section.buttons}
                  templateId={data.templateId}
                />
              )}
            </div>
            <div className={style.right}>{...section.media.map((media, idx) => <Media data={media} key={idx} />)}</div>
          </section>
        );
      }
      if (section.type === 'hero') {
        return (
          <section
            className={style.hero}
            key={index}
            style={{
              height: section.direction !== 'column' ? '75vh' : 'auto',
              // direction row | column
              flexDirection: section.direction,
              textAlign: section.textAlign,
            }}
          >
            <div className={`${style.hero_block} ${section.direction !== 'column' ? style.left : ''}`}>
              {/* 顶部小公告条 */}
              {section.announcement && (
                <div className={style.announcementChip}>
                  <span>{section.announcement}</span>
                </div>
              )}

              {/* 如name是字符串，如是数组，则打字机 */}
              <Typography tag="h1" level={1}>
                {typeof section.name === 'string' && <>{section.name}</>}
                {typeof section.name === 'object' && (
                  <>
                    <LandingPageTypeAnimation sequence={section.name} />
                  </>
                )}
              </Typography>

              <Text level={1} className={style.text} color="var(--text-secondary)">
                {section.tagline}
              </Text>

              {section.buttons && (
                <ButtonGroup
                  className={style.buttons}
                  buttonClassName={style.button}
                  data={section.buttons}
                  templateId={data.templateId}
                />
              )}
            </div>
            {/* 多媒体区 */}
            <div
              className={`${style.hero_block} ${section.direction !== 'column' ? style.right : ''}`}
              style={{
                marginTop: section.direction === 'column' ? '20px' : undefined,
                // ...mediaBackground 可能不存在
                padding: section.mediaBackground && section.direction === 'column' ? '60px 0 120px 0' : undefined,
              }}
            >
              {section.mediaBackground && <div className={style.mbg} style={{ ...section.mediaBackground }}></div>}

              {/* 如果是打字机，则不显示图片 */}
              {...section.media.map((media, idx) => <Media data={media} key={idx} />)}
            </div>
          </section>
        );
      }

      if (section.type === 'program-card') {
        return (
          <section className={style.program_card} key={index}>
            <Typography style={{ marginBottom: 20 }} level={2}>
              {section.title}
            </Typography>
            <Text className={style.text} color="var(--text-secondary)">
              {section.description}
            </Text>
            <div className={style.program_card_group}>
              <div className={style.program_card_group_title}>
                <Typography level={4}>{section.cardTitle}</Typography>
              </div>
              {section.group.map((group, idx) => (
                <div key={idx} className={style.program_card_group_item}>
                  <Typography level={5} className={style.title}>
                    {group.title}
                  </Typography>
                  {group.text.map((text, idx) => (
                    <div key={idx} className={style.program_card_group_item_text}>
                      <Text level={3} color={group.iconColor}>
                        ✓
                      </Text>
                      <Text className={style.text} level={3}>
                        {text}
                      </Text>
                    </div>
                  ))}
                </div>
              ))}
              <Text level={4} className={style.program_card_group_footer} color="var(--text-secondary)">
                {section.cardDescription}
              </Text>
            </div>
          </section>
        );
      }

      if (section.type === 'content') {
        return (
          <section className={style.content} key={index}>
            <div className={style.left}>
              <Typography level={2}>{section.title}</Typography>
              {section.description !== undefined &&
                (typeof section.description === 'string' ? [section.description] : section.description).map((item) => (
                  <Text key={item} className={style.text} color="var(--text-secondary)">
                    {item}
                  </Text>
                ))}
              {section.buttons && (
                <ButtonGroup
                  buttonClassName={style.button}
                  className={style.buttons}
                  data={section.buttons}
                  templateId={data.templateId}
                />
              )}
            </div>
            <div className={style.right}>{...section.media.map((media, idx) => <Media data={media} key={idx} />)}</div>
          </section>
        );
      }
      if (section.type === 'html') {
        return (
          <section key={index} className={style.html}>
            <div style={section.styles} dangerouslySetInnerHTML={{ __html: section.contents.join('\n') }} />
            {section.buttons && (
              <ButtonGroup className={style.buttons} data={section.buttons} templateId={data.templateId} />
            )}
          </section>
        );
      }
      if (section.type === 'template-graph') {
        return (
          <section key={index} className={style.template_graph_section}>
            <Typography style={{ marginBottom: 20 }} level={2}>
              {section.title}
            </Typography>
            <Text className={style.text} color="var(--text-secondary)">
              {section.description}
            </Text>

            <div className={style.template_graph}>
              <FlowEditor
                readonly
                showControl
                data={{
                  type: 'node-resources',
                  resources: section.resources,
                }}
              />
            </div>
          </section>
        );
      }

      if (section.type === 'pricing') {
        return <Pricing data={section} key={index} />;
      }

      if (section.type === 'qa') {
        const schemaData = {
          '@context': 'https://schema.org',
          '@type': 'FAQPage',
          mainEntity: SpacePlanQuestion[lang].map((item) => ({
            '@type': 'Question',
            name: item.q,
            acceptedAnswer: {
              '@type': 'Answer',
              text: item.a,
            },
          })),
          potentialAction: {
            '@type': 'ReadAction',
            target: {
              '@type': 'EntryPoint',
              urlTemplate: 'https://bika.ai/help',
            },
          },
        };

        return (
          <section className={style.qa} key={index}>
            <script
              type="application/ld+json"
              dangerouslySetInnerHTML={{
                __html: JSON.stringify(schemaData),
              }}
            />
            <Typography style={{ marginBottom: 20 }} level={2}>
              {section.title}
            </Typography>
            <div className={style.qalist}>
              {section.useConfig === 'BEFORE' &&
                SpacePlanQuestion[lang].map((configQA, idx) => (
                  <QABlock key={idx} question={configQA.q} answer={configQA.a} />
                ))}

              {section.list.map((item, idx) => (
                <QABlock key={idx} question={item.q} answer={item.a} />
              ))}

              {section.useConfig === 'AFTER' &&
                SpacePlanQuestion[lang].map((configQA, idx) => (
                  <QABlock key={idx} question={configQA.q} answer={configQA.a} />
                ))}
            </div>
          </section>
        );
      }

      if (section.type === 'template') {
        return <Template data={section} key={index} />;
      }
      if (section.type === 'category-templates') {
        return <CategoryTemplates data={section} key={index} />;
      }

      if (section.type === 'gallery') {
        return <Gallery data={section} key={index} />;
      }
      if (section.type === 'iconbox') {
        return <IconBox data={section} key={index} />;
      }
      return null;
    });

  return (
    <div className={style.container}>
      {props.data.background && <div className={style.background} style={props.data.background || {}}></div>}
      {render()}
      {/* 这是一个注入的ID */}
      {data.templateId && <input id="ENTRY_TEMLATEID" disabled type="hidden" value={data.templateId} />}
    </div>
  );
}
