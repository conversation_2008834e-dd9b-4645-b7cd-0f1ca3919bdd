'use server';

import { AvatarLogo } from 'basenext/avatar';
import { iStringParse } from 'basenext/i18n';
import React from 'react';
import { TemplatesVerified } from '@bika/contents/config/server/template/templates-init-data';
import type { CategoryTemplatesBlock, TemplateBlock } from '@bika/contents/pages/type';
import { LocalContentLoader } from '@bika/server-orm/content';
import type { CustomTemplate } from '@bika/types/template/bo';
import Template from '../template';

interface Props {
  data: CategoryTemplatesBlock;
}

/**
 * 模板集合，包住模板卡片Template
 * @param props
 */
export async function CategoryTemplates(props: Props) {
  const templates: CustomTemplate[] = LocalContentLoader.template.fsLocalRandomTemplateTemplates(props.data.category);

  const templateBlock: TemplateBlock = (() => {
    const tplData = [];
    for (const tpl of templates) {
      if (!tpl.visibility || tpl.visibility === 'PUBLIC' || tpl.visibility === 'WAITING_LIST') {
        let coverUrl;
        if (typeof tpl.cover === 'string') {
          coverUrl = tpl.cover;
        } else {
          const avatarLogo = tpl.cover as AvatarLogo;
          if (avatarLogo.type === 'URL') {
            coverUrl = avatarLogo.url;
          } else {
            coverUrl = '';
            console.error('Template cover is not a string', tpl);
          }
        }
        tplData.push({
          cover: coverUrl,
          name: iStringParse(tpl.name, props.data.locale),
          templateId: tpl.templateId,
          description: iStringParse(tpl.description, props.data.locale),
          // stars: tpl.stars,
          verified: TemplatesVerified[tpl.templateId],
          visibilty: tpl.visibility,
        });
      }
    }
    return {
      type: 'template',
      data: tplData,
      title: props.data.title,
      description: props.data.description,
      clickMode: props.data.clickMode,
    };
  })();
  // }, [props.data, templates]);
  return (
    <>
      {/* {templates.map((tpl, index) => ( */}
      <Template data={templateBlock} />
      {/* ))} */}
    </>
  );
}
