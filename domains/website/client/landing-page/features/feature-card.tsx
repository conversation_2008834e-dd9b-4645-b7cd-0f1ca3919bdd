'use client';

import Card from '@mui/joy/Card';
import CardContent from '@mui/joy/CardContent';
import Tooltip from '@mui/joy/Tooltip';
import Typography from '@mui/joy/Typography';
import type { Locale } from 'basenext/i18n';
import type { NodeResourceType } from '@bika/types/node/bo';
import type { IFeatureAbilityConfigDetail } from '@bika/types/website/bo';
import { NodeIcon } from '@bika/ui/node/icon';

interface Props {
  lang: Locale | undefined;
  value: IFeatureAbilityConfigDetail;
}
export function FeatureCard(props: Props) {
  return (
    <Tooltip title={props.value.description}>
      <Card
        variant="outlined"
        component="a"
        target="_blank"
        href={`/${props.lang || 'en'}/help/reference/${props.value.featureType}/${props.value.key.toLowerCase().replaceAll('_', '-')}`}
        sx={{ textDecoration: 'none', color: 'inherit', cursor: 'pointer' }}
      >
        <CardContent sx={{ textAlign: 'center' }}>
          <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '8px' }}>
            <NodeIcon
              value={
                props.value.featureType === 'node-resource'
                  ? {
                      kind: props.value.featureType,
                      nodeType: props.value.key as NodeResourceType,
                    }
                  : {
                      kind: 'avatar',
                      avatar: {
                        type: 'URL',
                        url: props.value.iconPath,
                      },
                    }
              }
            />
          </div>
          <Typography level="title-md">{props.value.label}</Typography>
          {/* <Typography>{props.value.description}</Typography> */}
        </CardContent>
      </Card>
    </Tooltip>
  );
}
