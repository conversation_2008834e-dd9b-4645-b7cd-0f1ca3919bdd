'use client';

import dayjs from 'dayjs';
import React from 'react';
import { iString, Locale, iStringParse } from 'basenext/i18n';
import { FeatureType } from '@bika/types/website/bo';
import { GlobalContext, IGlobalContext } from '@bika/types/website/context';
import { MarkdownBody } from '@bika/ui/markdown';
import { getStoryComponent } from '../../../story-components/config';
import { StoryComponentProvider } from '../../../story-components/providers/story-component-provider';

function MockGlobalContextProvider({ children }: { children: React.ReactNode }) {
  const value: IGlobalContext = React.useMemo(
    () =>
      ({
        api: { action: { create: () => {} } },
        servers: { docServerUrl: '', storagePublicUrl: '', formAppAIBaseUrl: 'https://formapp.ai' },
        track: () => {},
        theme: {},
        timezone: dayjs.tz.guess() ?? 'Asia/Shanghai',
        setTimeZone: () => {},
        authContext: {
          onlineSessions: [],
          registerSseHandler: () => {},
          registerSseEventsHandler: () => {},
          unregisterSseEventsHandler: () => {},
          unregisterSseHandler: () => {},
        },
        systemConfiguration: {},
        appEnv: 'LOCAL',
        version: '',
        mode: 'SPA',
        showUIModal: () => {},
        genUIModalQueryString: () => '',
      }) as unknown as IGlobalContext,
    [],
  );

  return <GlobalContext.Provider value={value}>{children}</GlobalContext.Provider>;
}

export function AutoHelpDocComponent({
  route,
  helpName,
  locale,
}: {
  route: FeatureType;
  helpName: string;
  locale: Locale;
}) {
  const StoryComponent = getStoryComponent(route, helpName);

  const description: iString = {
    'zh-CN': `这是本功能的 UI 交互演示、功能参数,您可以体验一下。请注意,这个交互演示仅展示 UI 界面和一些功能参数,没有实际的数据操作。如果您想要真正使用此功能,请登录系统,进入资源编辑器,届时功能将正式生效。`,
    en: 'This is a UI interaction demo and function parameter example of this feature. You can experience it. Please note that this interaction demo only shows the UI interface and some parameters, and does not have actual data operations. If you want to really use this function, please log in to the system and enter the resource editor, and the function will take effect at that time.',
    'zh-TW':
      '這是本功能的 UI 交互演示、功能参数,您可以體驗一下。請注意,這個交互演示僅展示 UI 界面和一些功能參數,沒有實際的數據操作。如果您想要真正使用此功能,請登錄系統,進入資源編輯器,屆時功能將正式生效。',
    ja: 'これはこの機能の UI インタラクションデモおよび機能パラメータの例です。 体験してみてください。 このインタラクションデモは UI インターフェースと一部のパラメータのみを表示し、実際のデータ操作は行われません。 この機能を実際に使用したい場合は、システムにログインしてリソースエディターに入り、その時点で機能が有効になります。',
  };

  const demo: iString = {
    en: 'UI DEMO',
    'zh-CN': '功能演示',
    'zh-TW': '功能演示',
    ja: '機能デモ',
  };
  const goto: iString = {
    'zh-CN': '→ 前往工作空间站',
    en: '→ Go to Space',
    'zh-TW': '→ 前往工作空間站',
    ja: '→ スペースに移動',
  };

  return (
    <>
      {StoryComponent && (
        <>
          {route !== 'formula' && (
            <MarkdownBody>
              <h2>{iStringParse(demo, locale)}</h2>
              <blockquote>
                {iStringParse(description, locale)}

                <a target="_blank" href="/space">
                  {iStringParse(goto, locale)}
                </a>
              </blockquote>
            </MarkdownBody>
          )}

          <MockGlobalContextProvider>
            <StoryComponentProvider>{StoryComponent}</StoryComponentProvider>
          </MockGlobalContextProvider>
        </>
      )}
    </>
  );
}
