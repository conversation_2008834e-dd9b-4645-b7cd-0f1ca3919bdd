'use server';

/* eslint-disable @next/next/no-img-element */
import { TemplateCards } from '@bika/contents/docs/components/template-cards';
import { getServerDictionary } from '@bika/contents/i18n/server';
import { AutoHelpGenSO } from '@bika/server-orm/content/auto-help-gen-so';
import { LocalContentLoader } from '@bika/server-orm/content/local-content-loader';
import { iStringParse, type Locale } from 'basenext/i18n';
import type { TemplateCardInfoVO } from '@bika/types/template/vo';
import { FeatureType } from '@bika/types/website/bo';
import { MarkdownBody } from '@bika/ui/markdown';
import { AutoHelpDocComponent } from './auto-help-doc-component';

export async function AutoHelpDetailView({
  locale,
  route,
  helpName,
}: {
  locale: Locale;
  route: FeatureType;
  helpName: string;
}) {
  // features-list

  // features detail
  const { iconPath, label, description, links } = await AutoHelpGenSO.getAutoHelpConfig(locale, route, helpName);
  const t = await getServerDictionary(locale);
  const templates = await LocalContentLoader.template.autoRandomTemplates({ type: route, name: helpName }, 10);

  const templateInfos: TemplateCardInfoVO[] = templates.map((tpl) => ({
    templateId: tpl.templateId,
    name: iStringParse(tpl.name, locale),
    description: iStringParse(tpl.description, locale),
    cover: tpl.cover as string,
    category: tpl.category,
  }));

  return (
    <>
      <MarkdownBody>
        {/* 图标标题描述 */}
        <img alt={description} width={150} height={150} src={iconPath} />

        <h1>{label}</h1>

        <p>{description}</p>

        {/* 相关链接 */}
        {links && links.length > 0 && (
          <>
            <h2>
              {iStringParse(
                {
                  en: 'Related Links',
                  'zh-CN': '相关链接',
                  'zh-TW': '相關鏈接',
                  ja: '関連リンク',
                },
                locale,
              )}
            </h2>

            <ul>
              {links.map((link, index) => (
                <li key={index}>
                  <a target="_blank" href={link.url}>
                    {link.text ? iStringParse(link.text, locale) : link.url}
                  </a>
                </li>
              ))}
            </ul>
          </>
        )}
      </MarkdownBody>
      {/* UI Demo */}
      <AutoHelpDocComponent route={route} helpName={helpName} locale={locale} />
      <MarkdownBody>
        {/* 相关模板 */}
        {templateInfos.length > 0 && (
          <>
            <h2>
              {iStringParse(
                {
                  en: 'Recommend Templates',
                  'zh-CN': '推荐模板',
                  'zh-TW': '推薦模板',
                  ja: 'おすすめのテンプレート',
                },
                locale,
              )}
            </h2>
            <blockquote>
              {iStringParse(
                {
                  en: 'These are the related templates that includes the feature. You can install it to get it started.',
                  'zh-CN': '这些是包含此功能的相关模板，您可以安装它来开始直接使用。',
                  'zh-TW': '這些是包含此功能的相關模板，您可以安裝它來開始直接使用。',
                  ja: 'これらは機能を含む関連テンプレートです。 インストールして開始できます。',
                },
                locale,
              )}
            </blockquote>
            {/* 推荐模板 */}
            <TemplateCards buttonText={t.template.get} templates={templateInfos} locale={locale} />
          </>
        )}
      </MarkdownBody>
    </>
  );
}
