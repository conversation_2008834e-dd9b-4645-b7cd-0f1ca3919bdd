import assert from 'assert';
import { isEmail } from 'basenext/utils';
import { usePathname } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import { getAppEnv } from 'sharelib/app-env';
import { useApiCaller } from '@bika/api-caller/context';
import type { AdminLoginInput, LoginInput } from '@bika/types/auth';
import type { OnlineSessionVO } from '@bika/types/system/online-session';
import { SSEEvent, SSEEventSchema } from '@bika/types/user/bo';
import type { AuthVO } from '@bika/types/user/vo';
import type { AuthContextProps, SSEEventName, SSEEventMap } from '@bika/types/website/bo';

interface Params {
  /**
   *
   * @deprecated Auth  一般都是从客户端获取的，不从服务端渲染，避免 SSR + CDN带来的问题
   */
  initialAuth: AuthVO | null | undefined;
}

export function useAuth(params: Params): AuthContextProps {
  const { initialAuth } = params;
  const [me, setMe] = useState<AuthVO | null>(initialAuth || null);
  const { trpc, trpcQuery, newSse } = useApiCaller();
  const [loading, setLoading] = useState(false);
  // 缓存我本人的在线会话ID，只有1个
  const [cacheOnlineSessionId, setCacheOnlineSessionId] = useState<string | null>(null);
  // 我的SSE
  const [sse, setSSE] = useState<EventSource | null>(null);

  const sseHandlers = useRef<Partial<SSEEventMap>>({});

  function callSseHandler<E extends SSEEvent['name']>(data: Extract<SSEEvent, { name: E }>) {
    sseHandlers.current[data.name]?.(data);
  }

  const pathname = usePathname();
  const appEnv = getAppEnv();

  const sendVerificationMail = trpcQuery.email.sendVerificationMail.useMutation();
  const sendVerificationSms = trpcQuery.sms.sendVerificationSms.useMutation();
  const quickLoginCall = trpcQuery.auth.quickLogin.useMutation();

  useEffect(() => {
    if (initialAuth) {
      setMe(initialAuth);
    }
  }, [initialAuth]);

  // 记录其它用户的在线会话
  const [othersOnlineSessions, setOthersOnlineSessions] = useState<OnlineSessionVO[]>([]);

  const [promise, setPromise] = useState<{
    reject: (reason?: any) => void;
    resolve: (code: string) => void;
  } | null>(null);

  function buildAuthURL(provider: string, options: { [key: string]: string | undefined }) {
    const queryParams = Object.keys(options)
      .filter((key) => options[key] !== undefined && options[key] !== null)
      .map((key) => `${key}=${encodeURIComponent(options[key]!)}`)
      .join('&');

    const url = `/api/auth/${provider}${queryParams ? `?${queryParams}` : ''}`;
    return url;
  }

  function linkExternalUser(provider: string, options: { [key: string]: string | undefined }): Promise<string> {
    const url = buildAuthURL(provider, options);
    const w = 600;
    const h = 600;
    const screenWidth = window.screen.width;
    const screenHeight = window.screen.height;
    const left = screenWidth / 2 - w / 2;
    const top = screenHeight / 2 - h / 2;
    const features = `width=${w},height=${h},top=${top},left=${left}`;
    window.open(url, '', features);
    const p = new Promise<string>((resolve, reject) => {
      setPromise({ resolve, reject });
    });
    return p;
  }

  function onMessage(event: MessageEvent) {
    if (typeof event.data === 'object' && typeof event.data.code === 'string') {
      if (event.data.error === 'ExternalAlreadyLinkedError') {
        if (promise) {
          promise.reject({
            message: event.data.error,
            type: event.data.type,
            code: event.data.code,
          });
          setPromise(null);
        }
      } else if (!event.data.error) {
        if (promise) {
          promise.resolve(event.data.code);
        }
      }
    }
  }

  useEffect(() => {
    if (promise) {
      window.addEventListener('message', onMessage);
      return () => {
        window.removeEventListener('message', onMessage);
      };
    }
    window.removeEventListener('message', onMessage);
  }, [promise]);

  async function refetchMe() {
    setLoading(true);
    const auth = await trpc.auth.getMe.query();
    setMe(auth!);

    setLoading(false);
    return auth ?? null;
  }

  async function sendEmailVerificationCode(email: string, checkEmail?: boolean) {
    const result = await sendVerificationMail.mutateAsync({ email, check: checkEmail });

    return result;
  }

  async function sendSmsVerificationCode(phone: string) {
    await sendVerificationSms.mutateAsync({ phone });
  }

  async function quickLogin(referralCode?: string) {
    setLoading(true);
    await quickLoginCall.mutateAsync({ referralCode });
    await refetchMe();
    setLoading(false);
  }

  async function loginWithUsername(input: LoginInput) {
    setLoading(true);
    await trpc.auth.login.mutate(input);
    await refetchMe();
    setLoading(false);
  }

  async function siteAdminLogin(input: AdminLoginInput) {
    setLoading(true);
    await trpc.auth.adminLogin.mutate(input);
    await refetchMe();
    setLoading(false);
  }

  async function loginWithQuickCode(code: string) {
    setLoading(true);
    await trpc.auth.loginWithQuickCode.mutate({ code });
    // 相当于切换账号了 直接重进刷新页面
    window.location.href = '/space';
  }

  async function bindUsername(username: string, verificationCode: string) {
    setLoading(true);
    try {
      const ret = isEmail(username)
        ? await trpc.user.bindEmail.mutate({ email: username, verificationCode })
        : await trpc.user.bindPhone.mutate({ phone: username, verificationCode });
      if (typeof ret === 'boolean') {
        await refetchMe();
        return true;
      }
      if (ret.error) {
        return ret.quickCode;
      }
      return false;
    } finally {
      setLoading(false);
    }
  }

  async function logout(redirect?: string) {
    setLoading(true);
    await trpc.auth.logout.mutate();
    setMe(null);
    window.location.href = redirect ?? '/';
  }

  useEffect(() => {
    // 一旦登录，连接SSE
    if (me) {
      const { eventSource: newSSE, onlineSessionId: newOnlineSessionId } = newSse();

      newSSE.onopen = (_ev) => {
        console.log('see subscriber initialized status', newSSE.readyState);
      };
      newSSE.onmessage = async (event) => {
        assert(event.data, 'SSE event must have event data');
        const dataObj = JSON.parse(event.data);
        if (appEnv !== 'PRODUCTION') console.log('[SSE onmessage]', event.lastEventId, dataObj);

        const parsed = SSEEventSchema.parse(dataObj);
        callSseHandler(parsed);
        // const sseData = parsed;
        // const sseHandler = sseHandlers.current[sseData.name];
        // if (sseHandler !== undefined) {
        //   sseHandler(sseData);
        // }
      };

      setSSE(newSSE);
      setCacheOnlineSessionId(newOnlineSessionId);

      console.log(`SSE connected, id: ${newOnlineSessionId}`);
    } else {
      sse?.close();
      setSSE(null);
    }
  }, [me]);

  // path发生变化时，主动告诉服务器，我在线、在这个路由（tRPC会自动合并多个请求）
  useEffect(() => {
    if (sse && cacheOnlineSessionId) {
      if (me) {
        trpc.system.onlineSessionLog
          .mutate({
            onlineSessionId: cacheOnlineSessionId,
            route: pathname,
          })
          .then((data) => {
            console.log('online sessions', data);
            setOthersOnlineSessions(data);
          }); // 异步记录即可
      }
    }
  }, [pathname, me, sse, cacheOnlineSessionId]);

  // 注册单个SSE事件
  const registerSseHandler = <E extends SSEEventName>(name: E, handler: SSEEventMap[E]) => {
    sseHandlers.current[name] = handler;
  };

  // 注册多个SSE事件
  const registerSseEventsHandler = <E extends SSEEventName>(events: E[], handler: SSEEventMap[E]) => {
    events.forEach((event) => {
      sseHandlers.current[event] = handler;
    });
  };

  const unregisterSseHandler = <E extends SSEEvent['name']>(event: E) => {
    delete sseHandlers.current[event];
  };

  const unregisterSseEventsHandler = <E extends SSEEvent['name']>(events: E[]) => {
    events.forEach((event) => {
      delete sseHandlers.current[event];
    });
  };
  return {
    isLogin: !!me,
    me,
    loading,
    refetchMe,
    quickLogin,
    onlineSessions: othersOnlineSessions,
    logout,
    sendEmailVerificationCode,
    sendSmsVerificationCode,
    siteAdminLogin,
    loginWithUsername,
    setOnlineSessions: setOthersOnlineSessions,
    loginWithQuickCode,
    bindUsername,
    registerSseHandler,
    registerSseEventsHandler,
    unregisterSseHandler,
    unregisterSseEventsHandler,
    linkExternalUser,
    buildAuthURL,
  };
}
