import type { Locale } from 'basenext/i18n';
import { load } from 'cheerio';
import type { Metadata } from 'next';
import { getServerDictionary } from '@bika/contents/i18n/server';

/**
 * 预处理HTMl页面的title，加上 | Bika.ai
 * @param title
 */
export function postprocessPageMetadata(
  metadata: Pick<Metadata, 'title' | 'description' | 'keywords'>,
  locale: Locale,
  withBrandTitle: boolean,
): Metadata {
  const dict = getServerDictionary(locale);

  const newMeatadata: Metadata = {
    ...metadata,
  };

  const brand = dict.slogan.slogan_prd_m;
  const finalTitle: string = withBrandTitle ? `${metadata.title} | ${brand}` : (metadata.title as string) || brand;

  if (metadata.title) newMeatadata.title = finalTitle;

  if (!newMeatadata.openGraph) {
    newMeatadata.openGraph = {
      title: finalTitle,
      description: metadata.description as string,
      type: 'website',
      locale,
    };
  }

  if (newMeatadata.openGraph?.title) {
    newMeatadata.openGraph.title = finalTitle;
  }
  return newMeatadata;
}

// export function getLocaleByHeaders(headers: Headers): string | undefined {
//   // Negotiator expects plain object so we need to transform headers
//   const negotiatorHeaders: Record<string, string> = {};
//   headers.forEach((value: string, key: string) => {
//     negotiatorHeaders[key] = value;
//   });

//   const { locales } = i18n;

//   // Use negotiator and intl-localematcher to get best locale
//   const languages = new Negotiator({ headers: negotiatorHeaders }).languages(Array.from(locales));

//   const locale = matchLocale(languages);

//   return locale;
// }

interface TreeItem {
  name: string;
  href: string;
  level: 1 | 2 | 3 | 4;
}

/**
 * 从 Markdown 字符串中提取标题
 * @param {string} markdown - Markdown 格式的字符串
 * @returns {Object} - 包含标题信息的对象
 */
export function extractTitlesFromMarkdown(markdown: string) {
  const titles: { level: number; title: string }[] = [];
  // let currentLevel = 0;

  const lines = markdown.split('\n');
  for (const line of lines) {
    const match = line.match(/^(#+)\s+(.+)/);
    if (match) {
      const level = match[1].length;
      const title = match[2];
      titles.push({ level, title });
      // currentLevel = level;
    }
  }

  return titles;
}

/**
 * 从HTML里提取目录
 * @param html
 * @returns
 */
export function generateTocAndModifyHtml(html: string): { tree: TreeItem[]; modifiedHtml: string } {
  const $ = load(html, null, false);
  const tree: TreeItem[] = [];
  const headings = $('h1, h2, h3');

  headings.each((_, element) => {
    const tagName = element.tagName;
    const text = $(element).text();
    const href = Buffer.from(text).toString('base64');

    $(element).attr('id', Buffer.from(text).toString('base64'));
    // $(element).html(`<a href="${href}">${text}</a>`);
    const tocItem: TreeItem = {
      name: text,
      href,
      level: Math.min(4, Number.parseInt(tagName.slice(1), 10)) as 1 | 2 | 3 | 4,
    };

    tree.push(tocItem);
  });
  // 给里面所有的a加上target="_blank"
  $('a').attr('target', '_blank');

  $('img').each((_, element) => {
    const src = $(element).attr('src');
    $(element).attr('data-src', src);
    $(element).attr('src', null);
  });

  return { tree, modifiedHtml: $.root().html() as string };
}

export const updateViewIdInPathname = (pathname: string, newViewId: string) =>
  pathname.replace(/(\/node\/[^/]+\/)[^/]+/, `$1${newViewId}`);
