/**
 * 本脚本用于从 Pulse MCP API 获取可用的 MCP Server 列表，并将其转换为符合 ToolSDK 规范的 JSON 文件，以供 awesome-mcp-registry 使用。
 * 使用前，你需要配置以下环境变量：
 * - PULSE_MCP_DIR: 存储 Pulse MCP 临时数据的目录，默认为当前脚本所在目录下的 'pulse-mcp'。
 * - OUTPUT_DIR: 输出 JSON 文件的目录，需要指向 awesome-mcp-registry 的 packages/uncategorized 目录。
 * - GITHUB_API_TOKEN: 用于访问 GitHub API 的令牌，确保你有权限访问相关仓库。
 */

import * as fs from 'fs';
import * as path from 'path';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { PackageConfigSchema, type PackageConfig } from '@toolsdk.ai/orm/types';
import axios from 'axios';
import semver from 'semver';
import { ensureDirectoryExists, extractEnvWithPatternMatching, transformToDescribedObject } from './helper';

// 定义目录路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const pulseMcpDir = process.env.PULSE_MCP_DIR || join(__dirname, 'pulse-mcp');
const outputDir = process.env.OUTPUT_DIR || join(__dirname, 'packages', 'uncategorized');
const GITHUB_API_TOKEN = process.env.GITHUB_API_TOKEN;

if (!GITHUB_API_TOKEN) {
  throw new Error('GITHUB_API_TOKEN environment variable is not set. Please set it to your GitHub API token.');
}

function sanitizePackageName(packageName: string): string {
  // 替换常见特殊符号为中划线
  const replaced = packageName.replace(/[^a-zA-Z0-9\-_]+/g, '-');
  // 去除前后多余的中划线或其他非法字符
  const trimmed = replaced.replace(/^[-]+|[-]+$/g, '');
  return trimmed;
}

// 校验依赖项有效性
function checkDependencyValidity(dependencyData: any, versionRange: string): boolean {
  // 兼容 "latest" 的情况
  if (versionRange === 'latest') {
    return Object.keys(dependencyData.versions).length > 0;
  }

  // 使用 semver 检查是否有满足版本范围的有效版本
  const versions = Object.keys(dependencyData.versions);
  for (const version of versions) {
    if (semver.satisfies(version, versionRange)) {
      return true;
    }
  }
  return false;
}

interface GitHubRepoInfo {
  language: string;
  license: {
    key: string;
  } | null;
}

async function getRepositoryInfo(repoUrl: string): Promise<GitHubRepoInfo | null> {
  try {
    // 提取 GitHub 用户名和仓库名
    const match = repoUrl.match(/github.com\/([^/]+)\/([^/]+)/);
    if (!match) return null;

    const owner = match[1];
    const repo = match[2];

    // GitHub API 速率限制：https://docs.github.com/zh/rest/rate-limit/rate-limit
    const token = process.env.GITHUB_API_TOKEN || 'ghp_xxx';
    const response = await axios.get(`https://api.github.com/repos/${owner}/${repo}`, {
      headers: {
        'User-Agent': 'MyToolManager/1.0 (https://mytoolmanager.com)',
        'X-GitHub-Api-Version': '2022-11-28',
        Accept: 'application/vnd.github+json',
        Authorization: `Bearer ${token}`,
      },
    });

    return {
      language: response.data.language,
      license: response.data.license ? { key: response.data.license.key } : null,
    };
  } catch (error) {
    console.error('Error fetching repository info:', (error as Error).message);
    return null;
  }
}

interface PulseServer {
  name: string;
  url: string;
  short_description: string;
  source_code_url: string;
  package_name: string | null;
  package_registry: string | null;
  EXPERIMENTAL_ai_generated_description: string;
}

interface ApiResponse {
  servers: PulseServer[];
}

async function fetchAndSavePulseMcpData(): Promise<void> {
  try {
    const response = await axios.get<ApiResponse>('https://api.pulsemcp.com/v0beta/servers?count_per_page=5000', {
      headers: {
        'User-Agent': 'MyToolManager/1.0 (https://mytoolmanager.com)',
      },
    });
    console.log('Response:', response.status, response.statusText);
    if (response.status !== 200) {
      console.error('Error fetching data:', response.statusText);
      return;
    }

    ensureDirectoryExists(pulseMcpDir);

    // 写入响应数据到本地文件
    const outputPath = join(pulseMcpDir, 'pulse.json');
    fs.writeFileSync(outputPath, JSON.stringify(response.data, null, 2), 'utf-8');
    console.log(`Data saved to ${outputPath}`);
  } catch (error) {
    console.error('Error fetching data:', (error as Error).message);
  }
}

// 添加缓存对象
const dependencyCache: Record<string, boolean> = {};
const devDependencyCache: Record<string, boolean> = {};

// 判断 npm 包及其依赖是否有效
async function getNpmPackageInfo(packageName: string): Promise<{ isValid: boolean; env: Record<string, string> }> {
  const npmPackageInfo = {
    isValid: false,
    env: {},
  };

  try {
    // 检查主包是否存在
    const response = await axios.get(`https://registry.npmjs.org/${packageName}`, {
      headers: {
        'User-Agent': 'MyToolManager/1.0 (https://mytoolmanager.com)',
      },
    });

    // 检查主包是否被标记为 unpublished
    if (response.status !== 200 || !response.data?.['dist-tags']?.latest) {
      console.error(`Package marked as unpublished: ${packageName}`);
      npmPackageInfo.isValid = false;
      return npmPackageInfo;
    }
    console.log(`Valid package: https://registry.npmjs.org/${packageName}`);

    // 获取主包的最新版本信息
    const latestVersion = response.data['dist-tags'].latest;
    const versionData = response.data?.versions?.[latestVersion];
    if (!versionData) {
      console.error(`Invalid package: ${packageName} - No version data found`);
      npmPackageInfo.isValid = false;
      return npmPackageInfo;
    }

    // 检查依赖项
    const dependencies = versionData.dependencies || {};
    console.log('Checking dependencies...');
    for (const depName of Object.keys(dependencies)) {
      const depVersionRange = dependencies[depName]; // 获取依赖的版本范围

      // 查询缓存
      const cacheKey = `${depName}@${depVersionRange}`;
      if (dependencyCache[cacheKey] !== undefined) {
        console.log(`Using cached result for ${cacheKey}: ${dependencyCache[cacheKey]}`);
        if (!dependencyCache[cacheKey]) {
          console.error(`Dependency invalid or missing: ${depName} in package ${packageName}`);
          npmPackageInfo.isValid = false;
          return npmPackageInfo;
        }
        continue;
      }

      const depResponse = await axios.get(`https://registry.npmjs.org/${depName}`, {
        headers: {
          'User-Agent': 'MyToolManager/1.0 (https://mytoolmanager.com)',
        },
      });

      if (depResponse.status !== 200) {
        console.error(`Failed to fetch dependency info for ${depName}`);
        dependencyCache[cacheKey] = false; // 更新缓存为无效
        npmPackageInfo.isValid = false;
        return npmPackageInfo;
      }

      // 校验依赖项的有效性
      const isValid = checkDependencyValidity(depResponse.data, depVersionRange);
      dependencyCache[cacheKey] = isValid; // 更新缓存
      if (!isValid) {
        console.error(`Dependency invalid or missing: ${depName} in package ${packageName}`);
        npmPackageInfo.isValid = false;
        return npmPackageInfo;
      }
    }

    // 检查开发依赖项（如果有）
    const devDependencies = versionData.devDependencies || {};
    console.log('Checking devDependencies...');
    for (const depName of Object.keys(devDependencies)) {
      const depVersionRange = devDependencies[depName];

      // 查询缓存
      const cacheKey = `${depName}@${depVersionRange}`;
      if (devDependencyCache[cacheKey] !== undefined) {
        console.log(`Using cached result for ${cacheKey}: ${devDependencyCache[cacheKey]}`);
        if (!devDependencyCache[cacheKey]) {
          console.error(`DevDependency invalid or missing: ${depName} in package ${packageName}`);
          npmPackageInfo.isValid = false;
          return npmPackageInfo;
        }
        continue;
      }

      const depResponse = await axios.get(`https://registry.npmjs.org/${depName}`, {
        headers: {
          'User-Agent': 'MyToolManager/1.0 (https://mytoolmanager.com)',
        },
      });

      if (depResponse.status !== 200) {
        console.error(`Failed to fetch devDependency info for ${depName}`);
        devDependencyCache[cacheKey] = false; // 更新缓存为无效
        npmPackageInfo.isValid = false;
        return npmPackageInfo;
      }

      // 校验开发依赖项的有效性
      const isValid = checkDependencyValidity(depResponse.data, depVersionRange);
      devDependencyCache[cacheKey] = isValid; // 更新缓存
      if (!isValid) {
        console.error(`DevDependency invalid or missing: ${depName} in package ${packageName}`);
        npmPackageInfo.isValid = false;
        return npmPackageInfo;
      }
    }

    const readmeContent = response.data?.readme || '';
    const rawEnv = extractEnvWithPatternMatching(readmeContent);

    // 过滤并转换环境变量：移除null值和非字符串值
    const filteredEnv: Record<string, string> = {};
    for (const [key, value] of Object.entries(rawEnv)) {
      if (value !== null && typeof value === 'string') {
        filteredEnv[key] = value;
      }
    }

    if (Object.keys(filteredEnv).length > 0) {
      npmPackageInfo.env = filteredEnv;
      npmPackageInfo.isValid = true;
    }
    return npmPackageInfo;
  } catch (error) {
    console.error(`Error validating package ${packageName}:`, (error as Error).message);
    npmPackageInfo.isValid = false;
    return npmPackageInfo;
  }
}

// 抽离出处理单个 server 的函数
async function processServer(server: PulseServer): Promise<void> {
  const packageName = server.package_name ? server.package_name : server.name.toLowerCase().replace(/\s+/g, '-');

  // 获取仓库信息
  let runtime: string | null = null;
  let license = 'unknown';
  let env = {};

  if (server.package_registry === 'npm') {
    // 判断 npm 包是否有效
    const npmPackageInfo = await getNpmPackageInfo(packageName);
    if (!npmPackageInfo.isValid) {
      console.log(`Skipping invalid npm package: ${packageName}`);
      return;
    }
    runtime = 'node';
    if (Object.getOwnPropertyNames(npmPackageInfo.env).length !== 0) {
      env = transformToDescribedObject(npmPackageInfo.env);
    }
  } else if (server.package_registry === 'pypi') {
    runtime = 'python';
  } else if (server.source_code_url && server.source_code_url.includes('github.com')) {
    const repoInfo = await getRepositoryInfo(server.source_code_url);
    if (repoInfo) {
      // 根据仓库语言设置 runtime
      switch (repoInfo.language?.toLowerCase()) {
        case 'python':
          runtime = 'python';
          break;
        case 'java':
          runtime = 'java';
          break;
        case 'go':
          runtime = 'go';
          break;
        case 'typescript':
        case 'javascript':
          runtime = null;
          break;
        default:
          runtime = null;
      }

      // 设置 license
      license = repoInfo.license?.key || 'unknown';
    }
  }

  if (!runtime) {
    console.log(`Skipping record due to missing runtime: ${packageName}`);
    return;
  }

  const config: PackageConfig = {
    type: 'mcp-server',
    packageName,
    description: server.short_description,
    url: server.source_code_url,
    runtime: runtime as 'node' | 'python' | 'java' | 'go',
    license,
    env,
  };

  // 验证配置是否符合规范
  try {
    PackageConfigSchema.parse(config);
  } catch (error) {
    console.error(`Validation error for ${packageName}:`, (error as Error).message);
    return;
  }

  // 确保输出目录存在
  ensureDirectoryExists(outputDir);

  // 写入预设格式数据到输出目录
  const safePackageName = sanitizePackageName(packageName);
  const outputFilePath = join(outputDir, `${safePackageName}.json`);
  fs.writeFileSync(outputFilePath, JSON.stringify(config, null, 2), 'utf-8');
  console.log(`Processed and saved ${outputFilePath}`);
}

async function processAndWriteJsonFile(): Promise<void> {
  const files = fs.readdirSync(pulseMcpDir);
  console.log('Files:', files);

  for (const file of files) {
    console.log(`ProcessingFile: ${file}`);
    const filePath = join(pulseMcpDir, file);
    const stat = fs.statSync(filePath);

    if (stat.isFile() && file.endsWith('.json') && file !== 'pulse.json') {
      continue; // 跳过非pulse.json文件, 后续可在拆分文件时做拓展
    }

    // 读取并解析JSON文件
    const jsonData = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
    const servers = jsonData.servers;

    // 分批处理 servers 数组，每 20 个任务并发执行
    const batchSize = 20;
    for (let i = 0; i < servers.length; i += batchSize) {
      console.log(`Processing i(${i}) batch ${i / batchSize + 1}`);
      const batch = servers.slice(i, i + batchSize); // 获取当前批次的任务
      const promises = batch.map((server: PulseServer) => processServer(server)); // 创建当前批次的 Promise 列表
      await Promise.all(promises); // 并发执行当前批次的任务
    }
  }
}

// 主函数
async function main(): Promise<void> {
  // 执行时间2025年6月23日10点20分 "total_count": 4763,
  await fetchAndSavePulseMcpData();

  await processAndWriteJsonFile();
}

main();
