// 本地找到.git/_mcp-get 这个目录，请预先 <NAME_EMAIL>:michaellatman/mcp-get.git .git/_mcp-get
// 扫描这个目录里的子目录 packages 里的所有json文件，转成 MCP Config Schema
// 写入到我们的本地目录 packages/@toolsdk.ai/registry/packages/uncategorized/ 目录

import * as fs from 'fs';
import * as path from 'path';
import { PackageConfigSchema, type PackageConfig } from '@toolsdk.ai/orm/types';

// Define paths
const mcpGetDir = path.resolve('.data/_mcp-get');
const packagesDir = path.join(mcpGetDir, 'packages');
const outputDir = path.resolve('packages/@toolsdk.ai/registry/packages/uncategorized');

interface IMcpGet {
  name: string;
  description: string;
  vendor: string;
  sourceUrl: string;
  homepage: string;
  license: string;
  runtime: 'node' | 'python' | 'java';
  environmentVariables: Record<string, { required: boolean; description: string }>;
}

// Ensure the MCP directory exists
if (!fs.existsSync(mcpGetDir)) {
  console.error(
    `Directory not found: ${mcpGetDir}. Please clone the repository: <NAME_EMAIL>:michaellatman/mcp-get.git`,
  );
  process.exit(1);
}

// Ensure the output directory exists
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Function to transform JSON data to MCP Config Schema
function transformToMcpConfigSchema(data: IMcpGet): PackageConfig {
  // Placeholder for transformation logic
  // Modify this function to implement the actual schema transformation
  return PackageConfigSchema.parse({
    type: 'mcp-server', // Example type
    name: data.name,
    description: data.description,
    url: data.sourceUrl,
    license: data.license,
    runtime: data.runtime,
    env: data.environmentVariables,
  });
}

// Function to process JSON files
function processJsonFiles(dir: string) {
  const files = fs.readdirSync(dir);

  files.forEach((file) => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      // Recursively process subdirectories
      processJsonFiles(filePath);
    } else if (file.endsWith('package-list.json')) {
      // ignore
    } else if (file.endsWith('.json')) {
      // Read and transform JSON file
      console.log(`Processing ${filePath}`);
      const jsonData: IMcpGet = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
      const transformedData = transformToMcpConfigSchema(jsonData);

      // Write transformed data to the output directory
      const outputFilePath = path.join(outputDir, file.replace('--', '-'));
      fs.writeFileSync(outputFilePath, JSON.stringify(transformedData, null, 2), 'utf-8');
    }
  });
}

// Start processing
console.log(`Scanning directory: ${packagesDir}`);
processJsonFiles(packagesDir);
console.log('Processing complete.');
