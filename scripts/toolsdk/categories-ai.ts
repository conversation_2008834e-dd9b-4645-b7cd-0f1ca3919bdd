// 自动读取所有 uncategories 的 json 文件，然后呼叫 AI，为它定义一个 category 分类，再把文件移动过去
import { readdir, readFile, rename } from 'fs/promises';
import { join } from 'path';
import McpServerApiClient from '@toolsdk.ai/orm/api/mcp-server-api-client';
import { CategoryConfig } from '@toolsdk.ai/orm/types';
import _ from 'lodash';
import { AISO } from '@bika/domains/ai/server/ai-so';

const aiPromptTpl = `
你是一个分类专家，你的任务是为MCP Server json文件，进行分类。

我们有以下的分类：

<%= categories %>

现在，这个json 文件如下：

<%= json %>

请你输出这个 json 文件，应该归到以上哪个分类？（不包含 uncategorized)
仅输出分类的key字符串，不要其它多余内容，不包含引号:
`;

// const { textStream } = await AISO.streamByAISDK({ prompt: aiPrompt }, {});
// console.log('output AI:');
// for await (const chunk of textStream) {
//   process.stdout.write(chunk);
// }
const uncategorizedDir = 'packages/@toolsdk.ai/registry/packages/uncategorized';
const categorizedDir = 'packages/@toolsdk.ai/registry/packages';

async function processFiles() {
  try {
    const client = new McpServerApiClient();
    const categoriesJSON: CategoryConfig[] = await client.fetchCategoriesConfig();

    const files = await readdir(uncategorizedDir);
    for (const file of files) {
      if (file.endsWith('.json')) {
        const filePath = join(uncategorizedDir, file);
        const jsonContent = await readFile(filePath, 'utf-8');

        const finalPrompt = _.template(aiPromptTpl)({ json: jsonContent, categories: categoriesJSON }); // Add categories dynamically if needed

        console.log(finalPrompt);
        let categoryKey = ''; // Default to uncategorized
        const s = await AISO.dangerStreamYield({ prompt: finalPrompt }, {});
        for await (const chunk of s) {
          categoryKey += chunk.chunkContent;
        }
        categoryKey = categoryKey.trim();

        if (categoryKey) {
          const targetDir = join(categorizedDir, categoryKey);
          const targetFile = join(targetDir, file);
          console.log(`Moving from ${filePath} to ${targetFile}`);

          try {
            await rename(filePath, targetFile);
          } catch (error) {
            console.error(`Failed to move ${file}`, (error as Error).message);
            continue;
          }
        } else {
          console.error(`Failed to categorize ${file}`);
        }
      }
    }
  } catch (error) {
    console.error('Error processing files:', error);
  }
}

await processFiles();
