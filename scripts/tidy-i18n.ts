/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable guard-for-in */
/**
 * 整理i18n字符串
 *
 * 以en.ts为准，读出en.ts，然后读出其他语言的ts，对比en.ts，如果有缺失的字段，就在对应的ts文件中添加缺失字段
 *
 * 并使用prettier格式化文件，重新写入
 *
 * (这段脚本AI自动生成)
 */

import fs from 'fs';
import path from 'path';
import prettier from 'prettier';
import { getServerDictionary } from '@bika/contents/i18n/server';
import { i18n } from 'basenext/i18n';

// Deep merge objects, adding missing keys from source to target
// Track missing keys during merge
// Deep merge objects and arrays, adding missing keys from source to target
function deepMerge(target: any, source: any, path: string[] = []): { result: any; missingKeys: string[] } {
  const output = Array.isArray(source) ? [] : { ...target };
  const missingKeys: string[] = [];

  if (Array.isArray(source)) {
    // Handle arrays
    if (!Array.isArray(target)) {
      // If target is not an array but source is, consider entire array as missing
      output.push(...source);
      missingKeys.push(path.join('.'));
    } else {
      // Merge arrays element by element
      for (let i = 0; i < source.length; i++) {
        const currentPath = [...path, i.toString()];
        if (i >= target.length) {
          output[i] = source[i];
          missingKeys.push(currentPath.join('.'));
        } else if (source[i] instanceof Object) {
          const merged = deepMerge(target[i], source[i], currentPath);
          output[i] = merged.result;
          missingKeys.push(...merged.missingKeys);
        } else {
          output[i] = target[i];
        }
      }
    }
  } else {
    // Handle objects
    for (const key in source) {
      const currentPath = [...path, key];
      if (source[key] instanceof Object) {
        if (key in target) {
          const merged = deepMerge(target[key], source[key], currentPath);
          output[key] = merged.result;
          missingKeys.push(...merged.missingKeys);
        } else {
          output[key] = source[key];
          missingKeys.push(currentPath.join('.'));
        }
      } else if (!(key in target)) {
        output[key] = source[key];
        missingKeys.push(currentPath.join('.'));
      } else {
        output[key] = target[key];
      }
    }
  }

  return { result: output, missingKeys };
}

// ... existing code ...

async function tidyI18n() {
  const prettierConfig = await prettier.resolveConfig(`${process.cwd()}/.prettierrc`);
  const en = await getServerDictionary('en');

  for (const lang of i18n.locales) {
    let merged: any;
    if (lang === 'en') {
      // 英文，不检查，直接prettier格式化
      merged = en;
    } else {
      const dict = await getServerDictionary(lang);

      // Check for missing keys
      const { result, missingKeys } = deepMerge(dict, en);
      merged = result;

      if (missingKeys.length > 0) {
        console.error(`Missing translations in ${lang}:`);
        missingKeys.forEach((key) => console.error(`  - ${key}`));
      }
    }

    // 排序，对这个对象进行排序，按照key的字母顺序（忽略数组不排序）
    const sortObjectKeys = (obj: any): any => {
      // If not an object or is an array, return as is
      if (typeof obj !== 'object' || Array.isArray(obj)) {
        return obj;
      }

      // Get sorted keys
      const sortedKeys = Object.keys(obj).sort();

      // Create new object with sorted keys
      const sortedObj: any = {};
      for (const key of sortedKeys) {
        sortedObj[key] = sortObjectKeys(obj[key]);
      }

      return sortedObj;
    };

    merged = sortObjectKeys(merged);

    // Format and write back
    const filePath = path.resolve(__dirname, `../contents/i18n/dictionaries/${lang}.ts`);
    const fileContent = `const dict = ${JSON.stringify(merged, null, 2)};\nexport default dict;`;

    const formatted = await prettier.format(fileContent, {
      ...prettierConfig,
      parser: 'typescript',
    });

    console.log('Writing...', filePath);
    fs.writeFileSync(filePath, formatted);
  }
}

tidyI18n().catch(console.error);
