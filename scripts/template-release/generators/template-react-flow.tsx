import * as fs from 'fs';
import * as path from 'path';
import { i18n } from 'basenext/i18n';
import puppeteer, { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'puppeteer';
import { TemplateReactFlowStrategy } from '@bika/contents/config/server/ai/aigc-strategy-types';
import type { IGenerator } from './interface';
import { getTemplateRepo, getTemplatesList } from './utils';

// eslint-disable-next-line no-promise-executor-return
const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export class TemplateReactFlowGenerator implements IGenerator {
  private _sta: TemplateReactFlowStrategy;

  constructor(sta: TemplateReactFlowStrategy) {
    this._sta = sta;
  }

  async run(): Promise<{ success: boolean }> {
    const localTplsList = await getTemplatesList();
    // eslint-disable-next-line no-unreachable-loop
    const browser = await puppeteer.launch();
    const page = await browser.newPage();
    // 设置大小
    // await page.setViewport({
    //   width: 1080,
    //   height: 1080,
    // });
    for (const tpl of localTplsList) {
      console.log(` ---------- ${tpl.templateId} start ---------- `);
      console.log(`Puppeteer for image generation...: ${tpl.templateId}`);
      const templateRepo = await getTemplateRepo(tpl.templateId);
      for (const locale of i18n.locales) {
        if (templateRepo?.templateId) {
          const url = `http://localhost:3000/${locale}/template-architecture/${tpl.templateId}`;
          // const url = `https://dev.bika.ai/${locale}/template-architecture/${tpl.templateId}`;
          // 打印访问的url
          console.log(`Get image from url ${url}`);
          await page.goto(url);
          await page.waitForSelector('#graph-editor-x');
          // 给 body 增加 10px padding
          await page.addStyleTag({
            content: 'body { padding: 10px; }',
          });
          // 获取ID为 graph-editor-x 的元素
          const eleX = (await page.$('#graph-editor-x')) as ElementHandle<HTMLInputElement>;
          const eleY = (await page.$('#graph-editor-y')) as ElementHandle<HTMLInputElement>;
          // console.log(eleX, eleY);
          // 获取这个元素的值
          // const x = await page.evaluate((ele) => ele.value, eleX);
          // const y = await page.evaluate((ele) => ele.value, eleY);
          await page.setViewport({
            width: Number(await page.evaluate((ele) => ele.value, eleX)),
            height: Number(await page.evaluate((ele) => ele.value, eleY)),
          });
          const imageBuffer = await page.screenshot({ omitBackground: true });
          // /assets/template-photo/{模板ID}/architecture-all-{语言}.png
          const dir = path.join(__dirname, `../../../apps/web/public/assets/template-photo/${tpl.templateId}`);
          // console.log(url, dir);
          if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, {
              recursive: true,
            });
          }
          console.log(`Write image to ${dir}/architecture-all-${locale}.png`);
          fs.writeFileSync(`${dir}/architecture-all-${locale}.png`, new Uint8Array(imageBuffer));
        }
      }
      console.log(` ---------- ${tpl.templateId} done ---------- `);
    }
    await browser.close();

    return {
      success: true,
    };
  }
}
