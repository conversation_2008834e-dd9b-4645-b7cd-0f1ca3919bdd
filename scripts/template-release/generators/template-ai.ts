/* eslint-disable no-continue */
/* eslint-disable no-console */
import assert from 'assert';
import * as fs from 'fs';
import { mkdir } from 'fs/promises';
import path from 'path';
import { Locale } from 'basenext/i18n';
import _ from 'lodash';
import { generate } from 'random-words';
import type { TemplateAIStrategy } from '@bika/contents/config/server/ai/aigc-strategy-types';
import { AISO } from '@bika/domains/ai/server/ai-so';
import { LocalContentLoader } from '@bika/server-orm/content/local-content-loader';
import type { IGenerator, RunMode } from './interface';
import { getPromptTemplatesVars, getTemplateRepo, loopTemplates, urlStr } from './utils';

export type TemplateAIGeneratorStrategy = TemplateAIStrategy;

export class TemplateAIGenerator implements IGenerator {
  _strategy: TemplateAIGeneratorStrategy;

  constructor(strategy: TemplateAIGeneratorStrategy) {
    this._strategy = strategy;
  }

  /**
   * 获取最终的write path，根据static和dynamic不同，目的地不同
   *
   * @param locale
   * @param templateId
   * @param loopKeyword
   * @returns
   */
  getWritePath(
    locale: Locale,
    templateId: string,
    loopKeyword: string,
    loopPersona: string,
    loopUseCase: string,
  ): string {
    // loop str可能会有中文，转拼音
    const pinyinLoopStr = urlStr(loopKeyword, locale);
    const loopStrFinal = pinyinLoopStr.length > 0 ? `-${pinyinLoopStr}` : '';

    const pinyinLoopPersona = urlStr(loopPersona, locale);
    const loopPersonaFinal = pinyinLoopPersona.length > 0 ? `-${pinyinLoopPersona}` : '';

    const pinyinLoopUseCase = urlStr(loopUseCase, locale);
    const loopUseCaseFinal = pinyinLoopUseCase.length > 0 ? `-${pinyinLoopUseCase}` : '';

    const contentDir = `${__dirname}/../../../bika-content`;
    if (!fs.existsSync(contentDir)) {
      console.error('找不到内容目录bika-content，请执行命令 make template-aigc-clone');
      process.exit(1);
    }

    if (this._strategy.dynamic === true) {
      // const date = ${dayjs().format('YYYY-MM-DD-HH-mm-ss')}
      const dynamicWords = generate({ exactly: 3, join: '-' });

      const writeFilePath = `${contentDir}/blog-aigc/${locale}/template/${templateId}/${templateId.replace('/', '-')}${loopPersonaFinal}${loopUseCaseFinal}${loopStrFinal}-${this._strategy.output}-${dynamicWords}.mdx`;
      return writeFilePath;
    }

    // const writeFilePath = `${__dirname}/../../../contents/docs/blog/${locale}/template/${templateId}/${templateId.replace('/', '-')}${loopStrFinal}-${this._strategy.output}.mdx`;
    const writeFilePath = `${contentDir}/blog-aigc/${locale}/template/${templateId}/${templateId.replace('/', '-')}${loopPersonaFinal}${loopUseCaseFinal}${loopStrFinal}-${this._strategy.output}.mdx`;
    return writeFilePath;
  }

  async run(runMode?: RunMode): Promise<{ success: boolean }> {
    const localTplsList = await LocalContentLoader.template.fsTemplatesList();

    const promptTpl = fs.readFileSync(`${__dirname}/../aigc-templates/${this._strategy.promptFile}`, 'utf-8');

    let tplsList: {
      templateId: string;
    }[] = localTplsList;

    const { includeFilters, excludeFilters } = this._strategy;

    for (const filter of includeFilters || []) {
      tplsList = tplsList.filter((tpl) => tpl.templateId.includes(filter));
    }
    for (const filter of excludeFilters || []) {
      tplsList = tplsList.filter((tpl) => !tpl.templateId.includes(filter));
    }

    // debug 模式，取一个模板
    if (runMode === 'DEBUG') {
      // eslint-disable-next-line no-unreachable-loop
      for (const tpl of _.shuffle(tplsList)) {
        // 随机只取1个，确保其score足够
        const templateRepo = await getTemplateRepo(tpl.templateId);
        if (templateRepo) {
          tplsList = [tpl];
          break;
        }
      }
    }
    assert(tplsList.length > 0);
    console.log('开始AIGC的模板数量: ', tplsList.length);

    // 循环模板
    const promises = await loopTemplates(tplsList, this._strategy, async (props) => {
      const promptVars = getPromptTemplatesVars(
        props.templateRepo,
        props.locale,
        props.loopKeyword,
        props.loopPersona,
        props.loopUseCase,
      );
      // 最终写入的文件
      const writeFilePath = this.getWritePath(
        props.locale,
        props.templateId,
        props.loopKeyword,
        props.loopPersona,
        props.loopUseCase,
      ); // `${__dirname}/../../../contents/docs/blog/${locale}/template/${templateId.replace('/', '-')}-${this._strategy.output}.mdx`;

      if (fs.existsSync(writeFilePath)) {
        console.log(
          `跳过模板${props.templateId}的生成，strategy prompfile: ${this._strategy.promptFile}, 文件已存在: ${writeFilePath}`,
        );
        return;
      }

      console.log(
        `开始AI生成，模板ID: ${props.templateId} locale: ${props.locale}, prompt模板: ${this._strategy.promptFile}, 写入文件: ${writeFilePath}`,
      );

      const finalPrompt = _.template(promptTpl)(promptVars);

      console.log(`============= Prompt: ${finalPrompt}`);

      console.log(`============= 使用模型${this._strategy.model} AI生成: `);

      const aistream = await AISO.dangerStreamYield(
        { prompt: finalPrompt },
        {
          model: this._strategy.model,
        },
      );

      let aimsg = '';
      for await (const chunk of aistream) {
        aimsg = chunk.content;
      }

      console.log('\n\n');
      console.log(`============= ${props.templateId} 生成完毕！, prompt模板: ${this._strategy.promptFile} `);
      // 开始写入文件

      const dirPath = path.dirname(writeFilePath);
      await mkdir(dirPath, { recursive: true });

      await fs.writeFileSync(writeFilePath, aimsg);

      console.log('\n\n');
    });

    if (runMode === 'DEBUG') {
      for (const p of promises) {
        await p;
      }
    } else {
      await Promise.all(promises);
    }

    return {
      success: false,
    };
  }
}
