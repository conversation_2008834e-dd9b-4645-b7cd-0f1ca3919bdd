import { type ExtendLocale, iStringParse, type Locale } from 'basenext/i18n';
import { i18n } from 'basenext/i18n';
import { pinyin } from 'pinyin-pro';
import * as wanakana from 'wanakana';
import type { TemplateAIStrategy, TemplatePhotoStrategy } from '@bika/contents/config/server/ai/aigc-strategy-types';
import { LocalContentLoader } from '@bika/server-orm';
import type { TemplateRepo } from '@bika/types/template/bo';

interface PromptTemplateVars {
  templateId: string;
  name: string;
  description: string;
  useCases: string;
  personas: string;
  README: string;
  locale: string;
  // JSON string
  resources: string;

  // 单个关键词
  keyword: string;
  // 单个useCase
  useCase: string;
  // 单个persona
  persona: string;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function getPromptTemplatesVars(
  templateRepo: TemplateRepo,
  locale: Locale,
  keyword: string,
  persona: string,
  useCase: string,
): PromptTemplateVars {
  return {
    templateId: templateRepo.templateId,
    name: iStringParse(templateRepo.name, locale),
    description: iStringParse(templateRepo.description, locale),
    useCases: iStringParse(templateRepo.useCases, locale),
    personas: iStringParse(templateRepo.personas, locale),
    README: iStringParse(templateRepo.readme, locale),
    locale,
    resources: JSON.stringify(templateRepo.current.data.resources),
    // loop
    keyword,
    persona,
    useCase,
  };
}

export async function getTemplatesList() {
  return LocalContentLoader.template.fsTemplatesList();
}

export async function getTemplateRepo(templateId: string) {
  const tplRepo = await LocalContentLoader.template.importLocalTemplateRepo(templateId);

  const { score } = LocalContentLoader.template.checkTemplateRepoScore(tplRepo);
  if (score < 30) {
    console.error(`模板 ${templateId} 分数太低: ${score}，不进行处理`);
    return null;
  }
  return tplRepo;
}

export interface ILooperProps {
  strategy: TemplateAIStrategy | TemplatePhotoStrategy;
  templateId: string;
  templateRepo: TemplateRepo;
  locale: Locale;
  loopUseCase: string;
  loopPersona: string;
  loopKeyword: string;
}

/**
 * 将任意字符串，变成url友好的字符串，中文变拼音，空格变-，大写变小写
 *
 * @param str
 * @returns
 */
export function urlStr(str: string, locale: ExtendLocale) {
  if (!str || str.length === 0) return '';

  if (
    locale === 'ja' ||
    locale === 'fr' ||
    locale === 'de' ||
    locale === 'es' ||
    locale === 'ru' ||
    locale === 'it' ||
    locale === 'vi'
  ) {
    const romaji = wanakana.toRomaji(str);
    let pinyinLoopStr = pinyin(romaji, { toneType: 'none', type: 'array' }).join('');
    // 修改内容：将拼音中的“ü”替换为“u”
    pinyinLoopStr = pinyinLoopStr.replace(/ü/g, 'u');
    const rUrlStr = pinyinLoopStr.replace(/\s+/g, '-').toLowerCase();
    return rUrlStr;
  }
  if (locale === 'zh-CN' || locale === 'zh-TW') {
    let pinyinLoopStr = pinyin(str, { toneType: 'none', type: 'array' }).join('');
    // 修改内容：将拼音中的“ü”替换为“u”
    pinyinLoopStr = pinyinLoopStr.replace(/ü/g, 'u');
    const rUrlStr = pinyinLoopStr.replace(/\s+/g, '-').toLowerCase();
    return rUrlStr;
  }
  // 去掉空格
  let rUrlStr = str.replace(/\s+/g, '-').toLowerCase();

  // 去掉&等等等的特殊字符，确保是一个SEO友好的URL
  rUrlStr = rUrlStr.replace(/[^a-zA-Z0-9-]/g, '');

  return rUrlStr;

  //
}
export async function loopTemplates(
  tplsList: { templateId: string }[],
  strategy: TemplateAIStrategy,

  cb: (_props: ILooperProps) => Promise<void>,
): Promise<Promise<void>[]> {
  const promises: Promise<void>[] = [];
  for (const { templateId } of tplsList) {
    const templateRepo = await getTemplateRepo(templateId);
    if (!templateRepo) continue;

    // loop locales
    for (const locale of strategy.locales || i18n.locales) {
      // 循环数组，根据策略，决定需要循环多少次，TEMPLATE模式循环一次、PERSONA模式循环4次、USE_CASE模式循环24次
      const loopKeywords = [];
      const loopPersonas = [];
      const loopUseCases = [];

      if (strategy.loopUseCases === true) {
        if (templateRepo.useCases) {
          const useCases = iStringParse(templateRepo.useCases, locale as Locale)
            .split(',')
            .map((v: string) => v.trim());
          loopUseCases.push(...useCases);
        } else {
          // 默认使用英文版本的 useCases
          const defaultUseCases = iStringParse(templateRepo.useCases, 'en')
            .split(',')
            .map((v: string) => v.trim());
          loopUseCases.push(...defaultUseCases);
        }
      } else {
        // 如果不遍历 useCases，直接推入一个空字符串
        loopUseCases.push('');
      }

      if (strategy.loopPersonas === true) {
        if (templateRepo.personas) {
          const personas = iStringParse(templateRepo.personas, locale as Locale)
            .split(',')
            .map((v: string) => v.trim());
          loopPersonas.push(...personas);
        } else {
          // 使用英文版 personas 作为默认值
          const defaultPersonas = iStringParse(templateRepo.personas, 'en')
            .split(',')
            .map((v: string) => v.trim());
          loopPersonas.push(...defaultPersonas);
        }
      } else {
        // 如果不遍历 persona，直接推入一个空字符串
        loopPersonas.push('');
      }

      if (strategy.loopKeywords) {
        loopKeywords.push(...strategy.loopKeywords);
      } else {
        loopKeywords.push(''); // 没有
      }

      // 调试输出
      console.log(`Locale: ${locale}`);
      console.log(`Loop Keywords: ${loopKeywords}`);
      console.log(`Loop Use Cases: ${loopUseCases}`);
      console.log(`Final Loop Personas: ${loopPersonas}`);

      // 开始循环
      for (const loopKeyword of loopKeywords) {
        // promptVars.keyword = loopKeyword;
        for (const loopPersona of loopPersonas) {
          // promptVars.persona = loopPersona;
          for (const loopUseCase of loopUseCases) {
            console.log(`Generating with: ${loopKeyword}, ${loopPersona}, ${loopUseCase}`); // 调试输出
            const promise = cb({
              strategy,
              templateId,
              templateRepo,
              locale: locale as Locale,
              loopUseCase,
              loopPersona,
              loopKeyword,
            });
            promises.push(promise);
          }
        }
      }
    }
  }
  return promises;
}
