import * as fs from 'fs';
import assert from 'node:assert';
import { input, confirm } from '@inquirer/prompts';
import { i18n } from 'basenext/i18n';
import _ from 'lodash';
import { models } from './config';

export function getTemplateDirPath(theTemplateId: string) {
  return `${__dirname}/../../../contents/templates/${theTemplateId}`;
}

export function getTemplateJSON(theTemplateId: string) {
  const dirPath = getTemplateDirPath(theTemplateId);
  const tsFilePath = `${dirPath}/template.ts`;
  const jsonFilePath = `${dirPath}/template.json`;
  if (fs.existsSync(tsFilePath)) {
    return fs.readFileSync(tsFilePath, 'utf-8');
  }
  if (fs.existsSync(jsonFilePath)) {
    return fs.readFileSync(jsonFilePath, 'utf-8');
  }
  throw new Error(`出错啦，${theTemplateId}模板既没有.ts文件，也没有.json文件?跳过这个执行失败`);
}

export function checkTemplateDirExist(theTemplateId: string) {
  const dirPath = getTemplateDirPath(theTemplateId);
  if (!fs.existsSync(dirPath)) {
    console.log(`抱歉，找不到这个ID的模板，没办法生成...你可以去试试AI生成模板：${dirPath}`);
    return false;
  }
  return true;
}
export async function commandTemplateReadme() {
  const theTemplateId = await input({ message: '想要生成README的模板ID是：' });
  assert(checkTemplateDirExist(theTemplateId), '找不到这个ID的模板，没办法生成...你可以去试试AI生成模板');

  const dirPath = getTemplateDirPath(theTemplateId);

  let firstReadmeContent = '';

  for (let i = 0; i < i18n.locales.length; i++) {
    const locale = i18n.locales[i];
    const readmePath = `${dirPath}/README.${locale}.md`;

    if (fs.existsSync(readmePath)) {
      console.log(`README文件已经存在，你是否要覆盖它？ -- ${readmePath}，`);
      const override = await confirm({ message: '是否覆盖？' });
      if (!override) {
        if (i === 0) {
          firstReadmeContent = fs.readFileSync(readmePath, 'utf-8');
        }
        console.log(`OK, 跳过...${readmePath}`);
        continue;
      }
    }

    let finalPrompt;

    if (i === 0) {
      const promptTpl = fs.readFileSync(`${__dirname}/../aigc-templates/template-readme.tpl.md`, 'utf-8');
      const code = getTemplateJSON(theTemplateId);

      finalPrompt = _.template(promptTpl)({
        locale,
        code,
      });
    } else {
      finalPrompt = `将下方 README 文档内容进行翻译，目标语言是 “${locale}”:\n\n${firstReadmeContent}`;
    }
    const model = models.default;

    console.log(`Prompt: \n${finalPrompt}, \n  使用的AI模型: ${model} \n开始写作： ${readmePath}`);

    const AISO = await import('@bika/domains/ai/server/ai-so').then((mod) => mod.AISO);
    const aistream = await AISO.dangerStreamYield({ prompt: finalPrompt }, { model });

    let content = '';
    for await (const chunk of aistream) {
      content = chunk.content;
    }

    if (i === 0) {
      firstReadmeContent = content;
    }

    const mkdir = await import('node:fs/promises').then((mod) => mod.mkdir);
    await mkdir(dirPath, { recursive: true });

    console.log('文件写入', readmePath);
    fs.writeFileSync(readmePath, content);
  }
}
