/**
 *
 * Template Release，这个脚本通常由CI执行，执行自动判断开发环境的template文件，生成对应的template release文件
 *
 */

import assert from 'node:assert';
import fs from 'node:fs';
import path from 'node:path';
import { i18n, iString, iStringParse, type Locale } from 'basenext/i18n';
import _ from 'lodash';
import { zodToJsonSchema } from 'zod-to-json-schema';
import { LocalContentLoader } from '@bika/server-orm';
import { getLocalTemplatePath } from '@bika/server-orm/content';
import { Automation } from '@bika/types/automation/bo';
import type { Database } from '@bika/types/database/bo';
import { CustomTemplate, CustomTemplateSchema } from '@bika/types/template/bo';

const jsonSchemaUrl = 'https://dev.bika.ai/api/schema/custom-template.json';
const { templateFullDirs, templateDirs } = LocalContentLoader.template.collectLocalTemplateDirs();
const templateIds: string[] = [];
for (let i = 0; i < templateDirs.length; i += 1) {
  const templateFullDir = templateFullDirs[i];
  const templateName = templateDirs[i];
  const templateTS = path.join(templateFullDir, 'template.ts');
  const templateReleaseFolder = path.join(templateFullDir, 'release');
  // console.log(templateFullDir, templateName, templateTS, templateReleaseFolder);
  // 首先判断release文件夹在不在？没有则创建
  if (!fs.existsSync(templateReleaseFolder)) {
    fs.mkdirSync(templateReleaseFolder);
  }
  // 拿到当前的develop模板（即ts结尾文件）
  const template = (await import(`@bika/contents/templates/${templateName}/template`)).default;
  assert(template, 'template must be exist');

  // validate template
  const validate = () => {
    // unique of template id in the global
    if (templateIds.includes(template.templateId)) {
      throw new Error(`templateId: '${template.templateId}' is repeated`);
    }
    templateIds.push(template.templateId);

    // check resource
    for (const resource of template.resources) {
      if (!resource.templateId) {
        console.error(
          `template: '${template.templateId}' ${resource.resourceType} resource has no templateId, please fix it asap`,
        );
      }
      // check database views and fields
      if (resource.resourceType === 'DATABASE') {
        const database = resource as Database;
        if (database.views) {
          for (const view of database.views) {
            if (!view.templateId) {
              console.error(
                `template: '${template.templateId}' DATABASE's ${view.type} view has no templateId, please fix it asap`,
              );
            }
          }
          if (database.fields) {
            for (const field of database.fields) {
              if (!field.templateId) {
                console.error(
                  `template: '${template.templateId}' DATABASE's ${field.type} field has no templateId, please fix it asap`,
                );
              }
            }
          }
        }
      }
      // check automation action and trigger
      if (resource.resourceType === 'AUTOMATION') {
        const automation = resource as Automation;
        for (const action of automation.actions) {
          if (!action.templateId) {
            console.error(
              `template: '${template.templateId}' AUTOMATION's action has no templateId, please fix it asap`,
            );
          }
        }
        for (const trigger of automation.triggers) {
          if (!trigger.templateId) {
            console.error(
              `template: '${template.templateId}' AUTOMATION's trigger has no templateId, please fix it asap`,
            );
          }
        }
      }
    }
  };

  validate();

  // 顺道Zod Schema校验一下
  const p = CustomTemplateSchema.safeParse(template);
  if (!p.success) {
    throw new Error(`模板${templateName}校验失败，请检查：${templateTS}, ${p.error}`);
  }

  // 拿到版本号，判断release在不在？没有就创建一个，模板发版了！
  const developVersion = template.version;
  const releaseVersionFolder = path.join(templateReleaseFolder, developVersion);
  const releaseVersionJSON = path.join(releaseVersionFolder, 'template.json');
  const releaseNotePath = path.join(releaseVersionFolder, 'release-notes.md');
  const changeLogPath = path.join(releaseVersionFolder, 'change-log.md');

  // console.log(releaseVersionFolder);
  if (!fs.existsSync(releaseVersionFolder)) {
    // 不存在，执行发版操作，即保存一个json文件咯
    fs.mkdirSync(releaseVersionFolder);

    console.log('[TEMPLATE RELEASE]发现新模板release：', templateName, developVersion);

    fs.writeFileSync(releaseVersionJSON, JSON.stringify(template, null, 2));
    fs.writeFileSync(changeLogPath, 'TODO: AUTO GEN');

    let releaseNotesTxt = '';

    const releaseNoteName: iString = {
      en: `Release Notes`,
      'zh-CN': `更新日志`,
      'zh-TW': `更新日誌`,
      ja: `リリースノート`,
    };

    const genLangReleaseNote = (lang: Locale) => `
# ${iStringParse(template.name, lang)} ${template.version} ${iStringParse(releaseNoteName, lang)}

${iStringParse(template.description, lang)}

## What's New

- TODO
- XXXXX
- YYYYY
`;

    if (typeof template.name === 'object') {
      for (const locale of i18n.locales) {
        if (template.name[locale] !== undefined) {
          releaseNotesTxt += genLangReleaseNote(locale);
        }
      }
    } else {
      releaseNotesTxt += genLangReleaseNote('en');
    }

    fs.writeFileSync(releaseNotePath, releaseNotesTxt);
  } else {
    // 哦，已经存在了？加载template.json文件，比较下是否内容一致
    if (!fs.existsSync(releaseVersionJSON)) {
      throw new Error(`找不到文件，请检查：${releaseVersionJSON}`);
    }
    if (!fs.existsSync(changeLogPath)) {
      fs.writeFileSync(changeLogPath, 'TODO: AUTO GEN');
    }

    const jsonStr = fs.readFileSync(releaseVersionJSON, 'utf8');
    const jsonObj = JSON.parse(jsonStr);
    const localTemplate = jsonObj as CustomTemplate;
    // Zod Schema也校验一下，这里能判断出，旧模板是否符合新的Schema向上兼容，新模板是否向下兼容
    const { success: releaseLoadSuccess } = CustomTemplateSchema.safeParse(localTemplate);
    if (!releaseLoadSuccess) {
      throw new Error(`老release模板校验失败，请检查：${releaseVersionJSON}`);
    }

    // 假设，develop版本号0.1.0，release版本号0.1.0，，但TMD内容不同？报错！！
    if (!_.isEqual(_.omit(template, '$schema'), _.omit(localTemplate, '$schema'))) {
      const diff = _.difference(template as any, localTemplate as any);
      throw new Error(
        `发现模板当前开发模板：${templateName}:${template.version}，与同版本号的release内容不一致，估计是你改了ts模板，但没升级版本号，请检查: ${templateTS}，差异地方： ${diff}`,
      );
    }

    // 一样? 不管，继续
    // PASS
    console.log(`[TEMPLATE RELEASE] PASS: ${templateName}`);
  }
  // 当前的template.ts，也生成一份放到release目录，方便某些地方读取JSON

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  template.$schema = jsonSchemaUrl;

  const currentVersionJSON = path.join(templateReleaseFolder, 'current.json');
  if (fs.existsSync(currentVersionJSON)) {
    fs.unlinkSync(currentVersionJSON);
  }
  // const tsFile = path.join(templateFullDir, 'template.ts');
  // if (fs.existsSync(tsFile)) {
  //   fs.unlinkSync(tsFile);
  // }

  const currentJSONFile = path.join(templateFullDir, 'template.json');
  console.log(`写入JSON文件备用： ${currentJSONFile}`);
  // fs.writeFileSync(currentVersionJSON, JSON.stringify(writeJSON, null, 2));
  fs.writeFileSync(currentJSONFile, JSON.stringify(template, null, 2));
}

const customTemplateSchema = zodToJsonSchema(CustomTemplateSchema);
console.log('写入custom-template的schema.json备用');
fs.writeFileSync(path.join(getLocalTemplatePath()!, 'schema.json'), JSON.stringify(customTemplateSchema, null, 2));
