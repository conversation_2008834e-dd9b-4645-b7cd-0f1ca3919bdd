#!/usr/bin/env bun

/**
 * 检查，哪些多语言是不对齐的
 *
 * 1. 读取en目录下，整理出所有网址路径，作为基准语言
 * 2. 开始读其它语言，比较不同的路径，然后罗列出来
 *
 * 相关内容：
 * - pages
 * - help
 * - blog
 * - i18n
 */

import { readdir } from 'node:fs/promises';
import path from 'node:path';
import { i18n } from 'basenext/i18n';
import _ from 'lodash';

function removeExtension(fileName: string) {
  const extension = path.extname(fileName);
  const baseName = path.basename(fileName, extension);
  return baseName;
}

function filterFiles(files: string[]) {
  return files
    .filter((f) => {
      const underscoreFile = f.startsWith('_');
      return !underscoreFile;
    })
    .map((f) => {
      const filteredPath = removeExtension(f);

      return filteredPath;
    });
}
async function readLangFolder(lang: string) {
  let baseFiles = await readdir(`${__dirname}/../contents/pages/${lang}`, { recursive: true });
  baseFiles = filterFiles(baseFiles);
  return baseFiles;
}

async function main() {
  const baseLang = 'en';
  const otherLangs = _.without(i18n.locales, baseLang);
  let retErr = false;

  const baseLangFiles = await readLangFolder(baseLang);

  for (const otherLang of otherLangs) {
    const langFiles = await readLangFolder(otherLang);
    const diffs = _.difference(baseLangFiles, langFiles);
    if (diffs.length > 0) {
      console.error(`[${otherLang}] has missing pages:`, diffs);
      retErr = true;
    }
  }

  if (retErr) {
    process.exit(1);
  }
}

await main();
