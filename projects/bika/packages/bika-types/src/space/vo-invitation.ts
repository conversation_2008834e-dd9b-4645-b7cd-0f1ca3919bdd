import { AvatarLogoSchema } from 'basenext/avatar';
import { z } from 'zod';
import { InvitationStatusSchema, InvitationTypeSchema } from './bo-invitation';
import { SpaceVOSchema } from './vo-space';
import { RenderOptionSchema } from '../system/render-option';
import { MemberVOSchema } from '../unit/vo-member';
import { RoleVOSchema } from '../unit/vo-role';
import { TeamVOSchema } from '../unit/vo-team';

const SpaceLinkInvitationVOSchema = z.object({
  token: z.string(),
  type: InvitationTypeSchema,
  member: MemberVOSchema,
  team: TeamVOSchema,
  roles: z.array(RoleVOSchema).optional(),
});
export type SpaceLinkInvitationVO = z.infer<typeof SpaceLinkInvitationVOSchema>;

export const SpaceLinkInvitationDetailVOSchema = z.object({
  spaceId: z.string(),
  spaceName: z.string(),
  spaceLogo: AvatarLogoSchema,
  inviterName: z.string(),
  token: z.string(),
});
export type SpaceLinkInvitationDetailVO = z.infer<typeof SpaceLinkInvitationDetailVOSchema>;

export const SpaceEmailInvitationVOSchema = z.object({
  id: z.string(),
  email: z.string(),
  type: InvitationTypeSchema,
  memberName: z.string().optional(),
  inviter: MemberVOSchema,
  space: SpaceVOSchema,
  status: InvitationStatusSchema,
  createdAt: z.string(),
});
export type SpaceEmailInvitationVO = z.infer<typeof SpaceEmailInvitationVOSchema>;

export const SpaceEmailInvitationDetailVOSchema = z.object({
  spaceId: z.string(),
  spaceName: z.string(),
  spaceLogo: AvatarLogoSchema,
  inviterName: z.string(),
  inviteId: z.string(),
  invitedEmail: z.string(),
  inviteType: InvitationTypeSchema,
});
export type SpaceEmailInvitationDetailVO = z.infer<typeof SpaceEmailInvitationDetailVOSchema>;

export const InvitationRenderOptsSchema = RenderOptionSchema.extend({});

export type InvitationRenderOpts = z.infer<typeof InvitationRenderOptsSchema>;
