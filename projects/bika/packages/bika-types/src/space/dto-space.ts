import { PaginationSchema } from 'basenext/pagination';
import { z } from 'zod';
import { InvitationTypeSchema } from './bo-invitation';
import { SpaceUpdateInfoSchema } from './vo-space';

export const SpaceListFilterSchema = z.object({
  id: z.string().optional(),
  name: z.string().optional(),
  admin: z.boolean().optional().describe('Permission'),
});

export type SpaceListFilter = z.infer<typeof SpaceListFilterSchema>;

const SpaceListBaseSchema = SpaceListFilterSchema.extend({
  sort: z
    .string()
    .refine((value) => ['name', 'createdAt'].includes(value), { message: 'Invalid sort field' })
    .default('createdAt'),
  direction: z
    .string()
    .optional()
    .transform((value) => (value === 'asc' ? value : 'desc')),
});

export const SpaceListSchema = SpaceListBaseSchema.merge(PaginationSchema);

export type SpaceListReq = z.infer<typeof SpaceListSchema>;

export const SpaceInfoSchema = z.object({
  identify: z.string().describe('Space identifier, ID or slug'),
});

export type SpaceInfoReq = z.infer<typeof SpaceInfoSchema>;

export const SpaceCreateSchema = z.object({
  name: z.string().trim().min(1),
});

export type SpaceCreateReq = z.infer<typeof SpaceCreateSchema>;

export const SpaceUpdateSchema = z.object({
  id: z.string().describe('Space ID'),
  data: SpaceUpdateInfoSchema.describe('Space settings data'),
});

export type SpaceUpdateReq = z.infer<typeof SpaceUpdateSchema>;

export const SpaceInstallTemplateSchema = z.object({
  spaceId: z.string().describe('Space ID'),
  templateId: z.string().describe('Template ID'),
  parentId: z.string().optional().describe('Parent node ID'),
});

export type SpaceInstallTemplateReq = z.infer<typeof SpaceInstallTemplateSchema>;

export const SpaceInvitationListSchema = z.object({
  spaceId: z.string().describe('Space ID'),
});

const SpaceAuditLogListBaseSchema = z.object({
  spaceId: z.string().describe('Space ID'),
  ip: z.string().optional().describe('IP address'),
  action: z.string().optional().describe('Action'),
  sort: z
    .string()
    .refine((value) => ['name', 'createdAt'].includes(value), { message: 'Invalid sort field' })
    .default('createdAt'),
  direction: z
    .string()
    .optional()
    .transform((value) => (value === 'asc' ? value : 'desc')),
});

export const SpaceAuditLogListSchema = SpaceAuditLogListBaseSchema.merge(PaginationSchema);
export type SpaceAuditLogListReq = z.infer<typeof SpaceAuditLogListSchema>;

export type SpaceInvitationListReq = z.infer<typeof SpaceInvitationListSchema>;

export const SpaceInvitationInfoSchema = z.object({
  inviteToken: z.string().describe('Invitation Token'),
});

export type SpaceInvitationInfoReq = z.infer<typeof SpaceInvitationInfoSchema>;

export const SpaceInvitationCreateSchema = z.object({
  spaceId: z.string().describe('Space ID'),
  teamId: z.string().describe('Team ID'),
  roleIds: z.array(z.string()).max(10).optional().describe('Role ID list'),
  type: InvitationTypeSchema.optional().describe('Invitation member type'),
});

export type SpaceInvitationCreateReq = z.infer<typeof SpaceInvitationCreateSchema>;

export const SpaceInvitationRefreshSchema = z.object({
  spaceId: z.string().describe('Space ID'),
  inviteToken: z.string().describe('Invitation Token'),
});

export type SpaceInvitationRefreshReq = z.infer<typeof SpaceInvitationRefreshSchema>;

export const SpaceInvitationDeleteSchema = z.object({
  spaceId: z.string().describe('Space ID'),
  inviteToken: z.string().describe('Invitation Token'),
});

export type SpaceInvitationDeleteReq = z.infer<typeof SpaceInvitationDeleteSchema>;

export const SpaceInvitationAcceptSchema = z.object({
  inviteToken: z.string().describe('Invitation Token'),
});

export type SpaceInvitationAcceptReq = z.infer<typeof SpaceInvitationAcceptSchema>;

export const EmailInviteSchema = z.object({
  type: InvitationTypeSchema.describe('Invitation member type'),
  email: z.string().email().describe('Email address'),
  name: z.string().optional().describe('Member name'),
});

export type EmailInvite = z.infer<typeof EmailInviteSchema>;

export const EmailInvitationSchema = z.object({
  spaceId: z.string(),
  invites: z.array(EmailInviteSchema).max(10),
});

export type EmailInvitationReq = z.infer<typeof EmailInvitationSchema>;

const EmailInviteListBaseSchema = z.object({
  spaceId: z.string(),
  email: z.string().email().optional(),
  sort: z
    .string()
    .refine((value) => ['name', 'createdAt'].includes(value), { message: 'Invalid sort field' })
    .default('createdAt'),
  direction: z
    .string()
    .optional()
    .transform((value) => (value === 'asc' ? value : 'desc')),
});
export const EmailInviteListSchema = EmailInviteListBaseSchema.merge(PaginationSchema);
export type EmailInviteListReq = z.infer<typeof EmailInviteListSchema>;

export const EmailInvitationInfoSchema = z.object({
  inviteId: z.string(),
});
export type EmailInvitationInfoReq = z.infer<typeof EmailInvitationInfoSchema>;

export const EmailInvitationAcceptSchema = z.object({
  inviteId: z.string(),
});
export type EmailInvitationAcceptReq = z.infer<typeof EmailInvitationAcceptSchema>;

export const EmailInvitationRejectSchema = z.object({
  inviteId: z.string(),
});
export type EmailInvitationRejectReq = z.infer<typeof EmailInvitationRejectSchema>;

export const SpaceCreditTransactionListSchema = z
  .object({
    spaceId: z.string().describe('Space ID'),
  })
  .merge(PaginationSchema);
export type SpaceCreditTransactionListReq = z.infer<typeof SpaceCreditTransactionListSchema>;
