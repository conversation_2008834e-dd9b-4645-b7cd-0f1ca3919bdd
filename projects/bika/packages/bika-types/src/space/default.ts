import { iString, iStringParse, Locale } from 'basenext/i18n';
import { SpaceRenderVO, SpaceVO } from './vo-space';

const guestRootTeamName: iString = {
  en: 'Guest group',
  'zh-CN': '访客组',
  'zh-TW': '訪客組',
  ja: 'ゲストグループ',
};

export const defaultGuestRootTeamName = (locale?: Locale): string => iStringParse(guestRootTeamName, locale);

export function defaultSpaceVO(): SpaceVO {
  return {
    id: 'default-space',
    name: 'Default Space',
    logo: { type: 'COLOR', color: 'BLUE' },
    settings: {
      onboardingStage: 'DONE',
    },
  };
}

export function defaultSpaceRenderVO(): SpaceRenderVO {
  const spaceVO = defaultSpaceVO();
  return {
    ...spaceVO,
    memberCount: 0,
    aiMembersCount: 0,
    usersMembersCount: 0,
  };
}
