import { AvatarLogoSchema } from 'basenext/avatar';
import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import { VirtualMissionFilterSchema } from '../mission/bo-virtual-mission';
import { AccessPrivilegeSchema } from '../permission/bo';
import { SubscriptionVOSchema } from '../pricing/billing-vo';
import { RenderOptionSchema } from '../system/render-option';

// Space joining method
export const SpaceJoinTypeSchema = z.enum(['LINK_INVITATION', 'EMAIL_INVITATION', 'APPLY']);
export type SpaceJoinType = z.infer<typeof SpaceJoinTypeSchema>;

// Space joining information
export const SpaceJoinInfoSchema = z.discriminatedUnion('joinType', [
  z.object({
    joinType: z.literal('LINK_INVITATION'),
    inviteToken: z.string(),
    // Inviter user ID
    inviterUserId: z.string(),
  }),
  z.object({
    joinType: z.literal('EMAIL_INVITATION'),
    email: z.string(),
    emailInvitationId: z.string(),
  }),
  z.object({
    joinType: z.literal('APPLY'),
  }),
]);
export type SpaceJoinInfo = z.infer<typeof SpaceJoinInfoSchema>;

export const SpaceJoinPermissionTypeSchema = z.enum([
  /**
   * Requires invitation to join (default)
   */
  'INVITE',

  /**
   * Anyone can join, no approval needed, default role is GUEST
   */
  'OPEN',

  /**
   * Can actively open URL to apply, requires request and approval to join
   */
  'APPROVAL',
]);
export type SpaceJoinPermissionType = z.infer<typeof SpaceJoinPermissionTypeSchema>;

/**
 * Onboarding has 4 stages
 */
export const SpaceOnboardingStageSchema = z.enum([
  // Completed
  'DONE',
  /**
   * Initialization stage, select template, fill in space name
   */
  'INIT',
  /**
   * UI tutorial stage, introduce layout
   */
  'UI',
  /**
   * Check if User has completed Onboarding (login verification, survey completion, research info)
   */
  'AUTH',
  /**
   * Credit card not bound yet, trial version popup occasionally, ad space
   */
  'TRIAL',
]);
export type SpaceOnboardingStage = z.infer<typeof SpaceOnboardingStageSchema>;

export const OnboardingPages = ['INVITE_CODE', 'INTRO_BILLING_PLAN', 'ONBOARDING_TEMPLATES'] as const;

export const OnboardingPageSchema = z.enum(OnboardingPages);

export type OnboardingPage = z.infer<typeof OnboardingPageSchema>;

/**
 * Zod Schema
 */
export const SpaceSettingsVOSchema = z
  .object({
    announcement: iStringSchema.optional().describe('Announcement displayed on Dashboard homepage'),

    watermark: z.boolean().optional().describe('Watermark feature'),

    allowEmailDomains: z
      .array(z.string())
      .optional()
      .describe('Enterprise email settings, if set, only these domains can join'),

    joinPermission: SpaceJoinPermissionTypeSchema.optional(),

    // Space wallpaper
    wallpaper: AvatarLogoSchema.optional(),

    /**
     * Has the onboarding been completed?
     * Not optional to avoid onboarding invalidation
     */
    onboardingStage: SpaceOnboardingStageSchema.describe('Onboarding stage').optional(),

    onboardings: z.array(OnboardingPageSchema).optional(),

    // Remove branding LOGO
    removeLogos: z
      .boolean()
      .optional()
      .describe('Whether to remove logos in forms, public pages, etc. Premium enterprise feature'),

    // Virtual missions, automatically display a mission to users based on configured rules
    virtualMissionFilters: z.array(VirtualMissionFilterSchema).optional(),

    // Global default space resource permission, CAN_VIEW, CAN_MANAGE(default), NO_ACCESS
    resourceGlobalDefaultPermission: AccessPrivilegeSchema.optional().nullable(),
  })
  .describe('Space additional settings');

export type SpaceSettingsVO = z.infer<typeof SpaceSettingsVOSchema>;

export type SpaceSettingsVOKey = keyof SpaceSettingsVO;

export const SpaceVOSchema = z.object({
  id: z.string(),
  name: z.string(),
  createBy: z.string().optional(),
  createdAt: z.string().optional(),
  slug: z.string().optional(),
  logo: AvatarLogoSchema,
  settings: SpaceSettingsVOSchema,
  owner: z.string().optional().describe('Space owner user ID'),
});
export type SpaceVO = z.infer<typeof SpaceVOSchema>;

// Node VO render condition options
export const SpaceRenderOptsSchema = RenderOptionSchema.extend({
  // Visitor's user ID, append permission return
  userId: z.string().optional(),
});

export type SpaceRenderOpts = z.infer<typeof SpaceRenderOptsSchema>;

export const SpaceRenderVOSchema = SpaceVOSchema.extend({
  memberCount: z.number(),
  usersMembersCount: z.number(),
  aiMembersCount: z.number(),
  unreadMessageCount: z.number().optional(),
  subscription: SubscriptionVOSchema.optional(),
});

export type SpaceRenderVO = z.infer<typeof SpaceRenderVOSchema>;

export const SpaceUpdateInfoSchema = z.object({
  name: z.string().optional().describe('Space name'),
  slug: z.string().optional().describe('Space slug'),
  logo: AvatarLogoSchema.optional().describe('Space logo'),
  settings: SpaceSettingsVOSchema.partial().optional().describe('Space settings'),
});

export type SpaceUpdateInfo = z.infer<typeof SpaceUpdateInfoSchema>;

export const SpaceFormSchema = z.object({
  id: z.string(),
  name: z.string(),
  watermark: z.boolean().optional().describe('Watermark feature'),
  allowEmailDomains: z
    .array(z.string())
    .optional()
    .describe('Enterprise email settings, if set, only these domains can join'),
  onboarding: SpaceOnboardingStageSchema.describe('Onboarding stage'),
});

export const SpaceCreditVOSchema = z.object({
  virtualBalance: z.number(), // virtual balance, total used to show
  virtualCredit: z.number(), // virtual credit
  virtualGiftCredit: z.number(), // virtual gift credit
  virtualSubscribeCredit: z.number(), // virtual subscribe credit
  credit: z.number(), // permanent credit
});
export type SpaceCreditVO = z.infer<typeof SpaceCreditVOSchema>;
