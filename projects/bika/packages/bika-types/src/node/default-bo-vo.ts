import { iStringParse, type Locale } from 'basenext/i18n';
import { type NodeResourceType } from './base';
import type { NodeResource } from './bo';
import { FolderSchema } from './bo';
import { MirrorBOSchema, MirrorCreateClientBOSchema, MirrorTypeEnum } from './bo-mirror';
// import { defaultFolderBO } from './node-create-dto';
import {
  FolderCreateBOSchema,
  FormCreateBO,
  FolderCreateBO,
  FormCreateClientBOSchema,
  MirrorCreateBO,
  ResourceCreateDTO,
} from './dto-node-create';
import type { FolderVO, NodeDetailVO } from './vo-node';
import { ResourceVO } from './vo-node';
import { defaultAIAgentNodeCreateDTO, defaultAIPageNodeCreateDTO } from '../ai/default';
import { defaultAutomationBO, defaultAutomationCreateBO } from '../automation/default';
import type { AutomationCreateBO } from '../automation/dto-automation';
import { defaultDashboardBO, defaultDashboardCreateBO } from '../dashboard/default';
import { DashboardCreateBO } from '../dashboard/dto';
import { defaultDatabaseCreateBO, defaultDatabaseBO } from '../database/default';
import type { DatabaseCreateDTO } from '../database/dto-database';
import { defaultDocumentCreateBO, defaultFileNodeCreateBO } from '../document/default';
import { FormBOSchema } from '../form/bo-form';
import { toResourceVO } from '../utils';

export function defaultFolderCreateBO(locale: Locale) {
  return FolderCreateBOSchema.parse({
    resourceType: 'FOLDER',
    name: iStringParse(
      { en: 'New Folder', 'zh-CN': '新的未命名文件夹', 'zh-TW': '新的未命名文件夾', ja: '新しいフォルダ' },
      locale,
    ),
    description: undefined,
  });
}

export function defaultFormBO(id: { id?: string; templateId?: string }, create: FormCreateBO) {
  return FormBOSchema.parse({
    id: id.id,
    templateId: id.templateId,
    ...create,
  });
}

export function defaultFolderBO(id: { id?: string; templateId?: string }, create: FolderCreateBO) {
  return FolderSchema.parse({
    id: id.id,
    templateId: id.templateId,
    ...create,
  });
}

export function defaultFormCreateBO(locale: Locale): FormCreateBO {
  return FormCreateClientBOSchema.parse({
    resourceType: 'FORM',
    name: iStringParse(
      {
        en: 'New Unnamed Form',
        'zh-CN': '新的未命名表单',
        'zh-TW': '新的未命名表單',
        ja: '新しい未命名フォーム',
      },
      locale,
    ),
    description: undefined,
  });
}

export function defaultMirrorCreateBO(locale: Locale) {
  return MirrorCreateClientBOSchema.parse({
    resourceType: 'MIRROR',
    name: iStringParse(
      {
        en: 'New Unnamed Mirror',
        'zh-CN': '新的未命名镜像',
        'zh-TW': '新的未命名鏡像',
        ja: '新しい未命名ミラー',
      },
      locale,
    ),
    mirrorType: MirrorTypeEnum.DATABASE_VIEW,
    description: undefined,
  });
}

export function defaultMirrorBO(id: { id?: string; templateId?: string }, create: MirrorCreateBO) {
  return MirrorBOSchema.parse({
    id: id.id,
    templateId: id.templateId,
    mirrorType: 'MIRROR_VIEW',
    view: {
      viewType: 'TABLE',
    },
    ...create,
  });
}

export function defaultResourceCreateDTO(
  resourceType: ResourceCreateDTO['resourceType'],
  locale: Locale,
): ResourceCreateDTO {
  switch (resourceType) {
    case 'DATABASE':
      return defaultDatabaseCreateBO(locale);
    case 'AUTOMATION':
      return defaultAutomationCreateBO(locale);
    case 'FOLDER':
      return defaultFolderCreateBO(locale);
    case 'DASHBOARD':
      return defaultDashboardCreateBO(locale);
    case 'FORM':
      return defaultFormCreateBO(locale);
    case 'MIRROR':
      return defaultMirrorCreateBO(locale);
    case 'DOCUMENT':
      return defaultDocumentCreateBO(locale);
    case 'FILE':
      return defaultFileNodeCreateBO(locale);
    case 'AI':
      return defaultAIAgentNodeCreateDTO(locale);
    case 'PAGE':
      return defaultAIPageNodeCreateDTO(locale);
    default:
      return {
        resourceType,
        name: iStringParse(
          {
            en: 'Coming soon resource',
            'zh-CN': '敬请期待的资源',
            'zh-TW': '敬請期待的資源',
            ja: 'お楽しみのリソース',
          },
          locale,
        ),
      };
  }
}
/**
 *
 * Get the default value. If `create` is provided, it will override the default. If not provided, the default value will be used.
 *
 * If `create` is not empty, `locale` will be used to create the default value.
 *
 * Note: This function may return null.
 *
 * @param type
 * @param locale
 * @param override
 * @returns
 */
export function defaultNodeResourceBO(
  type: NodeResourceType,
  id: { id?: string; templateId?: string },
  locale: Locale,
): NodeResource | null {
  switch (type) {
    case 'DATABASE':
      return defaultDatabaseBO(id, defaultResourceCreateDTO('DATABASE', locale) as DatabaseCreateDTO);
    case 'AUTOMATION':
      return defaultAutomationBO(id, defaultResourceCreateDTO('AUTOMATION', locale) as AutomationCreateBO);
    case 'DASHBOARD':
      return defaultDashboardBO(id, defaultResourceCreateDTO('DASHBOARD', locale) as DashboardCreateBO);
    case 'MIRROR': {
      const newMirroBO = defaultMirrorBO(id, defaultResourceCreateDTO('MIRROR', locale) as MirrorCreateBO);
      return newMirroBO;
    }
    case 'FORM':
      return defaultFormBO(id, defaultResourceCreateDTO('FORM', locale) as FormCreateBO);
    case 'FOLDER':
      return defaultFolderBO(
        id,
        defaultResourceCreateDTO('FOLDER', locale) as FolderCreateBO,
        // {
        //   resourceType: 'FOLDER',
        //   name: iStringParse(
        //     {
        //       en: 'new folder',
        //       'zh-CN': '新的未命名文件夹',
        //       'zh-TW': '新的未命名文件夾',
        //       ja: '新しいフォルダ',
        //     },
        //     locale,
        //   ),
        //   description: undefined,
        // },
      );
    default:
      return null;
  }
}
/**
 *
 * Convert DTO to BO, commonly used in local editors, does not need to go through PO
 *
 * @param id
 * @param dto
 * @returns
 */
export function createNodeResourceBOWithDTO(
  id: { id?: string; templateId?: string },
  dto: ResourceCreateDTO,
): NodeResource {
  switch (dto.resourceType) {
    case 'DATABASE': {
      const newDatabaseBO = defaultDatabaseBO(id, dto);
      return newDatabaseBO;
    }
    case 'DASHBOARD': {
      const newDashboardBO = defaultDashboardBO(id, dto);
      return newDashboardBO;
    }
    case 'FORM': {
      const newFormBO = defaultFormBO(id, dto);
      return newFormBO;
    }
    case 'MIRROR': {
      const newMirroBO = defaultMirrorBO(id, dto);
      return newMirroBO;
    }
    case 'FOLDER': {
      const newFolderBO = defaultFolderBO(id, dto);
      return newFolderBO;
    }
    case 'AUTOMATION': {
      const newAutomationBO = defaultAutomationBO(id, dto);
      return newAutomationBO;
    }
    case 'DOCUMENT':
      return {
        resourceType: 'DOCUMENT',
        name: 'New Document',
        markdown: '',
      };
    default:
      return {
        resourceType: dto.resourceType,
        name: 'not implemented',
      };
  }
}

export function defaultNodeDetailVO(resourceType: NodeResourceType, locale: Locale): NodeDetailVO | null {
  const resourceVO = toResourceVO(resourceType, locale);
  if (!resourceVO) {
    console.warn('defaultNodeDetailVO: nodeResourceBO is null', resourceType);
    return null;
  }
  return {
    id: resourceVO.id,
    name: (resourceVO as FolderVO).name,
    type: resourceType,
    scope: 'SPACE',
    resource: resourceVO,
  };
}

export function defaultResourceVO(resourceType: NodeResourceType, locale: Locale): ResourceVO | null {
  const resourceVO = toResourceVO(resourceType, locale);
  return resourceVO;
}
