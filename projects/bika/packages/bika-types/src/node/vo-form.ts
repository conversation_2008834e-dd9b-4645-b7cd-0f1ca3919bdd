import { AvatarLogoSchema } from 'basenext/avatar';
import { z } from 'zod';
import { BaseDatabaseVOSchema } from '../database/vo-database';
import { ViewFieldVOSchema, ViewSimpleVOSchema } from '../database/vo-view';
import { FormMetadataSchema } from '../form/bo-form';

export const FormVOSchema = z.object({
  id: z.string(),
  name: z.string(),
  cover: AvatarLogoSchema.optional(),
  brandLogo: AvatarLogoSchema.optional(),
  icon: AvatarLogoSchema.optional(),
  description: z.string().optional(),
  metadata: FormMetadataSchema,
  databaseId: z.string(),
  fields: z.array(ViewFieldVOSchema),
  database: BaseDatabaseVOSchema,
  view: ViewSimpleVOSchema.optional(),
});
export type FormVO = z.infer<typeof FormVOSchema>;
