import { iStringSchema, LocaleSchema } from 'basenext/i18n';
import { z } from 'zod';

export const UpgradeState = z.literal('UPGRADE');
export const ErrorState = z.literal('ERROR');
export const SharedState = z.literal('SHARED');
export const LockedState = z.literal('LOCKED');
export const RunningState = z.literal('RUNNING'); // means automaiton is open
export const NumberState = z.literal('NUMBER');
export const StopState = z.literal('STOP'); // means automaiton is closed

export const NumberNodeStateSchema = z.object({
  state: NumberState,
  number: z.number(),
});
export type NumberNodeState = z.infer<typeof NumberNodeStateSchema>;

export const ErrorNodeStateSchema = z.object({
  state: ErrorState,
  message: iStringSchema,
  resourceId: z.string().optional(),
});
export type ErrorNodeState = z.infer<typeof ErrorNodeStateSchema>;

/**
 * NodeState, used for Node status, UI for icon display, such as folder upgrades, automation configuration errors, etc.
 */
export const NodeStateBOSchema = z.union([
  // Usually used when template folders can be upgraded
  z.object({
    state: UpgradeState,
  }),
  // Error message, when automation is saved, if configuration is incorrect, the node will be set to this state for UI display
  ErrorNodeStateSchema,
  // In some scenarios, numbers are displayed on nodes
  NumberNodeStateSchema,
  // share
  z.object({
    state: SharedState,
  }),
  // have permission
  z.object({
    state: LockedState,
  }),
  z.object({
    state: RunningState,
  }),
  z.object({
    state: StopState,
  }),
]);

export const NodeStateSchema = z.array(NodeStateBOSchema).nullable().optional();

export type NodeStateBO = z.infer<typeof NodeStateBOSchema>;

export const NodeStateEnum = z.enum([
  NodeStateBOSchema.options.map((i) => i.shape.state.value)[0],
  ...NodeStateBOSchema.options.map((i) => i.shape.state.value).slice(1),
]);

export type NodeState = z.infer<typeof NodeStateEnum>;

export const ToBoOptionSchema = z.object({
  withRecords: z.boolean().optional(),
});

export type ToBoOptions = z.infer<typeof ToBoOptionSchema>;

export const ToTemplateOptionSchema = ToBoOptionSchema.extend({
  locale: LocaleSchema.optional(),
  templateId: z.string(),
});

export type ToTemplateOptions = z.infer<typeof ToTemplateOptionSchema> & {
  getTemplateId?: (id: string) => string | undefined;
};

export const ExportBOOptionSchema = ToBoOptionSchema.extend({
  resourceName: z.string().optional(),
  locale: LocaleSchema.optional(),
});

export type ExportBOOptions = z.infer<typeof ExportBOOptionSchema> & {
  getInstanceId?: (id: string) => string | undefined;
};
