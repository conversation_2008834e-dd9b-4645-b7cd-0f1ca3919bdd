import { AvatarLogoSchema } from 'basenext/avatar';
import { z } from 'zod';
import { BaseNodeResourceBOSchema, FormNodeType } from './base';

export const CodePageBOSchema = BaseNodeResourceBOSchema.extend({
  resourceType: FormNodeType,
  databaseId: z.string().optional(),
  databaseTemplateId: z.string().optional(),
  cover: AvatarLogoSchema.optional(),
  brandLogo: AvatarLogoSchema.optional(),
});

export type CodePageBO = z.infer<typeof CodePageBOSchema>;
