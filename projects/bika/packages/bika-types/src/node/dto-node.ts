import { PaginationSchema } from 'basenext/pagination';
import { z } from 'zod';
import { NodeResourceScopeSchema, NodeResourceTypeSchema } from './base';
import { NodeShareScopeSchema } from '../permission/access-vos';
import { AccessPrivilegeSchema } from '../permission/bo';
import { TemplatePublishDTOSchema } from '../template/dto-template';

// Node list query base schema
const NodeListBaseSchema = z.object({
  spaceId: z.string(),
  name: z.string().optional().describe('Node name'),
  parentId: z.string().optional().describe('Parent node ID'),
  resourceType: NodeResourceTypeSchema.optional().describe('Node type'),
  sort: z
    .string()
    .refine((value) => ['name', 'createdAt'].includes(value), { message: 'Invalid sort field' })
    .default('createdAt'),
  direction: z
    .string()
    .optional()
    .transform((value) => (value === 'asc' ? value : 'desc')),
});

export const NodeListDTOSchema = NodeListBaseSchema.merge(PaginationSchema);

export type NodeListDTOZodInput = z.input<typeof NodeListDTOSchema>;
export type NodeListDTOZodOutput = z.output<typeof NodeListDTOSchema>;
export type NodeListDTOWithoutSpaceID = Omit<NodeListDTOZodInput, 'spaceId'>;

// Node search schema
export const NodeSearchDTOSchema = z.object({
  spaceId: z.string(),
  keyword: z.string().optional(),
  resourceType: NodeResourceTypeSchema.optional(),
  scope: NodeResourceScopeSchema.optional(),
  pageNo: z.number().min(1),
  pageSize: z.number().max(20),
  nodeId: z.string().optional(),
});

export type NodeSearchDTO = z.infer<typeof NodeSearchDTOSchema>;

// Node info schema
export const NodeInfoDTOSchema = z.object({
  id: z.string(),
});

export type NodeInfoDTO = z.infer<typeof NodeInfoDTOSchema>;

// Node delete schema
export const NodeDeleteDTOSchema = z.object({
  spaceId: z.string(),
  id: z.string(),
});

export type NodeDeleteDTO = z.infer<typeof NodeDeleteDTOSchema>;

// Node publish schema
export const NodePublishDTOSchema = z.object({
  spaceId: z.string(),
  id: z.string(),
  data: TemplatePublishDTOSchema.describe('Publish parameters'),
});

export type NodePublishDTO = z.infer<typeof NodePublishDTOSchema>;

// Node export schema
export const NodeExportDTOSchema = z.object({
  spaceId: z.string(),
  id: z.string(),
  withRecords: z.boolean().optional(),
});

export type NodeExportDTO = z.infer<typeof NodeExportDTOSchema>;

// Excel import, only supports table type
export const ExcelImportDTOSchema = z.object({
  spaceId: z.string(),
  parentNodeId: z.string().optional(),
  fileName: z.string(),
  tmpAttachmentId: z.string(),
});
export type ExcelImportDTO = z.infer<typeof ExcelImportDTOSchema>;

export const NodeImportTypeSchema = z.enum(['bikafile', 'vika']);

export type NodeImportType = z.infer<typeof NodeImportTypeSchema>;

const baseNodeImportSchema = z.object({
  type: NodeImportTypeSchema,
  parentNodeId: z.string().optional(),
  spaceId: z.string(),
  scope: NodeResourceScopeSchema.optional(),
});

export const BikafileImportDTOSchema = baseNodeImportSchema.extend({
  type: z.literal(NodeImportTypeSchema.enum.bikafile),
  tmpAttachmentId: z.string(),
});

export type BikafileImportDTO = z.infer<typeof BikafileImportDTOSchema>;

export const VikaImportDTOSchema = baseNodeImportSchema.extend({
  type: z.literal(NodeImportTypeSchema.enum.vika),
  vikaResourceId: z.string(),
  integrationId: z.string(),
});

export const NodeImportDTOSchema = z.discriminatedUnion('type', [BikafileImportDTOSchema, VikaImportDTOSchema]);

export type NodeImportDTO = z.infer<typeof NodeImportDTOSchema>;

// Node detach schema
export const NodeDetachDTOSchema = z.object({
  spaceId: z.string(),
  id: z.string(),
});

export type NodeDetachDTO = z.infer<typeof NodeDetachDTOSchema>;

// Node upgrade schema
export const NodeUpgradeDTOSchema = z.object({
  spaceId: z.string(),
  id: z.string(),
});

export type NodeUpgradeDTO = z.infer<typeof NodeUpgradeDTOSchema>;

// Node apply access schema
export const NodeApplyAccessDTOSchema = z.object({
  id: z.string(),
});

export type NodeApplyAccessDTO = z.infer<typeof NodeApplyAccessDTOSchema>;

// Node share password validation schema
export const NodeSharePasswordValidateDTOSchema = z.object({
  id: z.string(),
  password: z.string().describe('Password'),
});

export type NodeSharePasswordValidateDTO = z.infer<typeof NodeSharePasswordValidateDTOSchema>;

// Share related
export const NodeShareInfoDTOSchema = z.object({
  spaceId: z.string(),
  id: z.string(),
});

export type NodeShareInfoDTOReq = z.infer<typeof NodeShareInfoDTOSchema>;

export const NodeCollaboratorListDTOSchema = z
  .object({
    spaceId: z.string(),
    id: z.string(),
  })
  .merge(PaginationSchema);

export type NodeCollaboratorListDTO = z.infer<typeof NodeCollaboratorListDTOSchema>;

// Node share update schema
export const NodeShareUpdateDTOSchema = z.object({
  spaceId: z.string(),
  id: z.string(),
  scope: NodeShareScopeSchema,
});

export type NodeShareUpdateDTO = z.infer<typeof NodeShareUpdateDTOSchema>;

// Node share restore schema
export const NodeShareRestoreDTOSchema = z.object({
  spaceId: z.string(),
  id: z.string(),
});

export type NodeShareRestoreDTO = z.infer<typeof NodeShareRestoreDTOSchema>;

// Node share password create schema
export const NodeSharePasswordCreateDTOSchema = z.object({
  spaceId: z.string(),
  id: z.string(),
});

// Node share password update schema
export const NodeSharePasswordUpdateDTOSchema = z.object({
  spaceId: z.string(),
  id: z.string(),
  password: z.string(),
});

// Node share password delete schema
export const NodeSharePasswordDeleteDTOSchema = z.object({
  spaceId: z.string(),
  id: z.string(),
});

// Node grant permission schema
export const NodeGrantPermissionSchema = z.object({
  spaceId: z.string(),
  id: z.string(),
  unitIds: z.array(z.string()).describe('Collaborators ID'),
  privilege: AccessPrivilegeSchema,
});

export type NodeGrantPermissionDTO = z.infer<typeof NodeGrantPermissionSchema>;

// Node revoke permission schema
export const NodeRevokePermissionSchema = z.object({
  spaceId: z.string(),
  id: z.string(),
  unitId: z.string().describe('Collaborator ID'),
});
