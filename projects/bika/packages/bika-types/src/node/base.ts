import { AvatarLogoSchema } from 'basenext/avatar';
import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import { NodeStateBOSchema } from './bo-nodes';
import { PermissionSchema } from '../permission/bo';

export const NodeResourceTypes = [
  'ROOT', //
  'TEMPLATE', // （）
  'FOLDER', //

  'DATABASE', //

  'FORM', //

  'DASHBOARD', //

  'DOCUMENT',

  'FILE',

  'AI', // AI Chat Agent / Chatbot
  'PAGE', //
  'EMBED', // ，

  'AUTOMATION', //
  'MIRROR', // mirror，View

  // /**
  //  * @deprecated   View，Mirror
  //  */
  // 'VIEW', // ()，mirror @deprecated
  'DATAPAGE', //
  'CANVAS', //
  'ALIAS', //
  'REPORT_TEMPLATE', //
  // 'APP_PAGE', //
  // 'CAMPAIGN', //
] as const;

export const NodeResourceTypeSchema = z.enum(NodeResourceTypes);

export const FolderNodeType = z.literal(NodeResourceTypeSchema.enum.FOLDER);
export const TemplateNodeType = z.literal(NodeResourceTypeSchema.enum.TEMPLATE);
export const DatabaseNodeType = z.literal(NodeResourceTypeSchema.enum.DATABASE);
export const DashboardNodeType = z.literal(NodeResourceTypeSchema.enum.DASHBOARD);
// export const ViewNodeType = z.literal(NodeResourceTypeSchema.enum.VIEW);
export const FormNodeType = z.literal(NodeResourceTypeSchema.enum.FORM);
export const ReportTemplateNodeType = z.literal(NodeResourceTypeSchema.enum.REPORT_TEMPLATE);
export const AutomationNodeType = z.literal(NodeResourceTypeSchema.enum.AUTOMATION);

export type NodeResourceType = z.infer<typeof NodeResourceTypeSchema>;

export const NodeResourceScopeSchema = z.enum(['SPACE', 'PRIVATE']);

export type NodeResourceScope = z.infer<typeof NodeResourceScopeSchema>;
/**
 * Node Resource Schema
 */
export const BaseNodeResourceBOSchema = z.object({
  resourceType: NodeResourceTypeSchema,
  id: z.string().optional(),
  templateId: z.string().optional(),
  name: iStringSchema
    .default('new resource')
    .describe('Node resource name, human friendly name for this resource content.'),
  description: iStringSchema
    .optional()
    .describe(
      'Node resource description, description for introducing this resource content. Must be different from name and more detailed',
    ),
  permissions: z.array(PermissionSchema).optional(),
  scope: NodeResourceScopeSchema.optional(),
  icon: AvatarLogoSchema.optional(),
});

/**
 * Create DTO
 */
export const BaseCreateNodeResourceBOSchema = BaseNodeResourceBOSchema.pick({
  name: true,
  description: true,
  templateId: true,
  resourceType: true,
  id: true,
  icon: true,
});

//  Update DTO
export const NodeUpdateBOSchema = z.object({
  // basic
  name: iStringSchema.optional(),
  description: iStringSchema.optional(),
  templateId: z.string().optional(),
  resourceType: NodeResourceTypeSchema.optional(),
  icon: AvatarLogoSchema.optional().nullable(),
  // Node.state，Resource update/create，node.state
  nodeState: NodeStateBOSchema.optional(),
});

export type NodeUpdateBO = z.infer<typeof NodeUpdateBOSchema>;

export const NodeMoveBOSchema = z.object({
  // preNodeId, preNodeId=null, , ,
  parentId: z.string().optional(),
  // ,null, ID, ,undefined
  preNodeId: z
    .string()
    .nullish()
    .refine(
      (value) => {
        //  undefined （） null（）
        if (value === undefined || value === null) return true;

        // 、
        return value.trim() !== '';
      },
      {
        message: 'preNodeId cannot be an empty string or contain only spaces',
      },
    ),

  //
  scope: NodeResourceScopeSchema.optional(),
});

export type NodeMoveBO = z.infer<typeof NodeMoveBOSchema>;
