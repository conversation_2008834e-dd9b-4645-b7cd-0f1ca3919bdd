import { AvatarLogoSchema } from 'basenext/avatar';
import { z } from 'zod';
import { NodeResourceTypeSchema } from './base';
import { TalkExpertKeySchema } from '../space/bo-talk';
import { IconTypes } from '../ui/icon';

export const EnumIconTypeSchema = z.enum(['DATUM', 'release_notes', 'workflow', 'IMPORT', 'create']);
export type EnumIconType = z.infer<typeof EnumIconTypeSchema>;

export const NodeIconValueSchema = z.discriminatedUnion('kind', [
  z.object({
    kind: z.literal('node-resource'),
    nodeType: NodeResourceTypeSchema,
    customIcon: AvatarLogoSchema.nullish(),
  }),
  z.object({
    kind: z.literal('expert'),
    expertKey: TalkExpertKeySchema,
  }),
  z.object({
    kind: z.literal('enum'),
    enum: EnumIconTypeSchema,
  }),
  z.object({
    kind: z.literal('avatar'),
    avatar: AvatarLogoSchema.optional(),
    name: z.string().optional(),
  }),
  z.object({
    kind: z.literal('icon'),
    icon: z.enum(IconTypes), // TODO: ui IconType as Zod Schema,
  }),
]);

export type INodeIconValue = z.infer<typeof NodeIconValueSchema>;
