import { AvatarLogoSchema } from 'basenext/avatar';
import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import { NodeResourceScopeSchema, NodeResourceTypeSchema } from './base';
import { NodeResource } from './bo';
import {
  ErrorState,
  LockedState,
  NumberNodeStateSchema,
  RunningState,
  SharedState,
  StopState,
  UpgradeState,
} from './bo-nodes';
import { FormVOSchema } from './vo-form';
import { MirrorVOSchema } from './vo-mirror';
import { AiNodeVOSchema } from '../ai/vo-ai-node';
import { AiPageVOSchema } from '../ai/vo-ai-page';
import { AutomationVOSchema } from '../automation/vo-automation';
import { DashboardVOSchema } from '../dashboard/vo-dashboard';
import { DatabaseVOSchema } from '../database/vo';
import { DocumentVOSchema, FileNodeVOSchema } from '../document/vo';
import { NodePrivilegeVOSchema } from '../permission/node-action-vo';
import { ShortcutRelationTypeSchema } from '../space/bo-shortcut';
import { RenderOptionSchema } from '../system/render-option';
import { MemberVOSchema } from '../unit/vo-member';

export const NodeStatusBadgeVOSchema = z.union([
  // Upgrade ok
  UpgradeState,
  // exceptions
  ErrorState,
  StopState,
  RunningState,
  SharedState,
  // have permission
  LockedState,
  NumberNodeStateSchema,
]);

export type NodeStatusBadgeVO = z.infer<typeof NodeStatusBadgeVOSchema>;

const NodeVOSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  type: NodeResourceTypeSchema,
  icon: AvatarLogoSchema.nullish(),

  // icons
  statusBadge: z.array(NodeStatusBadgeVOSchema).optional(),
  sharing: z.boolean().describe('Whether being shared'),
  hasShareLock: z.boolean().describe('Whether sharing is password protected'),
  hasPermissions: z.boolean().describe('Whether has permissions set'),
  permission: NodePrivilegeVOSchema.optional().describe('Permission collection'),
  templateId: z.string().optional(),
  isVerified: z.boolean().optional(),
  parentId: z.string().optional().nullable(),
  preNodeId: z.string().optional().nullable(), // Used for sorting
  scope: NodeResourceScopeSchema.optional(),
  // node path
  path: z.string().optional(),
  // Only returned in shortcutTree
  shortcut: z
    .object({
      id: z.string(),
      relationType: ShortcutRelationTypeSchema,
    })
    .optional(),
});

export type NodeTreeVO = z.infer<typeof NodeVOSchema> & {
  children?: NodeTreeVO[];
};

export type NodeTreeVOZodInput = z.input<typeof NodeVOSchema> & {
  children?: NodeTreeVOZodInput[];
};

export type NodeTreeVOZodOutput = z.output<typeof NodeVOSchema> & {
  children?: NodeTreeVOZodOutput[];
};

export const NodeTreeVOSchema: z.ZodType<NodeTreeVOZodOutput, z.ZodTypeDef, NodeTreeVOZodInput> = NodeVOSchema.extend({
  children: z
    .lazy(() => NodeTreeVOSchema.array().optional())
    .openapi({
      type: 'array',
      items: { $ref: '#/components/schemas/NodeTreeVO' },
    }),
}).openapi('NodeTreeVO');

/**
 * Used for the right-click menu, displays node properties.
 */
export const NodeInfoVOSchema = z.object({
  id: z.string(),
  spaceId: z.string(),
  name: iStringSchema,
  createdAt: z.string(),
  updatedAt: z.string(),
  createdBy: MemberVOSchema.optional(),
  updatedBy: MemberVOSchema.optional(),
});

export type NodeInfoVO = z.infer<typeof NodeInfoVOSchema>;

// Used for menus
export const NodeMenuVOSchema = NodeVOSchema.pick({
  id: true,
  parentId: true,
  icon: true,
  type: true,
  permission: true,
  name: true,
  description: true,
  scope: true,
  path: true,
  templateId: true,
});

/**
 * Node menu, which varies depending on the type and permissions (picked from NodeTreeVO), a simplified version of NodeTreeVO and NodeDetailVO
 */
export type NodeMenuVO = z.infer<typeof NodeMenuVOSchema>;

export const BaseFolderVOSchema = NodeVOSchema.extend({
  cover: AvatarLogoSchema.optional(),
  readme: iStringSchema.optional(),
});

// Folder VO vs NodeTreeVO, with additional avatar and README properties
export type FolderVO = z.infer<typeof BaseFolderVOSchema> & {
  children?: NodeTreeVO[];
};

export const FolderVOSchema: z.ZodType<FolderVO> = BaseFolderVOSchema.extend({
  children: z.lazy(() => NodeTreeVOSchema.array()).default([]),
});

/**
 * Used on the frontend, in the folder details page, to render template author info (name + avatar), simple version
 */
export const StoreTemplateAuthorVOSchema = z.object({
  name: z.string(),
  avatar: AvatarLogoSchema,
});
export type StoreTemplateAuthorVO = z.infer<typeof StoreTemplateAuthorVOSchema>;

export const TemplateFolderVOSchema = FolderVOSchema.and(
  z.object({
    // Installation info
    installation: z.object({
      templateId: z.string(),
      version: z.string(),
    }),
    // Only tells who the author is, no template details, details need to be fetched from template.detail
    author: StoreTemplateAuthorVOSchema,
  }),
);

export type TemplateFolderVO = z.infer<typeof TemplateFolderVOSchema>;

export const ResourceVOSchema = z.union([
  FolderVOSchema,
  TemplateFolderVOSchema,
  DatabaseVOSchema,
  DocumentVOSchema,
  AutomationVOSchema,
  DashboardVOSchema,
  FormVOSchema,
  MirrorVOSchema,
  FileNodeVOSchema,
  AiNodeVOSchema,
  AiPageVOSchema,
]);
export type ResourceVO = z.infer<typeof ResourceVOSchema>;

// Node detail VO, used for node rendering view
export const NodeDetailVOSchema = z
  .object({
    id: z.string(),
    name: z.string(),
    description: z.string().optional(),
    type: NodeResourceTypeSchema,
    permission: NodePrivilegeVOSchema.optional().describe('Permission collection'),
    resource: ResourceVOSchema.optional(),
    icon: AvatarLogoSchema.nullish(),
    scope: NodeResourceScopeSchema,
    path: z.string().optional(), // node parent path not include the root
    // Status icons
    statusBadge: z.array(NodeStatusBadgeVOSchema).optional(),
    templateId: z.string().optional(),
    // Whether this is the user's first time accessing this node type
    isFirstVisit: z.boolean().optional(),
  })
  .default({
    id: 'new',
    name: '',
    type: 'DATABASE',
    scope: 'SPACE',
  });

export type NodeDetailVO = z.infer<typeof NodeDetailVOSchema>;

export type Simplify<T> = { [K in keyof T]: T[K] } & {};

export type Simple_NodeDetailVO = Simplify<NodeDetailVO>;

export type FolderDetailVO = z.infer<typeof NodeVOSchema> &
  z.infer<typeof BaseFolderVOSchema> & {
    children?: NodeDetailVO[];
  };

export const FolderDetailVOSchema = NodeVOSchema.extend({
  children: z.lazy(() => NodeDetailVOSchema.array()).default([]),
}).and(BaseFolderVOSchema);

// Node VO render option schema
export const NodeRenderOptsSchema = RenderOptionSchema.extend({
  // If it's a folder node, the recursive depth of child nodes. Be careful with the size, recommended to traverse at most 2 levels
  depth: z.number().optional(),
  // User ID of the visitor, append permission return
  userId: z.string().optional(),

  // Whether to return the user's private files? Filter out other users' private files, default is space station public
  scope: z.enum(['SPACE', 'PRIVATE']).optional(),
  // User time zone
  timeZone: z.string().optional(),
});

export type NodeRenderOpts = z.infer<typeof NodeRenderOptsSchema>;

export const BORenderOptsSchema = RenderOptionSchema.extend({
  // User ID of the visitor, append permission return
  userId: z.string().optional(),
  // User time zone
  timeZone: z.string().optional(),
});

export type BORenderOpts = z.infer<typeof BORenderOptsSchema> & {
  getProcessor?: (id?: string) => IBOProcessor<NodeResource>;
};

export interface IBOProcessor<T> {
  /**
   * Get resource BO, original bo not processed, initialized value in constructor
   */
  get bo(): T;

  /**
   * Get ID, processor will create ID by default, must not be empty
   */
  get id(): string;

  /**
   * Validate the legality of the resource BO
   * For example:
   * 1. Table type, check if there are multiple primary keys, duplicate names, etc.
   * 2. Automation config, check if dependent tables are feasible, but need to pass the entire resource as a parameter
   */
  // validate(): void;

  /**
   * Convert to VO
   */
  toVO<V>(opts?: BORenderOpts): V | Promise<V>;
}
