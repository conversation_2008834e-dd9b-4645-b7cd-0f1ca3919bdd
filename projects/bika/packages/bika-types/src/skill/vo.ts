import { ToolAppConfigurationTemplateVOSchema } from '@toolsdk.ai/sdk-ts/types/vo';
import { AvatarLogoSchema } from 'basenext/avatar';
import { BasePaginationInfoSchema } from 'basenext/pagination';
import { z } from 'zod';
import { McpServerConfigSchema, SkillsetKindSchema } from './bo';

export const SkillVOSchema = z.object({
  key: z.string(),
  name: z.string(),
  description: z.string().optional(),
  logo: AvatarLogoSchema.nullish().optional(),
});
export type SkillVO = z.infer<typeof SkillVOSchema>;

export const SkillsetVOSchema = z.object({
  kind: SkillsetKindSchema,
  logo: AvatarLogoSchema.nullish().optional(),
  key: z.string(),
  version: z.string().optional(),
  name: z.string(),
  description: z.string(),
  configuration: ToolAppConfigurationTemplateVOSchema.optional(),
  skills: z.array(SkillVOSchema),
  serverConfig: McpServerConfigSchema.optional().describe('Configuration for custom MCP server'),

  // TODO:????
  nodeId: z.string().optional().describe('Used for custom skillsets like call-automation'),
});
export type SkillsetVO = z.infer<typeof SkillsetVOSchema>;

export const SkillsetVOPaginationSchema = BasePaginationInfoSchema.extend({
  data: z.array(SkillsetVOSchema),
});
export type SkillsetVOPagination = z.infer<typeof SkillsetVOPaginationSchema>;
