import { CommonSearchDTOSchema } from 'basenext/pagination';
import { z } from 'zod';
import { SkillSelectBOSchema, SkillsetSelectBOSchema } from './bo';

export const SkillsetSelectDTOSchema = SkillsetSelectBOSchema;
export type SkillsetSelectDTO = z.infer<typeof SkillsetSelectDTOSchema>;

export const SkillSelectDTOSchema = SkillSelectBOSchema;
export type SkillSelectDTO = z.infer<typeof SkillSelectDTOSchema>;

export const SkillsetSearchDTOSchema = CommonSearchDTOSchema.extend({
  category: z.literal('featured').optional(),
  selectedSkillsets: SkillsetSelectBOSchema.array().optional(),
});
export type SkillsetSearchDTO = z.infer<typeof SkillsetSearchDTOSchema>;
