import { AvatarLogoSchema } from 'basenext/avatar';
import { iStringSchema, LocaleSchema } from 'basenext/i18n';
import { z } from 'zod';
import { CustomTemplate } from './bo-custom-template';
import {
  TemplateAuthorSchema,
  TemplateCategorySchema,
  TemplateVisibilityEnumSchema,
  TemplateCategoryEnums,
} from './bo-template-base';
import { TemplateRepoSchema } from './bo-template-repo';
import { SpaceVOSchema } from '../space/vo-space';
import { UserVOSchema } from '../user/vo-user';

// export const TemplateReleaseVOSchema = z.object({
//   version: z.string(),
//   changelog: z.string().nullable(),
//   releaseNotes: z.string().nullable(),
// });

// export const TemplateDetailVOSchema = z.object({
//   releases: z.array(TemplateReleaseVOSchema),
// });

// Used for detail page, rich content, includes release details
// export type TemplateDetailVO = z.infer<typeof TemplateDetailVOSchema>;

// Simplified VO, used for list
export const TemplateCardVOSchema = z.object({
  // Usually the templateId for display
  key: z.string().optional(),
  templateId: z.string(),
  name: iStringSchema,
  cover: z.union([AvatarLogoSchema, z.string().url()]),
  description: iStringSchema.optional(),
  category: TemplateCategorySchema,
  version: z.string(),
  visibility: TemplateVisibilityEnumSchema.optional(),
  stars: z.number().optional(),
  verified: z.boolean().optional(),
  author: TemplateAuthorSchema.optional(),
  detach: z.boolean().optional(),
});
export type TemplateCardVO = z.infer<typeof TemplateCardVOSchema>;

// TODO: Temporary, UI type should be merged with TemplateCardVO
export const TemplateCardInfoVOSchema = z.object({
  // Usually the templateId for display
  key: z.string().optional(),
  templateId: z.string(),
  name: z.string(),
  cover: z.union([AvatarLogoSchema, z.string().url()]),
  description: z.string().optional(),
  category: TemplateCategorySchema.optional(),
  version: z.string().optional(),
  visibility: TemplateVisibilityEnumSchema.optional(),
  stars: z.number().optional(),
  verified: z.boolean().optional(),
});
export type TemplateCardInfoVO = z.infer<typeof TemplateCardInfoVOSchema>;

/**
 * Used for template center section rendering VO
 */
export const TemplateCenterSectionConfigSchema = z.object({
  layout: z.literal('card'), // Currently supports card layout (2 per row). May support more in future like App Store style (2 per row) or cover style (1 per row)
  name: iStringSchema,
  description: iStringSchema.optional(),
  templateIds: z.array(z.string()),
  // For different locales, override and merge different templates
  localeTemplateIdsOverride: z.record(LocaleSchema, z.array(z.string())).optional(),
});
export type TemplateCenterSectionConfig = z.infer<typeof TemplateCenterSectionConfigSchema>;

/**
 * Used for template center section rendering VO, transformed from config
 */
export const TemplateCenterSectionVOSchema = z.object({
  layout: z.literal('card'), // Currently supports card layout (2 per row). May support more in future like App Store style (2 per row) or cover style (1 per row)
  name: iStringSchema,
  description: iStringSchema.optional(),
  templates: z.array(TemplateCardVOSchema),
});
export type TemplateCenterSectionVO = z.infer<typeof TemplateCenterSectionVOSchema>;

export function convertTemplateBOtoVO(template: CustomTemplate): TemplateCardVO {
  return {
    templateId: template.templateId,
    name: template.name,
    cover: template.cover,
    description: template.description,
    category: template.category,
    version: template.version,
  };
}
export const StoreTemplateCateogryEnums = ['recommend', ...TemplateCategoryEnums] as const;

export const StoreTemplateCategoryEnumSchema = z.enum(StoreTemplateCateogryEnums);

export type StoreTemplateCategoryEnum = z.infer<typeof StoreTemplateCategoryEnumSchema>;

export const StoreTemplateVOSchema = TemplateRepoSchema.extend({
  space: SpaceVOSchema.optional(),
  user: UserVOSchema.optional(),
  stars: z.number().optional(),

  // Whether it has been verified, very useful for recommendations
  verified: z.boolean().optional(),

  // Whether I have starred it
  isStarred: z.boolean().optional(),
});

export type StoreTemplateVO = z.infer<typeof StoreTemplateVOSchema>;

// export const TemplateRepoDetailVOSchema = TemplateRepoReleaseSchema.extend({
//   readme: iStringSchema.nullish(),
//   space: SpaceVOSchema.optional(),
//   user: UserVOSchema.optional(),
// });

// export type TemplateRepoDetailVO = z.infer<typeof TemplateRepoDetailVOSchema>;

/**
 * Store Template detail VO, includes fields from TemplateRepo and TemplatePO
 *
 * Use this as much as possible in UI. TemplateRepo acts as middleware, bridging "local template editing" and "remote user published templates"
 */
// export const StoreTemplateDetailVOSchema = z.object({
//   repo: TemplateRepoVOSchema,
//   visibility: TemplateVisibilityEnumSchema,
// });
// export type StoreTemplateDetailVO = z.infer<typeof StoreTemplateDetailVOSchema>;

// Storage structure, saved to OpenObserve
export const TemplatePreviewLogSchema = z.object({
  // template version
  version: z.string(),
  // audit log type
  kind: z.literal('TEMPLATE_PREVIEW_LOG'),
  templateid: z.string(),
  // preview log, FolderDetailVOSchema.parse
  data: z.string(),
});
export type TemplatePreviewLog = z.infer<typeof TemplatePreviewLogSchema>;
