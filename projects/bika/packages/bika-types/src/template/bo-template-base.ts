/**
 * Template data structure definition file
 *
 * CustomTemplate: Custom templates, template configurations, used for both internal product configurations and external customized template projects.
 *
 * Template: Definition of template resources and functionalities.
 * TemplateRepo: Template repository, contains all releases, used internally by Template SO.
 * TemplateRelease: A release of a template, includes the compiled `template.json` JSON data.
 */
import { AvatarLogoSchema } from 'basenext/avatar';
import { iStringSchema } from 'basenext/i18n';
import * as z from 'zod';
import { MissionSchema } from '../mission/missions';
import { NodeResourceSchema } from '../node/bo';
import { UnitSchema } from '../unit/bo-unit';

export const TemplateCategoryEnums = [
  'marketing',
  'sales',
  'daily-life',
  'project',
  'technology',
  'finance',
  'operation',
  'automation',
  'ai',
  'integration',
  'email',
  'script',
  'official',
  'space', // Current space template search category
] as const;
export const TemplateCategoryEnumSchema = z.enum(TemplateCategoryEnums);

export type TemplateCategoryEnum = z.infer<typeof TemplateCategoryEnumSchema>;

export const TemplateCategorySchema = TemplateCategoryEnumSchema.or(z.array(TemplateCategoryEnumSchema));
export type TemplateCategory = z.infer<typeof TemplateCategorySchema>;

export const TemplateVisibilityEnumSchema = z.union([
  z.literal('PUBLIC'),
  z.literal('SPACE'),
  z.literal('PRIVATE'),
  z.literal('WAITING_LIST'),
]);
export type TemplateVisibilityEnum = z.infer<typeof TemplateVisibilityEnumSchema>;

/**
 * Similar to: 'base': '*', 'base': '1.2.3'
 */
export const TemplateDependenciesSchema = z.record(z.string());

export type TemplateDependencies = z.infer<typeof TemplateDependenciesSchema>;

export const TemplateAuthorDisplaySchema = z.enum(['SPACE', 'USER']);

export type TemplateAuthorDisplayEnum = z.infer<typeof TemplateAuthorDisplaySchema>;

export const TemplateAuthorSchema = z.object({
  spaceId: z.string(),
  userId: z.string(),
  // space: mean show space name, user: show user name
  display: TemplateAuthorDisplaySchema.default('USER'),
});

export type TemplateAuthor = z.infer<typeof TemplateAuthorSchema>;

/**
 * Template Metadata, used to describe the basic information of a template.
 */
export const TemplateMetadataSchema = z.object({
  $schema: z.literal('https://dev.bika.ai/api/schema/custom-template.json').optional(),
  templateId: z.string().describe('Template ID, will be used as the template package name'),
  name: iStringSchema,
  description: iStringSchema.optional(),

  dependencies: TemplateDependenciesSchema.optional().describe('Dependencies on other templates, generally empty'),

  // logo, cover image
  cover: z.union([z.string(), AvatarLogoSchema]),

  // Screenshots
  screenshots: z
    .array(
      z.object({
        src: z.union([z.string(), AvatarLogoSchema]),
        name: iStringSchema,
      }),
    )
    .optional(),

  // Tutorials? Step-by-step guides, must be original, not AI-generated
  tutorials: z
    .array(
      z.object({
        name: iStringSchema,
        description: iStringSchema,
        screenshot: z.union([z.string(), AvatarLogoSchema]),
      }),
    )
    .optional(),

  // Benefits?
  benefits: z.array(z.object({ name: iStringSchema, description: iStringSchema })).optional(),

  // Frequently Asked Questions
  faq: z.array(z.object({ q: iStringSchema, a: iStringSchema })).optional(),

  homepage: z.string().optional().describe('URL of this template'),
  copyright: z.string().optional().describe('Copyright information'),

  author: z.string().optional().describe('Author').or(TemplateAuthorSchema),
  category: TemplateCategorySchema,

  // Keywords, used for SEO
  keywords: iStringSchema.optional(),

  // Personas, who will use it? Used for generating first-person SEO articles and displayed on the template.
  personas: iStringSchema.optional(),

  // Use cases, will become tags, e.g., {
  //   'en': 'Financial Report, Administration',
  //   'cn': '财务部管理,行政部探班',
  // }
  useCases: iStringSchema.optional(),

  installOnce: z
    .boolean()
    .optional()
    .describe('Whether the template can only be installed once. Cannot install a second one'),

  detach: z
    .boolean()
    .optional()
    .describe(
      'Whether the template can be detached from the space, meaning no template ID is attached, and no upgrades are available',
    ),

  visibility: TemplateVisibilityEnumSchema.optional(),
});

export type TemplateMetadata = z.infer<typeof TemplateMetadataSchema>;

export const TemplateIgnoreChangedEnumSchema = z.enum(['automation.status', 'database.records']);
export type TemplateIgnoreChangedEnum = z.infer<typeof TemplateIgnoreChangedEnumSchema>;

/**
 * Template content, template data, will be stored in the DB StoreTemplateRelease.
 * TODO: Separate TemplateMetadataSchema, so it is not included in TemplateDataSchema.
 */
export const TemplateDataSchema = TemplateMetadataSchema.extend({
  schemaVersion: z.literal('v1'),

  // Used to ignore changes in certain fields during updates. Also, if this field is configured, it enables user-editable mode.
  // Controls what is open to users for modification while updates do not affect it.
  // Reference Terraform lifecycle documentation: https://developer.hashicorp.com/terraform/language/meta-arguments/lifecycle
  // Map and list elements can be referenced using index notation, like resources[0].name and resources[0] respectively.
  ignoreChanged: z.array(TemplateIgnoreChangedEnumSchema.or(z.string())).optional(),

  version: z.string().describe('Semantic Version'),

  // Node Resources
  resources: z.array(NodeResourceSchema),

  // Initialization missions after installation
  initMissions: z.array(MissionSchema).optional().describe('Missions for initialization'),
  // Missions triggered when a new member joins
  newMemberJoinMissions: z.array(MissionSchema).optional().describe('Missions triggered when a new member joins'),

  // Pre-configured Units (can only be Team and Role)
  presetUnits: z.array(UnitSchema).optional(),
});
export type TemplateData = z.infer<typeof TemplateDataSchema>;

/**
 * Template configuration for external third-party developers,
 * also used internally for our own templates.
 * More engineering-oriented compared to GUI.
 */
// export const CustomTemplateSchema = TemplateMetadataSchema.merge(TemplateDataSchema).extend({
//   schemaVersion: z.literal('v1'),
//   version: z.string().describe('Semantic Version'),
// });

// export type CustomTemplate = z.infer<typeof CustomTemplateSchema>;

// /**
//  * Template Objects are Business Objects of Bika.ai
//  */
// export interface Template {
//   /**
//      * Template ID, will be used as the template package name
//      */
//   templateId: string;

//   name: string;
//   description?: string;

//   /**
//      * URL of this template
//      */
//   homepage?: string;

//   /**
//      * Copyright, license template cannot be fine-tune
//      */
//   copyright?: string;

//   /**
//      * Author
//      */
//   author?: string;

//   dependencies?: { [templateId: string]: string };

//   /**
//      * Missions for init
//      */
//   initMissions?: Mission[];

//   /**
//      * SemVer
//      */
//   version: string;
//   keywords?: string;
//   personas?: string;
//   category: TemplateCategory | TemplateCategory[];

//   nodes: Node[];

//   // reports?: ContentTemplate[];
//   // automations: Automation[];
//   // dashboards: Dashboard[];
//   // mirrors: Mirror[];
//   // forms: Form[];
//   // databases: Database[];

//   campaigns?: Campaign[];
//   extensions?: Extension[];
//   integrations?: Integration[];
// }
