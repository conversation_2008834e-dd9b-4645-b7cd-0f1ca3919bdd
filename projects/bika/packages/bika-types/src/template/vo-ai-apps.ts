import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import { NodeResourceSchema } from '../node/bo';

export const BuildAIAppsResponseVOSchema = z.object({
  templateId: z.string(),
  name: iStringSchema,
  description: iStringSchema.optional(),
  resources: z.array(NodeResourceSchema),
});
/**
 * Simplified CustomTemplate for AI generation, with a smaller schema focused on generating Resources
 */
export type BuildAIAppsResponseVO = z.infer<typeof BuildAIAppsResponseVOSchema>;
