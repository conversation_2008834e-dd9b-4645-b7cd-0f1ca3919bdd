import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import { TemplateDataSchema, TemplateMetadataSchema } from './bo-template-base';

export const TemplateRepoReleaseSchema = z.object({
  version: z.string(),
  changelog: z.string().nullish(),
  releaseNotes: z.string().nullish(),
  data: TemplateDataSchema,
});

// A single template release version and its data
export type TemplateRepoRelease = z.infer<typeof TemplateRepoReleaseSchema>;

/**
 * Complete template repository, containing all versions.
 * Used internally by Template SO, as an intermediate state derived from PO and BO.
 */
export const TemplateRepoSchema = TemplateMetadataSchema.extend({
  readme: iStringSchema.nullish(), // readme
  current: TemplateRepoReleaseSchema, // develop branch or production branch
  releases: z.array(TemplateRepoReleaseSchema), // 1.0.0 => TemplateRelease
});

/**
 * A template project, Repo -> Release -> Data(TemplateSchema)
 * A fusion of VO's BO, aggregated from PO and BO.
 *
 * Template Repo = Custom Template stripped of metadata + readme + releases
 */
export type TemplateRepo = z.infer<typeof TemplateRepoSchema>;

export const TemplateRepoScoreWarningSchema = z.enum([
  'NO_README',
  'NO_VIDEOS_IN_README',
  'NO_PHOTOS_IN_README',
  'NO_AUTHOR',
  'NO_COVER',
  'NO_KEYWORDS',
  'NO_INIT_MISSIONS',
  'DATABASE_NO_INIT_RECORDS',
  'KEYWORDS_LT_5',
  'NO_PERSONAS',
  'PERSONAS_NO_I18N',
  'PERSONAS_LT_4',
  'NO_USE_CASES',
  'USE_CASES_LT_24',
  'USE_CASES_NO_I18N',
  'NO_RESOURCES',
  'NOT_VERIFIED',
  'ERROR_RELEASE_NOTES',
]);
export type TemplateRepoScoreWarning = z.infer<typeof TemplateRepoScoreWarningSchema>;
