import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import { NodeResourceSchema } from '../node/bo';
import { CustomTemplateSchema } from '../template/bo';
import { TemplateRepoReleaseSchema } from '../template/bo-template-repo';

export const BikafileFormats = [
  // Resources only
  'RESOURCES',

  // Template package
  'TEMPLATE',
] as const;

export const BikafileFormatSchema = z.enum(BikafileFormats);
export type BikafileFormat = z.infer<typeof BikafileFormatSchema>;

export const BikafileTemplateDataSchema = z.object({
  format: z.literal(BikafileFormatSchema.enum.TEMPLATE),
  template: CustomTemplateSchema,
  readme: iStringSchema.nullish(),
  releases: z.array(TemplateRepoReleaseSchema),
});
export type BikafileTemplateData = z.infer<typeof BikafileTemplateDataSchema>;

export const BikafileResourcesDataSchema = z.object({
  format: z.literal(BikafileFormatSchema.enum.RESOURCES),
  // Read from resources.json
  resources: z.array(NodeResourceSchema),
});
export type BikafileResourcesData = z.infer<typeof BikafileResourcesDataSchema>;

export const BikafileDataSchema = z.discriminatedUnion('format', [
  BikafileTemplateDataSchema,
  BikafileResourcesDataSchema,
]);
export type BikafileData = z.infer<typeof BikafileDataSchema>;

export const BikafileMetadataSchema = z.object({
  format: BikafileFormatSchema,
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});
export type BikafileMetadata = z.infer<typeof BikafileMetadataSchema>;
