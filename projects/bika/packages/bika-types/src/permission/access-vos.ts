import { BasePaginationInfoSchema } from 'basenext/pagination';
import { z } from 'zod';
import { AccessPrivilegeSchema } from './bo';
import { NodeResourceTypeSchema } from '../node/base';
import { UnitVOSchema, MemberVOSchema } from '../unit/vo';

// Resource sharing scope
// DEFAULT: Default mode, no restrictions. Internal users can access and have FULL_ACCESS, external users cannot access.
export const DefaultShareScope = z.literal('DEFAULT');
// MEMBERS: Read-only mode for organization members. When specific members are restricted, their permissions will be merged.
// export const MemberShareScope = z.literal('MEMBERS');
// GUESTS: Guest mode. Everyone except internal members can read. External users do not need to log in but can only view, not edit.
// export const GuestShareScope = z.literal('GUESTS');

// PUBLIC_READ: Publicly readable, accessible to external users.
export const PublicReadShareScope = z.literal('PUBLIC_READ');
// PUBLIC_READ_WRITE: Publicly editable, external users must log in to perform edit operations.
export const PublicReadWriteShareScope = z.literal('PUBLIC_READ_WRITE');
// ANONYMOUS_READ_WRITE: Anonymously editable.
export const AnonymousReadWriteShareScope = z.literal('ANONYMOUS_READ_WRITE');

export const NodeShareScopeSchema = z.union([
  DefaultShareScope,
  PublicReadShareScope,
  PublicReadWriteShareScope,
  AnonymousReadWriteShareScope,
]);
export type NodeShareScope = z.infer<typeof NodeShareScopeSchema>;

// Collaborator permission set (unit id -> privilege)
export const UnitPrivilegeSchema = z.object({
  unitId: z.string(),
  privilege: AccessPrivilegeSchema,
});
export type UnitPrivilege = z.infer<typeof UnitPrivilegeSchema>;

// Node sharing information
export const NodeShareVOSchema = z.object({
  id: z.string().describe('Node resource ID'),
  nodeType: NodeResourceTypeSchema,
  shareScope: NodeShareScopeSchema.describe('Node sharing scope'),
  password: z.string().nullable().describe('Sharing password'),
  hasAssignedUnit: z.boolean().describe('Whether permissions have been assigned'),
  units: z.array(UnitVOSchema).describe('List of organizational units'),
  memberCount: z.number().describe('Total number of members'),
  privileges: z.array(UnitPrivilegeSchema).describe('List of collaborator permissions'),
  shortURL: z.string().nullable().describe('Short link'),
});
export type NodeShareVO = z.infer<typeof NodeShareVOSchema>;

// Collaborator information
export const CollaboratorVOSchema = MemberVOSchema.extend({
  privilege: AccessPrivilegeSchema,
  teams: z.array(z.string()).describe('List of team names the member belongs to'),
  roles: z.array(z.string()).describe('List of role names the member belongs to'),
});
export type CollaboratorVO = z.infer<typeof CollaboratorVOSchema>;

export const CollaboratorPaginationVOSchema = BasePaginationInfoSchema.extend({
  data: z.array(CollaboratorVOSchema),
});
export type CollaboratorPaginationVO = z.infer<typeof CollaboratorPaginationVOSchema>;
