export const html = `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>{{title}}</title>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
      rel="stylesheet"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet"/>
    <!-- ECharts CDN -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
      body,
      html {
        width: 1366px;
        height: 768px;
        margin: 0;
        padding: 0;
        overflow: hidden;
        font-family: "Inter", sans-serif;
        color: var(--color-text);
        background-color: var(--color-background);
      }

      /* Adaptive height mode - removed, use CSS Grid auto-sizing instead */
      body.adaptive-height {
        height: 768px;
      }

      body.adaptive-height .echarts-slide {
        height: 768px;
      }
      :root {
        --color-primary: {{color.primary}};
        --color-secondary: {{color.secondary}};
        --color-text: {{color.text}};
        --color-accent: {{color.accent}};
        --color-background: {{color.background}};
        --color-surface: {{color.surface}};
        --color-card: {{color.card}};
      }

      /* Common utility classes */
      .text-primary {
        color: var(--color-text);
      }
      .text-accent {
        color: var(--color-accent);
      }
      .bg-surface {
        background-color: var(--color-surface);
      }
      .bg-card {
        background-color: var(--color-card);
      }
      .bg-primary {
        background-color: var(--color-primary);
      }
      .bg-secondary {
        background-color: var(--color-secondary);
      }
      .border-primary {
        border-color: var(--color-primary);
      }

      /* ECharts slide container */
      .echarts-slide {
        width: 1366px;
        height: 768px;
        position: relative;
        background-color: var(--color-background);
        padding: 40px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
      }

      /* Background decorative elements */
      .slide-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
          radial-gradient(circle at 15% 85%, var(--color-accent)08 0%, transparent 50%),
          radial-gradient(circle at 85% 15%, var(--color-primary)06 0%, transparent 50%);
        pointer-events: none;
      }

      /* Header section */
      .slide-header {
        position: relative;
        z-index: 2;
        text-align: center;
        margin-bottom: 2rem;
      }

      .slide-title {
        font-size: 2.25rem;
        font-weight: 700;
        color: var(--color-accent);
        margin: 0 0 1rem 0;
        line-height: 1.2;
      }

      .slide-subtitle {
        font-size: 1.1rem;
        color: var(--color-text);
        opacity: 0.8;
        margin: 0;
        line-height: 1.4;
      }

      /* Charts container - responsive grid layout */
      .charts-container {
        position: relative;
        z-index: 2;
        flex: 1;
        display: grid;
        gap: 1.5rem;
        min-height: 0;
      }

      /* Single chart layout */
      .charts-container[data-count="1"] {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr;
      }

      /* Two charts layout */
      .charts-container[data-count="2"] {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr;
      }

      /* Three charts layout - 2 on top, 1 below */
      .charts-container[data-count="3"] {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr 1fr;
      }

      .charts-container[data-count="3"] .chart-item:nth-child(3) {
        grid-column: 1 / -1;
      }

      /* Four charts layout - 2x2 grid */
      .charts-container[data-count="4"] {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr 1fr;
      }

      /* Five or more charts layout - adaptive grid */
      .charts-container[data-count="5"],
      .charts-container[data-count="6"] {
        grid-template-columns: 1fr 1fr 1fr;
        grid-template-rows: repeat(2, 1fr);
      }

      .charts-container[data-count="7"],
      .charts-container[data-count="8"] {
        grid-template-columns: 1fr 1fr 1fr 1fr;
        grid-template-rows: repeat(2, 1fr);
      }

      /* Adaptive height for 3+ charts - auto fit within available space */
      .charts-container[data-count="3"] .chart-item,
      .charts-container[data-count="4"] .chart-item {
        min-height: 160px;
      }

      .charts-container[data-count="5"] .chart-item,
      .charts-container[data-count="6"] .chart-item {
        min-height: 140px;
      }

      .charts-container[data-count="7"] .chart-item,
      .charts-container[data-count="8"] .chart-item {
        min-height: 120px;
      }

      /* Auto-fit chart content for many charts */
      .charts-container[data-count="3"] .chart-content,
      .charts-container[data-count="4"] .chart-content {
        min-height: 120px;
      }

      .charts-container[data-count="5"] .chart-content,
      .charts-container[data-count="6"] .chart-content {
        min-height: 100px;
      }

      .charts-container[data-count="7"] .chart-content,
      .charts-container[data-count="8"] .chart-content {
        min-height: 80px;
      }

      /* Chart item styling */
      .chart-item {
        background-color: var(--color-card);
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid var(--color-primary);
        border-opacity: 0.1;
        position: relative;
        min-height: 200px;
        display: flex;
        flex-direction: column;
        transition: all 0.3s ease;
      }

      .chart-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 32px rgba(0,0,0,0.12);
        border-opacity: 0.2;
      }

      /* Chart header */
      .chart-header {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        flex-shrink: 0;
      }

      .chart-icon {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        background: linear-gradient(135deg, var(--color-primary)22, var(--color-accent)22);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--color-primary);
        font-size: 14px;
        margin-right: 0.75rem;
        flex-shrink: 0;
      }

      .chart-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--color-text);
        margin: 0;
        line-height: 1.3;
      }

      /* Chart container */
      .chart-content {
        flex: 1;
        min-height: 180px;
        position: relative;
      }

      /* Chart placeholder when no data */
      .chart-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--color-text);
        opacity: 0.5;
        font-size: 0.9rem;
        border: 2px dashed var(--color-primary);
        border-opacity: 0.2;
        border-radius: 8px;
      }

      /* Chart description */
      .chart-description {
        margin-top: 0.75rem;
        font-size: 0.85rem;
        color: var(--color-text);
        opacity: 0.7;
        line-height: 1.4;
      }

      /* Responsive adjustments for smaller chart counts */
      .charts-container[data-count="1"] .chart-item {
        padding: 2rem;
      }

      .charts-container[data-count="1"] .chart-content {
        min-height: 300px;
      }

      .charts-container[data-count="2"] .chart-content {
        min-height: 250px;
      }

      /* Loading animation */
      @keyframes chartLoading {
        0% { opacity: 0.5; }
        50% { opacity: 0.8; }
        100% { opacity: 0.5; }
      }

      .chart-loading {
        animation: chartLoading 2s infinite;
      }

      /* Adaptive height styles - simplified */
      .charts-container[data-count="3"] .chart-header,
      .charts-container[data-count="4"] .chart-header,
      .charts-container[data-count="5"] .chart-header,
      .charts-container[data-count="6"] .chart-header,
      .charts-container[data-count="7"] .chart-header,
      .charts-container[data-count="8"] .chart-header {
        margin-bottom: 0.75rem;
      }

      .charts-container[data-count="5"] .chart-icon,
      .charts-container[data-count="6"] .chart-icon,
      .charts-container[data-count="7"] .chart-icon,
      .charts-container[data-count="8"] .chart-icon {
        width: 28px;
        height: 28px;
        font-size: 12px;
      }

      .charts-container[data-count="5"] .chart-title,
      .charts-container[data-count="6"] .chart-title,
      .charts-container[data-count="7"] .chart-title,
      .charts-container[data-count="8"] .chart-title {
        font-size: 1rem;
      }

      .charts-container[data-count="7"] .chart-description,
      .charts-container[data-count="8"] .chart-description {
        font-size: 0.8rem;
        margin-top: 0.5rem;
      }
    </style>
  </head>
  <body>
    <div class="echarts-slide">
      <!-- Background decorative elements -->
      <div class="slide-background"></div>

      <!-- Header section -->
      <div class="slide-header">
        <h1 class="slide-title">{{title}}</h1>
        {{#if subtitle}}
        <p class="slide-subtitle">{{subtitle}}</p>
        {{/if}}
      </div>

      <!-- Charts container with dynamic count attribute -->
      <div class="charts-container" data-count="{{charts.length}}">
        {{#each charts}}
        <div class="chart-item">
          <!-- Chart header -->
          <div class="chart-header">
            {{#if icon}}
            <div class="chart-icon">
              <i class="{{icon}}"></i>
            </div>
            {{/if}}
            <h3 class="chart-title">{{title}}</h3>
          </div>

          <!-- Chart content -->
          <div class="chart-content" id="chart-{{@index}}">
            {{#unless option}}
            <div class="chart-placeholder">
              <span>Chart configuration required</span>
            </div>
            {{/unless}}
          </div>

          <!-- Chart description -->
          {{#if description}}
          <div class="chart-description">{{description}}</div>
          {{/if}}
        </div>
        {{/each}}
      </div>
    </div>

    <script>
      // Initialize ECharts instances
      document.addEventListener('DOMContentLoaded', function() {
        const charts = {{{json charts}}};

        if (charts && Array.isArray(charts)) {
          charts.forEach((chartConfig, index) => {
            const chartContainer = document.getElementById(\`chart-\${index}\`);
            if (chartContainer && chartConfig.option) {
              const chart = echarts.init(chartContainer);

              // Set default theme colors based on slide colors
              const defaultColors = [
                '{{color.primary}}',
                '{{color.accent}}',
                '{{color.secondary}}',
                'rgba({{color.primary}}, 0.7)',
                'rgba({{color.accent}}, 0.7)',
                'rgba({{color.secondary}}, 0.7)'
              ];

              // Merge default colors with chart option
              const option = {
                ...chartConfig.option,
                color: chartConfig.option.color || defaultColors,
                backgroundColor: 'transparent',
                textStyle: {
                  color: '{{color.text}}'
                }
              };

              chart.setOption(option);

              // Handle window resize
              window.addEventListener('resize', () => {
                chart.resize();
              });
            }
          });
        }
      });
    </script>
  </body>
</html>
`;
