export const html = `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>{{title}}</title>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
      rel="stylesheet"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet"/>
    <style>
      body,
      html {
        width: 1366px;
        height: 768px;
        margin: 0;
        padding: 0;
        overflow: hidden;
        font-family: "Inter", sans-serif;
        color: var(--color-text);
        background-color: var(--color-background);
      }
      :root {
        --color-primary: {{color.primary}};
        --color-secondary: {{color.secondary}};
        --color-text: {{color.text}};
        --color-accent: {{color.accent}};
        --color-background: {{color.background}};
        --color-surface: {{color.surface}};
        --color-card: {{color.card}};
      }

      /* Common utility classes */
      .text-primary {
        color: var(--color-text);
      }
      .text-accent {
        color: var(--color-accent);
      }
      .bg-surface {
        background-color: var(--color-surface);
      }
      .bg-card {
        background-color: var(--color-card);
      }
      .bg-primary {
        background-color: var(--color-primary);
      }
      .bg-secondary {
        background-color: var(--color-secondary);
      }
      .border-primary {
        border-color: var(--color-primary);
      }


      /* Generated CSS classes from inline styles */
      .fullscreen-container {
        width: 100%;
        height: 100vh;
        background: var(--color-background);
        color: var(--color-text);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 2rem;
        box-sizing: border-box;
        position: relative;
      }

      .absolute-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
          radial-gradient(circle at 20% 80%, var(--color-accent)10 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, var(--color-primary)08 0%, transparent 50%);
        pointer-events: none;
      }

      .relative-positioned {
        position: relative;
        z-index: 2;
        max-width: 900px;
        width: 100%;
        text-align: center;
      }

      .title-text {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--color-accent);
        margin: 0 0 2rem 0;
        line-height: 1.2;
      }

      .dynamic-style-4 {
        position: relative;
        width: 100%;
        aspect-ratio: 16/9;
        background: #000;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        border: 2px solid var(--color-accent)30;
      }

      .no-border {
        width: 100%;
        height: 100%;
        border: none;
      }

      .cover-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .dynamic-style-7 {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--color-text);
        font-size: 1.2rem;
      }

      .dynamic-style-8 {
        margin-top: 2rem;
        padding: 1.5rem;
        background: var(--color-surface);
        border-radius: 12px;
        border-left: 4px solid var(--color-accent);
      }

      .dynamic-style-9 {
        font-size: 1.1rem;
        line-height: 1.6;
        margin: 0;
        color: var(--color-text);
      }

</style>
  </head>
  <body>
    <div class="video-slide fullscreen-container">

      <!-- Background decorative elements -->
      <div class="absolute-overlay"></div>

      <div class="relative-positioned">

        {{#if title}}
        <h1 class="title-text">{{title}}</h1>
        {{/if}}

        <div class="video-container dynamic-style-4">

          {{#if url}}
            {{#if (contains url "youtube.com")}}
              <iframe
                src="{{url}}"
                class="no-border"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen>
              </iframe>
            {{else if (contains url "vimeo.com")}}
              <iframe
                src="{{url}}"
                class="no-border"
                allow="autoplay; fullscreen; picture-in-picture"
                allowfullscreen>
              </iframe>
            {{else}}
              <video
                class="cover-image"
                controls
              >
                <source src="{{url}}" type="video/mp4">
                Your browser does not support the video tag.
              </video>
            {{/if}}
          {{else}}
            <div class="dynamic-style-7">
              Video URL not provided
            </div>
          {{/if}}
        </div>

        {{#if description}}
        <div class="dynamic-style-8">
          <p class="dynamic-style-9">{{description}}</p>
        </div>
        {{/if}}
      </div>
    </div>
  </body>
</html>
`;
