export const html = `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>{{title}}</title>
    <!-- Font Awesome CDN -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
      rel="stylesheet"
    />
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- D3.js CDN for data visualization -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <!-- Google Fonts - Inter and Noto Sans SC -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet"/>

    <style>
      body,
      html {
        width: 1366px;
        height: 768px;
        margin: 0;
        padding: 0;
        overflow: hidden;
        font-family: "Inter", "Noto Sans SC", sans-serif;
        color: var(--color-text);
        background-color: var(--color-background);
      }

      :root {
        --color-primary: {{color.primary}};
        --color-secondary: {{color.secondary}};
        --color-text: {{color.text}};
        --color-accent: {{color.accent}};
        --color-background: {{color.background}};
        --color-surface: {{color.surface}};
        --color-card: {{color.card}};
      }

      /*
      Color scheme references are now dynamically provided through template variables.
      All colors are accessible via var(--color-primary), var(--color-secondary), etc.
      */

      .slide-container {
        width: 1366px;
        height: 768px;
        position: relative;
        background-color: var(--color-background);
        padding: 40px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
      }

      /*
      Industry card animation options:

      Option 1 - Lift and shadow (current):
      .industry-card:hover { transform: translateY(-5px); box-shadow: 0 15px 35px var(--color-primary)22; }

      Option 2 - Scale and glow:
      .industry-card:hover { transform: scale(1.02); box-shadow: 0 0 30px var(--color-primary)33; }

      Option 3 - Tilt perspective:
      .industry-card:hover { transform: perspective(1000px) rotateX(3deg) translateY(-5px); }

      Option 4 - Border highlight:
      .industry-card:hover { border-left-width: 8px; transform: translateX(4px); }
      */
      .industry-card {
        height: 420px;
        transition: all 0.3s ease;
        cursor: pointer;
      }

      .industry-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px var(--color-primary)22;
      }

      /*
      Icon container style options:

      Option 1 - Circle (current):
      border-radius: 50%;

      Option 2 - Rounded square:
      border-radius: 12px;

      Option 3 - Hexagon:
      clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
      */
      .icon-container {
        height: 60px;
        width: 60px;
        border-radius: 50%;
      }

      /*
      Metric visualization container options:

      Option 1 - Fixed height (current):
      height: 120px;

      Option 2 - Flexible height:
      min-height: 100px; height: auto;

      Option 3 - Aspect ratio:
      aspect-ratio: 2/1; height: auto;
      */
      .metric-container {
        height: 120px;
      }

      /* Common utility classes */
      .text-primary {
        color: var(--color-text);
      }
      .text-accent {
        color: var(--color-accent);
      }
      .bg-surface {
        background-color: var(--color-surface);
      }
      .bg-card {
        background-color: var(--color-card);
      }
      .bg-primary {
        background-color: var(--color-primary);
      }
      .bg-secondary {
        background-color: var(--color-secondary);
      }
      .border-primary {
        border-color: var(--color-primary);
      }


      /* Generated CSS classes from inline styles */
      .border-left-themed {
        background-color: {{this.bottomSection.achievement.bgColor}};
        border-left-color: {{this.bottomSection.achievement.borderColor}};
      }

      .border-top-themed {
        border-top-color: {{this.borderColor}};
      }

      .bg-themed-light {
        background-color: {{this.borderColor}};
      }

      .bg-themed {
        background-color: {{this.solutionHighlight.bgColor}};
      }

      .dynamic-style-4 {
        color: {{this.iconColor}};
      }

      .dynamic-style-8 {
        color: {{this.solutionHighlight.titleColor}};
      }

      .dynamic-style-9 {
        color: {{this.color}};
      }

      .dynamic-style-11 {
        color: {{this.bottomSection.achievement.iconColor}};
      }

</style>
  </head>
  <body>
    <div class="slide-container">
      <!-- Header Section -->
      <div class="mb-8">
        <h1 class="text-4xl font-bold text-primary mb-2">{{pageTitle}}</h1>
        <p class="text-primary opacity-80">{{pageSubtitle}}</p>
      </div>

      <!-- Industry Cases Section -->
      <!--
      Industry cases layout options:

      Option 1 - Equal flex columns (current):
      <div class="flex-1 flex justify-between space-x-5">

      Option 2 - Grid layout:
      <div class="flex-1 grid grid-cols-3 gap-5">

      Option 3 - Variable width:
      <div class="flex-1 flex space-x-5">

      Option 4 - Responsive grid:
      <div class="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-5">
      -->
      <div class="flex-1 flex justify-between space-x-5">
        {{#each industryCases}}
        <!--
        Industry card style options:

        Option 1 - Left border accent (current):
        <div class="industry-card flex-1 bg-surface rounded-lg shadow-sm p-5 flex flex-col border-l-4 border-left-themed">

        Option 2 - Top border accent:
        <div class="industry-card flex-1 bg-surface rounded-lg shadow-sm p-5 flex flex-col border-t-4 border-top-themed">

        Option 3 - Colored background:
        <div class="industry-card flex-1 rounded-lg shadow-sm p-5 flex flex-col bg-themed-light">

        Option 4 - Gradient background:
        <div class="industry-card flex-1 bg-gradient-to-br from-[var(--color-surface)] to-[{{this.borderColor}}]11 rounded-lg shadow-sm p-5 flex flex-col">
        -->
        <div class="industry-card flex-1 bg-card border   rounded-lg shadow-sm p-5 flex flex-col border-l-4 border-left-themed">
          <!-- Card Header -->
          <div class="flex items-center mb-4">
            <div class="icon-container flex items-center justify-center mr-4 bg-themed">
              <i class="{{this.iconClass}} text-3xl dynamic-style-4"></i>
            </div>
            <h2 class="text-2xl font-bold text-primary">{{this.title}}</h2>
          </div>

          <!-- Content Section -->
          <div class="flex-1">
            <!-- Description -->
            <p class="text-primary opacity-80 mb-4">{{this.description}}</p>

            <!-- Solution Highlight -->
            <!--
            Solution highlight style options:

            Option 1 - Colored background box (current):
            <div class="p-4 rounded-lg mb-4 bg-themed">

            Option 2 - Left border accent:
            <div class="border-l-4 pl-4 py-3 mb-4 border-left-themed">

            Option 3 - Card style:
            <div class="bg-card border   rounded-lg p-4 mb-4 shadow-sm">

            Option 4 - Quote style:
            <blockquote class="border-l-4 pl-4 py-2 mb-4 italic border-left-themed">
            -->
            <div class="p-4 rounded-lg mb-4 bg-themed">
              <h3 class="font-bold mb-2 dynamic-style-8">{{this.solutionHighlight.title}}</h3>
              <p class="text-primary opacity-90">{{this.solutionHighlight.content}}</p>
            </div>

            <!-- Bottom Section Content -->
            {{#eq this.bottomSection.type 'advantages'}}
            <!-- Advantages List -->
            <div class="mt-4">
              <h3 class="font-bold text-primary opacity-90 mb-2">{{this.bottomSection.title}}</h3>
              <!--
              Advantages list style options:

              Option 1 - Vertical list (current):
              <ul class="space-y-2">

              Option 2 - Compact list:
              <ul class="space-y-1">

              Option 3 - Grid layout:
              <div class="grid grid-cols-1 gap-2">
              -->
              <ul class="space-y-2">
                {{#each this.bottomSection.items}}
                <li class="flex items-start">
                  <i class="{{this.iconClass}} mt-1 mr-2 dynamic-style-4"></i>
                  <span class="text-primary opacity-90">{{this.text}}</span>
                </li>
                {{/each}}
              </ul>
            </div>
            {{/eq}}

            {{#eq this.bottomSection.type 'metrics'}}
            <!-- Metrics Visualization -->
            <div class="metric-container mt-4" id="{{this.bottomSection.containerId}}">
              <!-- D3.js visualization will be inserted here -->
            </div>
            {{/eq}}

            {{#eq this.bottomSection.type 'stats'}}
            <!-- Statistics Cards -->
            <!--
            Statistics layout options:

            Option 1 - Flex wrap (current):
            <div class="flex flex-wrap mt-4 mb-4">

            Option 2 - Grid layout:
            <div class="grid grid-cols-2 gap-2 mt-4 mb-4">

            Option 3 - Horizontal layout:
            <div class="flex space-x-2 mt-4 mb-4">
            -->
            <div class="flex flex-wrap mt-4 mb-4">
              {{#each this.bottomSection.stats}}
              <div class="{{#if @last}}w-full{{else}}w-1/2{{/if}} p-2">
                <div class="bg-surface rounded p-3 h-full flex flex-col items-center justify-center">
                  <span class="text-3xl font-bold dynamic-style-9">{{this.value}}</span>
                  <span class="text-sm text-primary opacity-70 text-center">{{this.label}}</span>
                </div>
              </div>
              {{/each}}
            </div>

            <!-- Achievement Badge -->
            {{#if this.bottomSection.achievement}}
            <!--
            Achievement style options:

            Option 1 - Left border accent box (current):
            <div class="p-3 rounded-lg border-l-4 border-left-themed">

            Option 2 - Card with icon:
            <div class="bg-surface border rounded-lg p-3 flex items-center">

            Option 3 - Badge style:
            <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium">
            -->
            <div class="mt-4">
              <div class="p-3 rounded-lg border-l-4 border-left-themed">
                <p class="text-primary opacity-90">
                  <i class="{{this.bottomSection.achievement.iconClass}} mr-2 dynamic-style-11"></i>
                  {{this.bottomSection.achievement.text}}
                </p>
              </div>
            </div>
            {{/if}}
            {{/eq}}
          </div>
        </div>
        {{/each}}
      </div>
    </div>

    <script>
      // D3.js visualization for metrics
      document.addEventListener('DOMContentLoaded', function() {
        {{#each industryCases}}
        {{#eq this.bottomSection.type 'metrics'}}
        (function() {
          const container = d3.select("#{{this.bottomSection.containerId}}");
          if (container.empty()) return;

          const width = container.node().getBoundingClientRect().width;
          const height = 120;

          // Create SVG
          const svg = container.append("svg")
            .attr("width", width)
            .attr("height", height);

          // Data from schema
          const data = [
            {{#each this.bottomSection.data}}
            { label: "{{this.label}}", value: {{this.value}}, unit: "{{this.unit}}", color: "{{this.color}}" }{{#unless @last}},{{/unless}}
            {{/each}}
          ];

          // Create scales
          const xScale = d3.scaleBand()
            .domain(data.map(d => d.label))
            .range([40, width - 40])
            .padding(0.4);

          const yScale = d3.scaleLinear()
            .domain([0, d3.max(data, d => d.value) * 1.2])
            .range([height - 30, 20]);

          // Create bars
          const bars = svg.selectAll(".bar")
            .data(data)
            .enter()
            .append("g");

          // Add rectangles
          bars.append("rect")
            .attr("x", d => xScale(d.label))
            .attr("y", d => yScale(d.value))
            .attr("width", xScale.bandwidth())
            .attr("height", d => height - 30 - yScale(d.value))
            .attr("rx", 4)
            .attr("fill", d => d.color);

          // Add value labels
          bars.append("text")
            .attr("x", d => xScale(d.label) + xScale.bandwidth() / 2)
            .attr("y", d => yScale(d.value) - 10)
            .attr("text-anchor", "middle")
            .attr("font-size", "16px")
            .attr("font-weight", "bold")
            .attr("fill", "var(--color-text)")
            .text(d => d.value + d.unit);

          // Add labels
          bars.append("text")
            .attr("x", d => xScale(d.label) + xScale.bandwidth() / 2)
            .attr("y", height - 10)
            .attr("text-anchor", "middle")
            .attr("font-size", "12px")
            .attr("fill", "var(--color-text)")
            .attr("opacity", "0.8")
            .text(d => d.label);
        })();
        {{/eq}}
        {{/each}}
      });
    </script>
  </body>
</html>
`;
