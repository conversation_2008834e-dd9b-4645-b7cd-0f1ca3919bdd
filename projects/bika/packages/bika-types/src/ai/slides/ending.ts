export const html = `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>{{title}}</title>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
      rel="stylesheet"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet"/>
    <style>
      body,
      html {
        width: 1366px;
        height: 768px;
        margin: 0;
        padding: 0;
        overflow: hidden;
        font-family: "Inter", sans-serif;
        color: var(--color-text);
        background-color: var(--color-background);
      }
      :root {
        --color-primary: {{color.primary}};
        --color-secondary: {{color.secondary}};
        --color-text: {{color.text}};
        --color-accent: {{color.accent}};
        --color-background: {{color.background}};
        --color-surface: {{color.surface}};
        --color-card: {{color.card}};
      }

      /* Common utility classes */
      .text-primary {
        color: var(--color-text);
      }
      .text-accent {
        color: var(--color-accent);
      }
      .bg-surface {
        background-color: var(--color-surface);
      }
      .bg-card {
        background-color: var(--color-card);
      }
      .bg-primary {
        background-color: var(--color-primary);
      }
      .bg-secondary {
        background-color: var(--color-secondary);
      }
      .border-primary {
        border-color: var(--color-primary);
      }


      /* Generated CSS classes from inline styles */
      .fullscreen-container {
        width: 100%;
        height: 100vh;
        background: var(--color-background);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        position: relative;
        overflow: hidden;
      }

      .absolute-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 30% 40%, var(--color-accent)15 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, var(--color-primary)10 0%, transparent 50%),
                    radial-gradient(circle at 40% 80%, var(--color-secondary)08 0%, transparent 50%);
        pointer-events: none;
      }

      .relative-positioned {
        position: relative;
        z-index: 2;
        max-width: 600px;
        padding: 0 2rem;
      }

      .title-text {
        font-size: 1.5rem;
        font-weight: 400;
        color: var(--color-text);
        margin: 0 0 2rem 0;
        opacity: 0.8;
      }

      .dynamic-style-5 {
        font-size: 1.1rem;
        color: var(--color-text);
        margin: 0 0 3rem 0;
        line-height: 1.6;
        opacity: 0.9;
      }

      .dynamic-style-6 {
        position: absolute;
        bottom: 2rem;
        right: 2rem;
        width: 100px;
        height: 4px;
        background: linear-gradient(90deg, var(--color-accent), var(--color-primary));
        border-radius: 2px;
        opacity: 0.6;
      }

</style>
  </head>
  <body>
    <div class="fullscreen-container">
      <!-- Background decoration -->
      <div class="absolute-overlay"></div>

      <!-- Main content -->
      <div class="relative-positioned">
        <!-- Title -->
        <h1 class="title-text">{{title}}</h1>

        <!-- Subtitle -->
        {{#if subtitle}}
        <h2 class="title-text">{{subtitle}}</h2>
        {{/if}}

        <!-- Message -->
        {{#if message}}
        <p class="dynamic-style-5">{{message}}</p>
        {{/if}}
      </div>

      <!-- Decorative elements -->
      <div class="dynamic-style-6"></div>
    </div>
  </body>
</html>
`;
