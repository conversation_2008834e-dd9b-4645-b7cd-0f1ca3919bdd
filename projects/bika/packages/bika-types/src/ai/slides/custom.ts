export const html = `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>{{title}}</title>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
      rel="stylesheet"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet"/>
    <style>
      body,
      html {
        width: 1366px;
        height: 768px;
        margin: 0;
        padding: 0;
        overflow: hidden;
        font-family: "Inter", sans-serif;
        color: var(--color-text);
        background-color: var(--color-background);
      }
      :root {
        --color-primary: {{color.primary}};
        --color-secondary: {{color.secondary}};
        --color-text: {{color.text}};
        --color-accent: {{color.accent}};
        --color-background: {{color.background}};
        --color-surface: {{color.surface}};
        --color-card: {{color.card}};
      }

      /* Common utility classes */
      .text-primary {
        color: var(--color-text);
      }
      .text-accent {
        color: var(--color-accent);
      }
      .bg-surface {
        background-color: var(--color-surface);
      }
      .bg-card {
        background-color: var(--color-card);
      }
      .bg-primary {
        background-color: var(--color-primary);
      }
      .bg-secondary {
        background-color: var(--color-secondary);
      }
      .border-primary {
        border-color: var(--color-primary);
      }


      /* Generated CSS classes from inline styles */
      .fullscreen-container {
        width: 100%;
        height: 100vh;
        background: var(--color-background);
        color: var(--color-text);
        padding: 2rem;
        box-sizing: border-box;
        position: relative;
      }

      .text-center-styled {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: {{#if title}}vh{{else}}vh{{/if}};
        text-align: center;
      }

      .title-text {
        font-size: 1.8rem;
        font-weight: 600;
        color: var(--color-accent);
        margin-bottom: 1rem;
      }

      .dynamic-style-4 {
        max-width: 800px;
        width: 100%;
      }

      .dynamic-style-5 {
        margin-bottom: 2rem;
      }

      .dynamic-style-7 {
        font-size: 1.1rem;
        line-height: 1.6;
        color: var(--color-text);
      }

      .dynamic-style-8 {
        width: 100%;
        height: 100%;
      }

      .dynamic-style-9 {
        font-size: 1.2rem;
        color: var(--color-text);
        opacity: 0.7;
      }

</style>
  </head>
  <body>
    <div class="custom-page fullscreen-container">

      {{#if title}}
      <div class="text-center-styled">
        <h1 class="title-text">{{title}}</h1>
      </div>
      {{/if}}

      <div class="text-center-styled">
        {{#if data}}
          <div class="dynamic-style-4">
            {{#each data}}
              <div class="dynamic-style-5">
                {{#if this.title}}
                  <h2 class="title-text">{{this.title}}</h2>
                {{/if}}
                {{#if this.content}}
                  <div class="dynamic-style-7">{{this.content}}</div>
                {{/if}}
              </div>
            {{/each}}
          </div>
        {{else if page}}
          <div class="dynamic-style-8">
            {{{page}}}
          </div>
        {{else}}
          <div class="dynamic-style-9">
            Custom content will be displayed here
          </div>
        {{/if}}
      </div>
    </div>
  </body>
</html>
`;
