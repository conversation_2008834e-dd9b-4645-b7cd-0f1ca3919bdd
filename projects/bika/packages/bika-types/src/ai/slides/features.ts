export const html = `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>{{title}}</title>
    <!-- Font Awesome CDN -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
      rel="stylesheet"
    />
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- ECharts CDN for advanced charts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- Google Fonts - Inter and Noto Sans SC -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet"/>

    <style>
      body,
      html {
        width: 1366px;
        height: 768px;
        margin: 0;
        padding: 0;
        overflow: hidden;
        font-family: "Inter", "Noto Sans SC", sans-serif;
        color: var(--color-text);
        background-color: var(--color-background);
      }

      :root {
        --color-primary: {{color.primary}};
        --color-secondary: {{color.secondary}};
        --color-text: {{color.text}};
        --color-accent: {{color.accent}};
        --color-background: {{color.background}};
        --color-surface: {{color.surface}};
        --color-card: {{color.card}};
      }

      /*
      Color scheme references are now dynamically provided through template variables.
      All colors are accessible via var(--color-primary), var(--color-secondary), etc.
      */

      .slide-container {
        width: 1366px;
        height: 768px;
        position: relative;
        background-color: var(--color-background);
        padding: 40px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
      }

      /*
      Component card animation options:

      Option 1 - Lift on hover:
      .component-card:hover { transform: translateY(-5px); box-shadow: 0 10px 25px var(--color-primary)22; }

      Option 2 - Scale on hover:
      .component-card:hover { transform: scale(1.02); }

      Option 3 - Glow on hover:
      .component-card:hover { box-shadow: 0 0 20px var(--color-primary)33; }
      */
      .component-card {
        transition: all 0.3s ease;
        cursor: pointer;
      }

      .component-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px var(--color-primary)22;
      }

      /*
      Icon container style options:

      Option 1 - Circle (current):
      border-radius: 50%;

      Option 2 - Rounded square:
      border-radius: 12px;

      Option 3 - Hexagon:
      clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
      */
      .icon-container {
        height: 60px;
        width: 60px;
        border-radius: 50%;
        flex-shrink: 0;
      }

            .growth-chart {
        width: 160px;
        height: 160px;
        flex-shrink: 0;
      }

      /*
      Chart container style options:

      Option 1 - Centered with subtle background:
      .growth-chart { background: linear-gradient(135deg, var(--color-primary)05, var(--color-accent)05); border-radius: 50%; }

      Option 2 - Card-style container:
      .growth-chart { background: var(--color-surface); border: 2px solid var(--color-primary)22; border-radius: 12px; padding: 10px; }

      Option 3 - Minimal style (current):
      Simple container without additional styling
      */

      /*
      Border accent style options:

      Option 1 - Left border (current):
      border-left: 4px solid;

      Option 2 - Top border:
      border-top: 4px solid;

      Option 3 - Full border:
      border: 2px solid;

      Option 4 - Corner accent:
      position: relative;
      &::before { content: ''; position: absolute; top: 0; left: 0; width: 20px; height: 20px; background: color; }
      */

      /* Common utility classes */
      .text-primary {
        color: var(--color-text);
      }
      .text-accent {
        color: var(--color-accent);
      }
      .bg-surface {
        background-color: var(--color-surface);
      }
      .bg-card {
        background-color: var(--color-card);
      }
      .bg-primary {
        background-color: var(--color-primary);
      }
      .bg-secondary {
        background-color: var(--color-secondary);
      }
      .border-primary {
        border-color: var(--color-primary);
      }


      /* Generated CSS classes from inline styles */
      .border-themed {
        background-color: {{this.borderColor}};
        border-color: {{this.borderColor}};
      }

      .border-left-themed {
        border-left-color: {{this.borderColor}};
      }

      .bg-themed {
        background-color: {{this.iconBgColor}};
      }

      .dynamic-style-4 {
        color: {{this.iconColor}};
      }

      .metric-padding {
        padding:8px;
      }

      .dynamic-style-6 {
        color:var(--color-primary);
        font-weight:bold;
      }

      .dynamic-style-7 {
        color:var(--color-text);
        opacity:0.8;
        font-size:12px;
      }

</style>
  </head>
  <body>
    <div class="slide-container">
      <!-- Header Section -->
      <div class="mb-6">
        <h1 class="text-4xl font-bold text-primary mb-3">{{pageTitle}}</h1>
        <p class="text-xl text-primary opacity-80">{{pageSubtitle}}</p>
      </div>

      <!-- Main Content Container -->
      <div class="flex-1 flex">
        <!-- Left Section: Components -->
        <div class="w-1/2 pr-6">
          <h2 class="text-2xl font-semibold text-primary mb-4 flex items-center">
            <i class="{{leftColumn.sectionIcon}} text-primary mr-2"></i>
            {{leftColumn.sectionTitle}}
          </h2>

          <div class="space-y-4">
            {{#each leftColumn.components}}
            <!--
            Component card style options:

            Option 1 - Left border accent (current):
            <div class="component-card bg-surface rounded-lg shadow-sm p-4 flex border-l-4 border-gray-200 border-themed">

            Option 2 - Top border accent:
            <div class="component-card bg-surface rounded-lg shadow-sm p-4 flex border-t-4 border-gray-200 border-themed">

            Option 3 - Colored background:
            <div class="component-card rounded-lg shadow-sm p-4 flex border border-gray-200 border-themed">>

            Option 4 - Simple card:
            <div class="component-card bg-surface rounded-lg shadow-sm p-4 flex">
            -->
            <div class="component-card bg-card border   rounded-lg shadow-sm p-4 flex border-l-4 border-left-themed">
              <div class="icon-container flex items-center justify-center mr-4 bg-themed">
                <i class="{{this.iconClass}} text-2xl dynamic-style-4"></i>
              </div>
              <div>
                <h3 class="font-bold text-lg text-primary mb-1">{{this.title}}</h3>
                <p class="text-primary opacity-80 text-sm leading-relaxed">{{this.description}}</p>
              </div>
            </div>
            {{/each}}
          </div>
        </div>

        <!-- Right Section: Growth and AI Features -->
        <div class="w-1/2 pl-6">
          <!-- Growth Statistics -->
          <div class="mb-6">
            <h2 class="text-2xl font-semibold text-primary mb-4 flex items-center">
              <i class="{{rightColumn.growthSection.iconClass}} text-primary mr-2"></i>
              {{rightColumn.growthSection.title}}
            </h2>

            <!--
            Growth card style options:

            Option 1 - Side-by-side layout (current):
            <div class="bg-surface rounded-lg shadow-sm p-6 flex items-center">

            Option 2 - Centered layout:
            <div class="bg-surface rounded-lg shadow-sm p-6 text-center">

            Option 3 - Gradient background:
            <div class="bg-gradient-to-r from-[var(--color-primary)]11 to-[var(--color-accent)]11 rounded-lg shadow-sm p-6 flex items-center">
            -->
                        <!--
            Growth metric card style options:

            Option 1 - Standard card with chart:
            <div class="bg-surface rounded-lg shadow-sm p-6 flex items-center">

            Option 2 - Highlighted background:
            <div class="bg-gradient-to-r from-[var(--color-primary)]08 to-[var(--color-accent)]08 rounded-lg shadow-sm p-6 flex items-center">

            Option 3 - Border accent:
            <div class="bg-surface border-l-4 border-primary rounded-lg shadow-sm p-6 flex items-center">
            -->
            <div class="bg-card border   rounded-lg shadow-sm p-6">
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <p class="text-primary opacity-70 mb-2">{{rightColumn.growthSection.metric.label}}</p>
                  <div class="flex items-center mb-1">
                    <span class="text-4xl font-bold text-accent">{{rightColumn.growthSection.metric.value}}</span>
                    <span class="text-accent ml-2"><i class="fas fa-arrow-up"></i></span>
                  </div>
                  <p class="text-sm text-primary opacity-60">{{rightColumn.growthSection.metric.subLabel}}</p>
                  {{#if rightColumn.growthSection.metric.showChart}}
                  {{#if rightColumn.growthSection.metric.chartLabel}}
                  <p class="text-xs text-primary mt-2 font-medium">
                    <i class="fas fa-chart-pie mr-1"></i>
                    {{rightColumn.growthSection.metric.chartLabel}}
                  </p>
                  {{/if}}
                  {{/if}}
                </div>
                {{#if rightColumn.growthSection.metric.showChart}}
                <div class="ml-6">
                  <div class="growth-chart" id="growth-chart"></div>
                </div>
                {{/if}}
              </div>
            </div>
          </div>

          <!-- AI Capabilities -->
          <div>
            <h2 class="text-2xl font-semibold text-primary mb-4 flex items-center">
              <i class="{{rightColumn.aiSection.iconClass}} text-primary mr-2"></i>
              {{rightColumn.aiSection.title}}
            </h2>

            <!--
            AI section style options:

            Option 1 - Top border accent (current):
            <div class="bg-surface rounded-lg shadow-sm p-6 border-t-4 border-primary">

            Option 2 - Left border accent:
            <div class="bg-surface rounded-lg shadow-sm p-6 border-l-4 border-primary">

            Option 3 - Gradient background:
            <div class="bg-gradient-to-br from-[var(--color-primary)]11 to-[var(--color-secondary)]11 rounded-lg shadow-sm p-6">

            Option 4 - Card with icon header:
            <div class="bg-surface rounded-lg shadow-sm overflow-hidden">
                <div class="bg-primary p-4">
                    <h3 class="font-bold text-xl text-white">{{rightColumn.aiSection.mainFeature.name}}</h3>
                </div>
                <div class="p-6">...</div>
            </div>
            -->
            <div class="bg-card border   rounded-lg shadow-sm p-6 border-t-4 border-primary">
              <h3 class="font-bold text-xl text-primary mb-3">{{rightColumn.aiSection.mainFeature.name}}</h3>
              <p class="text-primary opacity-80 mb-4 leading-relaxed">{{rightColumn.aiSection.mainFeature.description}}</p>

              <!-- AI Capabilities Grid -->
              <!--
              Capabilities layout options:

              Option 1 - 2x2 grid (current):
              <div class="grid grid-cols-2 gap-3">

              Option 2 - Single column list:
              <div class="space-y-3">

              Option 3 - Horizontal row:
              <div class="flex flex-wrap gap-4">

              Option 4 - 2x2 with larger spacing:
              <div class="grid grid-cols-2 gap-6">
              -->
              <div class="grid grid-cols-2 gap-3">
                {{#each rightColumn.aiSection.capabilities}}
                <div class="flex items-center">
                  <i class="{{this.iconClass}} text-primary mr-2 flex-shrink-0"></i>
                  <span class="text-primary opacity-80 text-sm">{{this.text}}</span>
                </div>
                {{/each}}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer Note -->
      <!--
      Footer style options:

      Option 1 - Simple centered text (current):
      <div class="mt-6 text-center">

      Option 2 - Highlighted footer:
      <div class="mt-6 bg-primary11 rounded-lg p-3 text-center">

      Option 3 - Footer with icon:
      <div class="mt-6 text-center flex items-center justify-center">
          <i class="fas fa-lightbulb text-accent mr-2"></i>
          <p class="...">{{footerNote.text}}</p>
      </div>
      -->
      <div class="mt-6 text-center">
        <p class="text-primary opacity-70 {{#eq footerNote.style 'italic'}}italic{{/eq}}{{#eq footerNote.style 'bold'}}font-bold{{/eq}}">
          {{footerNote.text}}
        </p>
      </div>
    </div>

    {{#if rightColumn.growthSection.metric.showChart}}
    <script>
      // ECharts circular progress chart with enhanced styling
      document.addEventListener('DOMContentLoaded', function() {
        const chartContainer = document.getElementById('growth-chart');
        if (!chartContainer) return;

        // Initialize ECharts
        const chart = echarts.init(chartContainer);

        // Extract percentage from metric value
        const metricValue = '{{rightColumn.growthSection.metric.value}}';
        const percentage = parseFloat(metricValue.replace('%', ''));

        /*
        Chart style options:

        Option 1 - Gradient ring with glow:
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            {offset: 0, color: 'var(--color-primary)'},
            {offset: 1, color: 'var(--color-accent)'}
          ]),
          shadowBlur: 10,
          shadowColor: 'var(--color-primary)66'
        }

        Option 2 - Solid ring with animation:
        itemStyle: { color: 'var(--color-primary)' },
        animationType: 'scale',
        animationDuration: 2000

        Option 3 - Multi-color segments (current):
        Based on percentage ranges
        */

        const option = {
          series: [{
            type: 'pie',
            radius: ['45%', '70%'],
            center: ['50%', '50%'],
            startAngle: 90,
            data: [
              {
                value: percentage,
                name: 'Progress',
                itemStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                    {offset: 0, color: 'var(--color-primary)'},
                    {offset: 1, color: 'var(--color-accent)'}
                  ]),
                  shadowBlur: 8,
                  shadowColor: 'var(--color-primary)44'
                }
              },
              {
                value: 100 - percentage,
                name: 'Remaining',
                itemStyle: {
                  color: 'var(--color-primary)15',
                  borderColor: 'var(--color-primary)22',
                  borderWidth: 1
                },
                silent: true
              }
            ],
            label: {
              show: true,
              position: 'center',
              fontSize: 20,
              fontWeight: 'bold',
              color: 'var(--color-primary)',
              formatter: function() {
                return metricValue;
              }
            },
            labelLine: {
              show: false
            },
            animation: true,
            animationType: 'scale',
            animationDuration: 1500,
            animationEasing: 'elasticOut'
          }],
          tooltip: {
            trigger: 'item',
            formatter: function(params) {
              if (params.name === 'Progress') {
                const prefix = '{{rightColumn.growthSection.metric.tooltipPrefix}}' || 'Progress';
                return \`<div class="metric-padding">
                  <div class="dynamic-style-6">\${prefix} \${metricValue}</div>
                  <div class="dynamic-style-7">{{rightColumn.growthSection.metric.subLabel}}</div>
                </div>\`;
              }
              return '';
            },
            backgroundColor: 'var(--color-surface)',
            borderColor: 'var(--color-primary)',
            borderWidth: 1,
            textStyle: {
              color: 'var(--color-text)'
            }
          }
        };

        chart.setOption(option);

        // Responsive resize
        window.addEventListener('resize', function() {
          chart.resize();
        });
      });
    </script>
    {{/if}}
  </body>
</html>
`;
