export const html = `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>{{title}}</title>
    <!-- Font Awesome CDN -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
      rel="stylesheet"
    />
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- D3.js CDN for potential visualizations -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <!-- Google Fonts - Inter and Noto Sans SC -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet"/>

    <style>
      body,
      html {
        width: 1366px;
        height: 768px;
        margin: 0;
        padding: 0;
        overflow: hidden;
        font-family: "Inter", "Noto Sans SC", sans-serif;
        color: var(--color-text);
        background-color: var(--color-background);
      }

      :root {
        --color-primary: {{color.primary}};
        --color-secondary: {{color.secondary}};
        --color-text: {{color.text}};
        --color-accent: {{color.accent}};
        --color-background: {{color.background}};
        --color-surface: {{color.surface}};
        --color-card: {{color.card}};
      }

      /*
      Color scheme references are now dynamically provided through template variables.
      All colors are accessible via var(--color-primary), var(--color-secondary), etc.
      */

      .slide-container {
        width: 1366px;
        height: 768px;
        position: relative;
        background-color: var(--color-background);
        padding: 40px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
      }

      /*
      Pricing card animation options:

      Option 1 - Lift and shadow (current):
      .pricing-card:hover { transform: translateY(-5px); box-shadow: 0 15px 35px var(--color-primary)22; }

      Option 2 - Scale effect:
      .pricing-card:hover { transform: scale(1.02); }

      Option 3 - Tilt effect:
      .pricing-card:hover { transform: perspective(1000px) rotateX(3deg) translateY(-3px); }

      Option 4 - Border glow:
      .pricing-card:hover { box-shadow: 0 0 20px var(--color-primary)33; }
      */
      .pricing-card {
        transition: all 0.3s ease;
        cursor: pointer;
      }

      .pricing-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px var(--color-primary)22;
      }

      /*
      Icon background style options:

      Option 1 - Circle (current):
      border-radius: 50%;

      Option 2 - Rounded square:
      border-radius: 16px;

      Option 3 - Hexagon:
      clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
      */
      .icon-bg {
        height: 60px;
        border-radius: 50%; /* Current option */
        /* Uncomment the desired option below: */
        /* border-radius: 16px; */ /* Option 2 */
        /* clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%); */ /* Option 3 */
      }

      /* Common utility classes */
      .text-primary {
        color: var(--color-text);
      }
      .text-accent {
        color: var(--color-accent);
      }
      .bg-surface {
        background-color: var(--color-surface);
      }
      .bg-card {
        background-color: var(--color-card);
      }

      /*
      Pricing model pill animations:

      Option 1 - Scale (current):
      .model-pill:hover { transform: scale(1.05); }

      Option 2 - Lift:
      .model-pill:hover { transform: translateY(-2px); }

      Option 3 - Glow:
      .model-pill:hover { box-shadow: 0 0 15px var(--color-primary)33; }
      */
      .model-pill {
        transition: all 0.2s ease;
        cursor: pointer;
      }

      .model-pill:hover {
        transform: scale(1.05);
      }

      /*
      Evolution chart stage sizes:

      small: h-8 w-8
      medium: h-10 w-10
      large: h-12 w-12
      xlarge: h-14 w-14
      */

      /* Generated CSS classes from inline styles */
      .border-top-themed {
        border-top-color: {{this.borderColor}};
      }

      .border-left-themed {
        border-left-color: {{this.borderColor}};
      }

      .bg-themed-light {
        background-color: {{this.borderColor}};
      }

      .bg-themed {
        background-color: {{this.bgColor}};
      }

      .dynamic-style-4 {
        color: {{this.iconColor}};
      }

      .dynamic-style-8 {
        color: {{this.content.highlight.iconColor}};
      }

      .dynamic-style-10 {
        color: {{this.content.summary.iconColor}};
      }

    </style>
  </head>
  <body>
    <div class="slide-container">
      <!-- Header Section -->
      <div class="mb-6">
        <h1 class="text-4xl font-bold text-primary mb-2">{{pageTitle}}</h1>
        <p class="text-lg text-primary opacity-80">{{pageSubtitle}}</p>
      </div>

      <!-- Main Content - Pricing Strategies Grid -->
      <!--
      Grid layout options:

      Option 1 - 3 equal columns (current):
      <div class="grid grid-cols-3 gap-6 flex-1">

      Option 2 - Responsive grid:
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 flex-1">

      Option 3 - Flex layout:
      <div class="flex gap-6 flex-1">

      Option 4 - Variable column widths:
      <div class="grid grid-cols-12 gap-6 flex-1"> (with col-span-4 for each)
      -->
      <div class="grid grid-cols-3 gap-6 flex-1">
        {{#each pricingStrategies}}
        <!--
        Pricing card style options:

        Option 1 - Top border accent (current):
        <div class="pricing-card bg-surface rounded-lg shadow-sm p-6 border-t-4 flex flex-col border-top-themed">

        Option 2 - Left border accent:
        <div class="pricing-card bg-surface rounded-lg shadow-sm p-6 border-l-4 flex flex-col border-left-themed">

        Option 3 - Colored background:
        <div class="pricing-card rounded-lg shadow-sm p-6 flex flex-col bg-themed-light">

        Option 4 - Gradient header:
        <div class="pricing-card bg-surface rounded-lg shadow-sm overflow-hidden flex flex-col">
        -->
        <div class="pricing-card bg-card border   rounded-lg shadow-sm p-6 border-t-4 flex flex-col border-top-themed">
          <!-- Card Header -->
          <div class="flex items-center mb-4">
            <div class="icon-bg flex items-center justify-center mr-4 bg-themed">
              <i class="{{this.iconClass}} text-3xl dynamic-style-4"></i>
            </div>
            <h2 class="text-2xl font-bold text-primary">{{this.title}}</h2>
          </div>

          <!-- Description -->
          <p class="text-primary opacity-80 mb-4">{{this.description}}</p>

          <!-- Content Section -->
          {{#eq this.content.type 'pills'}}
          <!-- Pricing Model Pills -->
          <!--
          Pills layout options:

          Option 1 - Flex wrap (current):
          <div class="flex flex-wrap gap-3 mt-2">

          Option 2 - Grid layout:
          <div class="grid grid-cols-1 gap-3 mt-2">

          Option 3 - Vertical stack:
          <div class="space-y-3 mt-2">
          -->
          <div class="flex flex-wrap gap-3 mt-2">
            {{#each this.content.pills}}
            <div class="model-pill px-4 py-2 rounded-full flex items-center bg-themed">
              <i class="{{this.iconClass}} mr-2 dynamic-style-4"></i>
              <span class="text-primary opacity-90">{{this.text}}</span>
            </div>
            {{/each}}
          </div>

          <!-- Quote Section -->
          <div class="mt-auto pt-4">
            <div class="p-3 rounded-lg bg-themed">
              <p class="text-primary opacity-80 text-sm">
                <i class="fas fa-quote-left text-accent mr-2"></i>
                {{this.content.quote.text}}
                <i class="fas fa-quote-right text-accent ml-2"></i>
              </p>
              <p class="text-primary opacity-60 text-xs mt-1 text-right">— {{this.content.quote.author}}</p>
            </div>
          </div>
          {{/eq}}

          {{#eq this.content.type 'features'}}
          <!-- Features List -->
          <!--
          Features list style options:

          Option 1 - Vertical list (current):
          <div class="space-y-4">

          Option 2 - Compact list:
          <div class="space-y-2">

          Option 3 - Card style:
          <div class="grid grid-cols-1 gap-3">
          -->
          <div class="space-y-4">
            {{#each this.content.features}}
            <div class="flex items-start">
              <i class="{{this.iconClass}} mt-1 mr-3 dynamic-style-4"></i>
              <div>
                <h3 class="font-semibold text-primary">{{this.title}}</h3>
                <p class="text-primary opacity-80 text-sm">{{this.description}}</p>
              </div>
            </div>
            {{/each}}
          </div>

          <!-- Feature Highlight -->
          <div class="mt-auto pt-4">
            <div class="p-3 rounded-lg bg-themed">
              <div class="flex items-center">
                <div class="text-2xl mr-3 dynamic-style-8">
                  <i class="{{this.content.highlight.iconClass}}"></i>
                </div>
                <p class="text-primary opacity-90 text-sm">{{this.content.highlight.text}}</p>
              </div>
            </div>
          </div>
          {{/eq}}

          {{#eq this.content.type 'segments'}}
          <!-- Market Segments -->
          <!--
          Segments layout options:

          Option 1 - Vertical list (current):
          <div class="space-y-4">

          Option 2 - Card grid:
          <div class="grid grid-cols-1 gap-3">

          Option 3 - Compact layout:
          <div class="space-y-3">
          -->
          <div class="space-y-4">
            {{#each this.content.segments}}
            <div class="flex items-start">
              <i class="{{this.iconClass}} mt-1 mr-3 dynamic-style-4"></i>
              <div>
                <h3 class="font-semibold text-primary">{{this.title}}</h3>
                <p class="text-primary opacity-80 text-sm">
                  {{this.description}}
                  {{#if this.highlight}}
                  <span class="font-bold">{{this.highlight}}</span>
                  {{/if}}
                </p>
              </div>
            </div>
            {{/each}}
          </div>

          <!-- Segment Summary -->
          <div class="mt-auto pt-4">
            <div class="p-3 rounded-lg flex items-center bg-themed">
              <i class="{{this.content.summary.iconClass}} text-2xl mr-3 dynamic-style-10"></i>
              <p class="text-primary opacity-90 text-sm">{{this.content.summary.text}}</p>
            </div>
          </div>
          {{/eq}}
        </div>
        {{/each}}
      </div>

      <!-- Footer - Evolution Chart -->
      {{#if evolutionChart.enabled}}
      <!--
      Evolution chart style options:

      Option 1 - Simple timeline (current):
      <div class="mt-6">
        <div class="w-full h-16 flex items-center justify-center">

      Option 2 - Full width timeline:
      <div class="mt-6 bg-card border   rounded-lg p-4">

      Option 3 - Card style:
      <div class="mt-6 bg-card border   rounded-lg p-6 shadow-sm">
      -->
      <div class="mt-6">
        <div class="w-full h-16 flex items-center justify-center">
          <div class="flex items-center space-x-2">
            {{#each evolutionChart.stages}}
            {{#unless @first}}
            <!-- Connector line between stages -->
            <div class="h-1 w-16 bg-gradient-to-r from-{{@root.evolutionChart.stages.[subtract @index 1].bgColor}} to-{{this.bgColor}}"></div>
            {{/unless}}
            <!-- Evolution stage circle -->
            <!--
            Stage size mapping:
            small: h-8 w-8
            medium: h-10 w-10
            large: h-12 w-12
            xlarge: h-14 w-14
            -->
            <div class="{{#eq this.size 'small'}}h-8 w-8{{/eq}}{{#eq this.size 'medium'}}h-10 w-10{{/eq}}{{#eq this.size 'large'}}h-12 w-12{{/eq}}{{#eq this.size 'xlarge'}}h-14 w-14{{/eq}} rounded-full flex items-center justify-center bg-themed">
              <i class="{{this.iconClass}} dynamic-style-4"></i>
            </div>
            {{/each}}
          </div>
        </div>
        <p class="text-center text-primary opacity-70 mt-2">{{evolutionChart.description}}</p>
      </div>
      {{/if}}
    </div>
  </body>
</html>
`;
