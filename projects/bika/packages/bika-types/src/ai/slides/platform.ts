export const html = `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>{{title}}</title>
    <!-- Font Awesome CDN -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
      rel="stylesheet"
    />
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- D3.js CDN for charts -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <!-- Google Fonts - Inter and Noto Sans SC -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet"/>

    <style>
      body,
      html {
        width: 1366px;
        height: 768px;
        margin: 0;
        padding: 0;
        overflow: hidden;
        font-family: "Inter", "Noto Sans SC", sans-serif;
        color: var(--color-text);
        background-color: var(--color-background);
      }

      :root {
        --color-primary: {{color.primary}};
        --color-secondary: {{color.secondary}};
        --color-text: {{color.text}};
        --color-accent: {{color.accent}};
        --color-background: {{color.background}};
        --color-surface: {{color.surface}};
        --color-card: {{color.card}};
      }

      /*
      Color scheme references are now dynamically provided through template variables.
      All colors are accessible via var(--color-primary), var(--color-secondary), etc.
      */

      .slide-container {
        width: 1366px;
        height: 768px;
        position: relative;
        background-color: var(--color-background);
        padding: 40px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
      }

      /*
      Feature card animation options:

      Option 1 - Lift effect (current):
      .feature-card:hover { transform: translateY(-3px); box-shadow: 0 10px 25px var(--color-primary)22; }

      Option 2 - Scale effect:
      .feature-card:hover { transform: scale(1.05); }

      Option 3 - Glow effect:
      .feature-card:hover { box-shadow: 0 0 20px var(--color-primary)44; }

      Option 4 - Border highlight:
      .feature-card:hover { border-color: var(--color-primary); border-width: 2px; }
      */
      .feature-card {
        transition: all 0.3s ease;
        cursor: pointer;
      }

      .feature-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px var(--color-primary)22;
      }

      /*
      Integration arrow style options:

      Option 1 - Simple line with arrow:
      .integration-arrow { height: 2px; background-color: var(--color-primary); }
      .integration-arrow:after { border-left: 8px solid var(--color-primary); }

      Option 2 - Dotted connection:
      .integration-arrow { border-top: 2px dotted var(--color-primary); background: none; }

      Option 3 - Gradient arrow:
      .integration-arrow { background: linear-gradient(to right, var(--color-primary), var(--color-accent)); }
      */
      .integration-arrow {
        position: relative;
        height: 2px;
        background-color: var(--color-primary);
        margin: 0 15px;
      }

      .integration-arrow:after {
        content: '';
        position: absolute;
        right: -5px;
        top: -4px;
        width: 0;
        height: 0;
        border-top: 5px solid transparent;
        border-bottom: 5px solid transparent;
        border-left: 8px solid var(--color-primary);
      }

      /* Common utility classes */
      .text-primary {
        color: var(--color-text);
      }
      .text-accent {
        color: var(--color-accent);
      }
      .bg-surface {
        background-color: var(--color-surface);
      }
      .bg-card {
        background-color: var(--color-card);
      }
      .bg-primary {
        background-color: var(--color-primary);
      }
      .bg-secondary {
        background-color: var(--color-secondary);
      }
      .border-primary {
        border-color: var(--color-primary);
      }


      /* Generated CSS classes from inline styles */
      .bg-themed {
        background-color: {{this.bgColor}};
      }

      .dynamic-style-1 {
        color: var(--color-secondary);
      }

      .dynamic-style-3 {
        color: {{this.iconColor}};
      }

      .border-themed {
        border-color: var(--color-secondary);
        shadow-sm rounded-r-md;
      }

</style>
  </head>
  <body>
    <div class="slide-container">
      <!-- Header Section -->
      <div class="mb-6">
        <h1 class="text-4xl font-bold text-primary mb-2">{{pageTitle}}</h1>
        <!--
        Title underline style options:

        Option 1 - Solid color underline:
        <div class="w-24 h-1 bg-primary rounded"></div>

        Option 2 - Custom color underline:
        <div class="w-24 h-1 rounded bg-themed"></div>

        Option 3 - Gradient underline:
        <div class="w-24 h-1 bg-gradient-to-r from-[var(--color-primary)] to-[var(--color-accent)] rounded"></div>
        -->
        {{#if titleUnderlineColor}}
        <div class="w-24 h-1 rounded bg-themed"></div>
        {{else}}
        <div class="w-24 h-1 bg-primary rounded"></div>
        {{/if}}
      </div>

      <!-- Main Content Container -->
      <div class="flex flex-1 space-x-6">
        <!-- Left Column - Core Functionality -->
        <div class="w-1/2 flex flex-col">
          <div class="bg-card border   rounded-lg shadow-sm p-6 mb-6 flex-1">
            <h2 class="text-2xl font-semibold text-primary mb-4 flex items-center">
              <i class="{{leftColumn.coreFunctionality.sectionIcon}} text-primary mr-3"></i>
              {{leftColumn.coreFunctionality.sectionTitle}}
            </h2>

            <p class="text-primary opacity-80 mb-4 leading-relaxed">
              {{leftColumn.coreFunctionality.description}}
            </p>

            <!-- Features Grid -->
            <!--
            Features grid layout options:

            Option 1 - 2-column grid (current):
            <div class="grid grid-cols-2 gap-4 mt-6">

            Option 2 - 3-column grid:
            <div class="grid grid-cols-3 gap-3 mt-6">

            Option 3 - Single column list:
            <div class="space-y-3 mt-6">

            Option 4 - Auto-fit grid:
            <div class="grid grid-cols-[repeat(auto-fit,minmax(250px,1fr))] gap-4 mt-6">
            -->
            <div class="grid grid-cols-2 gap-4 mt-6">
              {{#each leftColumn.coreFunctionality.features}}
              <!--
              Feature card style options:

              Option 1 - Colored background (current):
              <div class="feature-card bg-primary11 p-3 rounded-md {{#eq this.spanCols 2}}col-span-2{{/eq}}">

              Option 2 - Border style:
              <div class="feature-card bg-surface border border-primary33 p-3 rounded-md {{#eq this.spanCols 2}}col-span-2{{/eq}}">

              Option 3 - Solid color background:
              <div class="feature-card bg-primary text-white p-3 rounded-md {{#eq this.spanCols 2}}col-span-2{{/eq}}">

              Option 4 - Minimal style:
              <div class="feature-card bg-surface hover:bg-primary11 p-3 rounded-md {{#eq this.spanCols 2}}col-span-2{{/eq}}">
              -->
              <div class="feature-card bg-primary11 p-3 rounded-md {{#eq this.spanCols 2}}col-span-2{{/eq}}">
                <div class="flex items-center mb-2">
                  <i class="{{this.iconClass}} text-primary mr-2"></i>
                  <span class="font-medium text-primary">{{this.title}}</span>
                </div>
                <p class="text-sm text-primary opacity-80">{{this.description}}</p>
              </div>
              {{/each}}
            </div>
          </div>
        </div>

        <!-- Right Column - Growth and Integration -->
        <div class="w-1/2 flex flex-col">
          <!-- Growth Metrics Section -->
          <div class="bg-card border   rounded-lg shadow-sm p-6 mb-6">
            <h2 class="text-2xl font-semibold text-primary mb-4 flex items-center">
              <i class="{{rightColumn.growthSection.iconClass}} text-accent mr-3"></i>
              {{rightColumn.growthSection.title}}
            </h2>

            <!--
            Growth content layout options:

            Option 1 - Side-by-side with chart:
            <div class="flex items-center justify-between">

            Option 2 - Stacked layout:
            <div class="text-center">

            Option 3 - Chart-focused layout:
            <div class="flex flex-col items-center">
            -->
            <div class="flex items-center justify-between">
              <div>
                <p class="text-primary opacity-80 mb-3">
                  {{rightColumn.growthSection.description}}
                  <span class="text-accent font-bold text-2xl">{{rightColumn.growthSection.growthPercentage}}</span>
                </p>
                {{#if rightColumn.growthSection.additionalInfo}}
                <p class="text-primary opacity-80">{{rightColumn.growthSection.additionalInfo}}</p>
                {{/if}}
              </div>
              {{#if rightColumn.growthSection.showChart}}
              <div class="w-40 h-40 relative" id="growth-chart"></div>
              {{/if}}
            </div>
          </div>

          <!-- Integration Advantages -->
          <div class="bg-card border   rounded-lg shadow-sm p-6 flex-1">
            <h2 class="text-2xl font-semibold text-primary mb-4 flex items-center">
              <i class="{{rightColumn.integrationSection.iconClass}} mr-3 dynamic-style-1"></i>
              {{rightColumn.integrationSection.title}}
            </h2>

            <!-- Integration Platforms -->
            <!--
            Platform icons layout options:

            Option 1 - Horizontal with arrows (current):
            Connected layout with visual flow

            Option 2 - Triangle layout:
            One platform at top, two at bottom

            Option 3 - Vertical stack:
            Platforms stacked vertically

            Option 4 - Hub and spoke:
            Central platform with others around it
            -->
            <div class="flex items-center justify-between mb-6">
              {{#each rightColumn.integrationSection.platforms}}
              <div class="w-1/3 text-center">
                <div class="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-2 bg-themed">
                  <i class="{{this.iconClass}} text-2xl dynamic-style-3"></i>
                </div>
                <p class="font-medium text-primary text-sm">{{this.name}}</p>
              </div>
              {{#unless @last}}
              <div class="integration-arrow flex-shrink-0"></div>
              {{/unless}}
              {{/each}}
            </div>

            <!-- Platform Descriptions -->
            <div class="flex items-center justify-between mb-6">
              {{#each rightColumn.integrationSection.platforms}}
              <div class="w-1/3 px-3">
                <p class="text-sm text-primary opacity-70 text-center">{{this.description}}</p>
              </div>
              {{/each}}
            </div>

            <!-- Summary Box -->
            <!--
            Summary box style options:

            Option 1 - Colored background (current):
            <div class="mt-6 p-3 bg-secondary11 rounded-md">

            Option 2 - Border style:
            <div class="mt-6 p-3 border border-gray-200 border-themed">

            Option 3 - Gradient background:
            <div class="mt-6 p-3 bg-gradient-to-r from-[var(--color-primary)]11 to-[var(--color-secondary)]11 rounded-md">

            Option 4 - Card style:
            <div class="mt-6 p-4 bg-surface border-l-4 border-gray-200 border-themed">
            -->
            <div class="mt-6 p-3 bg-secondary11 rounded-md">
              <p class="text-sm text-primary opacity-90">
                {{#if rightColumn.integrationSection.summaryBox.iconClass}}
                <i class="{{rightColumn.integrationSection.summaryBox.iconClass}} text-accent mr-2"></i>
                {{/if}}
                {{rightColumn.integrationSection.summaryBox.text}}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    {{#if rightColumn.growthSection.showChart}}
    <script>
      // D3.js growth chart
      document.addEventListener('DOMContentLoaded', function() {
        const chartContainer = document.getElementById('growth-chart');
        if (!chartContainer) return;

        const width = 160;
        const height = 160;
        const radius = Math.min(width, height) / 2;

        // Extract percentage from growth data
        const growthText = '{{rightColumn.growthSection.growthPercentage}}';
        const percentage = parseFloat(growthText.replace('%', ''));

        const svg = d3.select("#growth-chart")
          .append("svg")
          .attr("width", width)
          .attr("height", height)
          .append("g")
          .attr("transform", \`translate(\${width / 2}, \${height / 2})\`);

        // Growth data (last year vs this year, normalized)
        const lastYear = 100 - percentage;
        const thisYear = 100;
        const data = [lastYear, percentage];

        // Color scale
        const color = d3.scaleOrdinal()
          .domain([0, 1])
          .range(["var(--color-text)33", "var(--color-primary)"]);

        // Pie chart setup
        const pie = d3.pie()
          .sort(null)
          .value(d => d);

        const arc = d3.arc()
          .innerRadius(radius * 0.6)
          .outerRadius(radius * 0.9);

        // Create pie chart
        const arcs = svg.selectAll("arc")
          .data(pie(data))
          .enter()
          .append("g")
          .attr("class", "arc");

        // Add paths
        arcs.append("path")
          .attr("d", arc)
          .attr("fill", (d, i) => color(i))
          .attr("stroke", "var(--color-surface)")
          .style("stroke-width", "2px");

        // Add text in the center
        svg.append("text")
          .attr("text-anchor", "middle")
          .attr("dy", "0em")
          .attr("font-size", "24px")
          .attr("font-weight", "bold")
          .attr("fill", "var(--color-primary)")
          .text(growthText);

        svg.append("text")
          .attr("text-anchor", "middle")
          .attr("dy", "1.5em")
          .attr("font-size", "12px")
          .attr("fill", "var(--color-text)")
          .attr("opacity", "0.7")
          .text("Growth");
      });
    </script>
    {{/if}}
  </body>
</html>
`;
