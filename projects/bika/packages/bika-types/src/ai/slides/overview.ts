export const html = `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>{{title}}</title>
    <!-- Font Awesome CDN -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
      rel="stylesheet"
    />
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- D3.js CDN for potential charts -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <!-- Google Fonts - Inter and Noto Sans SC -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet"/>

    <style>
      body,
      html {
        width: 1366px;
        height: 768px;
        margin: 0;
        padding: 0;
        overflow: hidden;
        font-family: "Inter", "Noto Sans SC", sans-serif;
        color: var(--color-text);
        background-color: var(--color-background);
      }

      :root {
        --color-primary: {{color.primary}};
        --color-secondary: {{color.secondary}};
        --color-text: {{color.text}};
        --color-accent: {{color.accent}};
        --color-background: {{color.background}};
        --color-surface: {{color.surface}};
        --color-card: {{color.card}};
      }

      /*
      Color scheme references are now dynamically provided through template variables.
      All colors are accessible via var(--color-primary), var(--color-secondary), etc.
      */

      .slide-container {
        width: 1366px;
        height: 768px;
        position: relative;
        background-color: var(--color-background);
        padding: 40px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
      }

      /*
      Strategy card animation options:

      Option 1 - Lift effect (current):
      .strategy-card:hover { transform: translateY(-5px); box-shadow: 0 15px 35px var(--color-primary)22; }

      Option 2 - Scale and glow:
      .strategy-card:hover { transform: scale(1.02); box-shadow: 0 0 30px var(--color-primary)33; }

      Option 3 - Tilt effect:
      .strategy-card:hover { transform: perspective(1000px) rotateX(5deg) translateY(-5px); }

      Option 4 - Border highlight:
      .strategy-card:hover { border-left-width: 8px; }
      */
      .strategy-card {
        height: 400px;
        transition: all 0.3s ease;
        cursor: pointer;
      }

      .strategy-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px var(--color-primary)22;
      }

      /*
      Icon background style options:

      Option 1 - Circle (current):
      border-radius: 50%;

      Option 2 - Rounded square:
      border-radius: 16px;

      Option 3 - Hexagon:
      clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
      */
      .icon-bg {

      /* Common utility classes */
      .text-primary {
        color: var(--color-text);
      }
      .text-accent {
        color: var(--color-accent);
      }
      .bg-surface {
        background-color: var(--color-surface);
      }
      .bg-card {
        background-color: var(--color-card);
      }


      /*
      Metric row style options:

      Option 1 - Dashed border (current):
      border-bottom: 1px dashed var(--color-text)33;

      Option 2 - Solid border:
      border-bottom: 1px solid var(--color-text)22;

      Option 3 - No border:
      border-bottom: none; (add padding for spacing)

      Option 4 - Colored border:
      border-bottom: 1px solid var(--color-primary)44;
      */
      .metric-row {
        border-bottom: 1px dashed var(--color-text)33;
        padding: 8px 0;
      }

      .metric-row:last-child {
        border-bottom: none;
      }


      /* Generated CSS classes from inline styles */
      .border-left-themed {
        border-left-color: {{this.borderColor}};
      }

      .border-top-themed {
        border-top-color: {{this.borderColor}};
      }

      .bg-themed-light {
        background-color: {{this.borderColor}};
      }

      .bg-themed {
        background-color: {{this.bgColor}};
        color: {{this.textColor}};
      }

      .dynamic-style-4 {
        color: {{this.iconColor}};
      }

      .dynamic-style-5 {
        color: {{this.valueColor}};
      }

</style>
  </head>
  <body>
    <div class="slide-container">
      <!-- Header Section -->
      <div class="mb-8">
        <h1 class="text-4xl font-bold text-primary mb-4">{{pageTitle}}</h1>
        <p class="text-xl text-primary opacity-80">{{pageSubtitle}}</p>
      </div>

      <!-- Main Content Area with Three Strategy Pillars -->
      <!--
      Strategy pillars layout options:

      Option 1 - Equal width columns (current):
      <div class="flex-1 flex justify-between space-x-6">

      Option 2 - Grid layout:
      <div class="flex-1 grid grid-cols-3 gap-6">

      Option 3 - Flexible width:
      <div class="flex-1 flex space-x-6">

      Option 4 - Responsive breakpoints:
      <div class="flex-1 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      -->
      <div class="flex-1 flex justify-between space-x-6">
        {{#each strategyPillars}}
        <!--
        Strategy card style options:

        Option 1 - Left border accent (current):
        <div class="strategy-card flex-1 bg-surface rounded-lg shadow-sm p-6 flex flex-col border-l-4 border-left-themed">

        Option 2 - Top border accent:
        <div class="strategy-card flex-1 bg-surface rounded-lg shadow-sm p-6 flex flex-col border-t-4 border-top-themed">

        Option 3 - Colored background:
        <div class="strategy-card flex-1 rounded-lg shadow-sm p-6 flex flex-col bg-themed-light">

        Option 4 - Gradient background:
        <div class="strategy-card flex-1 bg-gradient-to-br from-[var(--color-surface)] to-[{{this.borderColor}}]11 rounded-lg shadow-sm p-6 flex flex-col">
        -->
        <div class="strategy-card flex-1 bg-card border   rounded-lg shadow-sm p-6 flex flex-col border-l-4 border-left-themed">
          <!-- Card Header -->
          <div class="flex items-center mb-5">
            <div class="icon-bg flex items-center justify-center mr-4 bg-themed">
              <i class="{{this.iconClass}} text-3xl dynamic-style-4"></i>
            </div>
            <h2 class="text-2xl font-bold text-primary">{{this.title}}</h2>
          </div>

          <!-- Description -->
          <p class="text-primary opacity-80 mb-4 leading-relaxed">{{this.description}}</p>

          <!-- Metrics Section -->
          <!--
          Metrics display options:

          Option 1 - Table-style rows (current):
          <div class="space-y-1 mb-4">

          Option 2 - Card-style metrics:
          <div class="grid grid-cols-1 gap-2 mb-4">

          Option 3 - Horizontal layout:
          <div class="flex flex-wrap gap-4 mb-4">
          -->
          <div class="space-y-1 mb-4">
            {{#each this.metrics}}
            <div class="metric-row flex justify-between items-center">
              <span class="text-sm text-primary opacity-70">{{this.label}}</span>
              <span class="font-semibold dynamic-style-5">{{this.value}}</span>
            </div>
            {{/each}}
          </div>

          <!-- Bottom Section -->
          <div class="mt-auto">
            <h3 class="text-sm font-semibold text-primary opacity-80 mb-2">{{this.bottomSection.sectionTitle}}</h3>

            {{#eq this.bottomSection.content.type 'tags'}}
            <!-- Tags Display -->
            <!--
            Tags layout options:

            Option 1 - Flex wrap (current):
            <div class="flex flex-wrap gap-2">

            Option 2 - Grid layout:
            <div class="grid grid-cols-3 gap-2">

            Option 3 - Vertical stack:
            <div class="space-y-2">
            -->
            <div class="flex flex-wrap gap-2">
              {{#each this.bottomSection.content.tags}}
              <span class="px-2 py-1 text-xs rounded-full bg-themed">{{this.text}}</span>
              {{/each}}
            </div>
            {{/eq}}

            {{#eq this.bottomSection.content.type 'highlights'}}
            <!-- Highlights Display -->
            <!--
            Highlights layout options:

            Option 1 - Vertical list (current):
            <div class="space-y-2">

            Option 2 - Compact list:
            <div class="space-y-1">

            Option 3 - Grid layout:
            <div class="grid grid-cols-1 gap-2">
            -->
            <div class="space-y-2">
              {{#each this.bottomSection.content.items}}
              <div class="flex items-center space-x-3">
                <i class="{{this.iconClass}} dynamic-style-4"></i>
                <span class="text-sm text-primary opacity-90">{{this.text}}</span>
              </div>
              {{/each}}
            </div>
            {{/eq}}
          </div>
        </div>
        {{/each}}
      </div>

      <!-- Footer with Strategy Vision -->
      <!--
      Footer style options:

      Option 1 - Pill-shaped highlight (current):
      <div class="mt-8 flex justify-center">
          <div class="bg-primary11 px-10 py-4 rounded-full shadow-sm text-center">

      Option 2 - Full-width banner:
      <div class="mt-8 bg-primary11 py-4 rounded-lg text-center">

      Option 3 - Simple centered text:
      <div class="mt-8 text-center">

      Option 4 - Card-style footer:
      <div class="mt-8 bg-surface border border-primary33 rounded-lg p-4 text-center">
      -->
      <div class="mt-8 flex justify-center">
        <div class="bg-primary11 px-10 py-4 rounded-full shadow-sm text-center">
          <p class="text-primary opacity-90">
            {{#if footerVision.iconClass}}
            <i class="{{footerVision.iconClass}} text-accent mr-2"></i>
            {{/if}}
            {{#if footerVision.highlightTerm}}
            <span class="font-medium">{{footerVision.highlightTerm}}:</span>
            {{/if}}
            {{footerVision.text}}
            {{#if footerVision.iconClass}}
            <i class="{{footerVision.iconClass}} text-accent ml-2"></i>
            {{/if}}
          </p>
        </div>
      </div>
    </div>
  </body>
</html>
`;
