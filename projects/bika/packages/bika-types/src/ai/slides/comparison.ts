export const html = `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>{{title}}</title>
    <!-- Font Awesome CDN -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
      rel="stylesheet"
    />
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts - Inter, Noto Sans SC, SF Pro Display -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;700&family=SF+Pro+Display:wght@300;400;500;600;700&display=swap" rel="stylesheet"/>

    <style>
      body,
      html {
        width: 1366px;
        height: 768px;
        margin: 0;
        padding: 0;
        overflow: hidden;
        font-family: "SF Pro Display", "Inter", "Noto Sans SC", -apple-system, BlinkMacSystemFont, sans-serif;
        color: var(--color-text);
        background-color: var(--color-background);
      }

      :root {
        --color-primary: {{color.primary}};
        --color-secondary: {{color.secondary}};
        --color-text: {{color.text}};
        --color-accent: {{color.accent}};
        --color-background: {{color.background}};
        --color-surface: {{color.surface}};
        --color-card: {{color.card}};
      }

      /*
      Color scheme references are now dynamically provided through template variables.
      All colors are accessible via var(--color-primary), var(--color-secondary), etc.
      */

      .slide-container {
        width: 1366px;
        height: 768px;
        position: relative;
        background: linear-gradient(135deg, var(--color-background) 0%, var(--color-surface) 100%);
        padding: 64px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
      }

      /*
      Comparison table style options:

      Option 1 - Modern separated (current):
      border-collapse: separate; border-spacing: 0;

      Option 2 - Traditional collapsed:
      border-collapse: collapse;

      Option 3 - Card-style rows:
      Add shadow and spacing between rows
      */
      .comparison-table {
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
      }

      .comparison-table th {
        background-color: var(--color-surface);
        padding: 12px;
        text-align: left;
        font-weight: 600;
        color: var(--color-text);
      }

      .comparison-table td {
        padding: 12px;
        border-top: 1px solid var(--color-text)22;
        color: var(--color-text);
      }

      .comparison-table tr:nth-child(even) td {
        background-color: var(--color-surface);
      }

      .comparison-table tr:hover td {
        background-color: var(--color-primary)11;
        transition: background-color 0.2s ease;
      }

      /*
      Page number positioning options:

      Option 1 - Bottom right (current):
      position: absolute; bottom: 20px; right: 20px;

      Option 2 - Bottom center:
      position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%);

      Option 3 - Top right:
      position: absolute; top: 20px; right: 20px;
      */
      .page-number {
        position: absolute;
        bottom: 20px;
        right: 20px;
        font-size: 14px;
        color: var(--color-text)77;
      }

      /* Common utility classes */
      .text-primary {
        color: var(--color-text);
      }
      .text-accent {
        color: var(--color-accent);
      }
      .bg-surface {
        background-color: var(--color-surface);
      }
      .bg-card {
        background-color: var(--color-card);
      }
      .bg-primary {
        background-color: var(--color-primary);
      }
      .bg-secondary {
        background-color: var(--color-secondary);
      }
      .border-primary {
        border-color: var(--color-primary);
      }


      /* Generated CSS classes from inline styles */
      .bg-themed {
        background-color: {{this.improvement.color}};
        color: {{this.improvement.color}};
      }

      .border-themed {
        border-color: {{sideContent.highlights.borderColor}};
      }

      .dynamic-style-4 {
        color: {{comparisonTable.columns.newProduct.color}};
      }

      .dynamic-style-5 {
        color: {{comparisonTable.columns.oldProduct.color}};
      }

      .dynamic-style-6 {
        color: {{this.feature.iconColor}};
      }

      .dynamic-style-7 {
        color: {{this.improvement.color}};
        font-weight: 600;
      }

      .dynamic-style-9 {
        color: {{this.improvement.color}};
      }

      .border-left-themed {
        background-color: {{sideContent.highlights.bgColor}};
        border-left-color: {{sideContent.highlights.borderColor}};
      }

      .border-top-themed {
        background-color: {{sideContent.highlights.bgColor}};
        border-top-color: {{sideContent.highlights.borderColor}};
      }

      .bg-themed-light {
        background-color: {{sideContent.highlights.borderColor}};
      }

      .dynamic-style-14 {
        color: {{this.iconColor}};
      }

</style>
  </head>
  <body>
    <div class="slide-container">
      <!-- Header Section -->
      <div class="mb-6">
        <h1 class="text-4xl font-bold text-primary">{{pageTitle}}</h1>
        <!--
        Title accent line options:

        Option 1 - Solid line (current):
        <div class="w-20 h-1 mt-3 bg-themed"></div>

        Option 2 - Gradient line:
        <div class="w-20 h-1 mt-3 bg-gradient-to-r from-[{{titleAccentColor}}] to-[var(--color-accent)]"></div>

        Option 3 - Rounded line:
        <div class="w-20 h-1 mt-3 rounded-full bg-themed"></div>

        Option 4 - Multiple lines:
        <div class="flex space-x-1 mt-3">
          <div class="w-6 h-1 bg-themed"></div>
          <div class="w-4 h-1 bg-themed"></div>
        </div>
        -->
        <div class="w-20 h-1 mt-3 bg-themed"></div>
      </div>

      <!-- Introduction -->
      <div class="mb-6">
        <p class="text-primary opacity-90">{{introduction}}</p>
      </div>

      <!-- Main Content Grid -->
      <!--
      Grid layout options:

      Option 1 - 8:4 split (current):
      <div class="grid grid-cols-12 gap-6">
        <div class="col-span-8">...</div>
        <div class="col-span-4">...</div>

      Option 2 - 7:5 split:
      <div class="grid grid-cols-12 gap-6">
        <div class="col-span-7">...</div>
        <div class="col-span-5">...</div>

      Option 3 - Responsive split:
      <div class="grid grid-cols-1 lg:grid-cols-12 gap-6">
      -->
      <div class="grid grid-cols-12 gap-6">
        <!-- Left Column - Comparison Table -->
        <div class="col-span-8">
          <!--
          Table container style options:

          Option 1 - Card with shadow (current):
          <div class="bg-surface rounded-lg shadow-sm overflow-hidden">

          Option 2 - Simple border:
          <div class="border border-gray-200 border-themed">

          Option 3 - No container:
          Direct table without wrapper

          Option 4 - Gradient background:
          <div class="bg-gradient-to-br from-[var(--color-surface)] to-[var(--color-background)] rounded-lg shadow-sm overflow-hidden">
          -->
          <div class="bg-card border   rounded-lg shadow-sm overflow-hidden">
            <table class="comparison-table">
              <thead>
                <tr>
                  <th class="w-1/4">{{comparisonTable.columns.feature}}</th>
                  <th class="w-1/4 dynamic-style-4">{{comparisonTable.columns.newProduct.name}}</th>
                  <th class="w-1/4 dynamic-style-5">{{comparisonTable.columns.oldProduct.name}}</th>
                  <th class="w-1/4">{{comparisonTable.columns.improvement}}</th>
                </tr>
              </thead>
              <tbody>
                {{#each comparisonTable.rows}}
                <tr>
                  <!-- Feature Column -->
                  <!--
                  Feature cell style options:

                  Option 1 - Icon + text (current):
                  <div class="flex items-center">
                    <i class="{{this.feature.iconClass}} mr-2 dynamic-style-6"></i>
                    <span>{{this.feature.name}}</span>
                  </div>

                  Option 2 - Text only:
                  <span>{{this.feature.name}}</span>

                  Option 3 - Badge style:
                  <div class="inline-flex items-center px-2 py-1 rounded-full bg-surface">
                  -->
                  <td>
                    <div class="flex items-center">
                      <i class="{{this.feature.iconClass}} mr-2 dynamic-style-6"></i>
                      <span>{{this.feature.name}}</span>
                    </div>
                  </td>
                  <!-- New Product Value -->
                  <td>{{this.newValue}}</td>
                  <!-- Old Product Value -->
                  <td>{{this.oldValue}}</td>
                  <!-- Improvement -->
                  <!--
                  Improvement cell style options:

                  Option 1 - Colored text (current):
                  <td class="dynamic-style-7">{{this.improvement.text}}</td>

                  Option 2 - Badge style:
                  <td><span class="px-2 py-1 rounded-full text-sm bg-themed">{{this.improvement.text}}</span></td>

                  Option 3 - With icon:
                  <td><i class="fas fa-arrow-up mr-1 dynamic-style-9"></i>{{this.improvement.text}}</td>
                  -->
                  <td class="dynamic-style-7">{{this.improvement.text}}</td>
                </tr>
                {{/each}}
              </tbody>
            </table>
          </div>
        </div>

        <!-- Right Column - Image and Highlights -->
        <div class="col-span-4">
          <!-- Product Image -->
          <!--
          Image style options:

          Option 1 - Rounded with shadow (current):
          <div class="mb-6">
            <img class="rounded-lg shadow-sm w-full h-auto" src="{{sideContent.image.src}}" alt="{{sideContent.image.alt}}" />
          </div>

          Option 2 - Full rounded:
          <div class="mb-6">
            <img class="rounded-2xl shadow-lg w-full h-auto" src="{{sideContent.image.src}}" alt="{{sideContent.image.alt}}" />
          </div>

          Option 3 - Card container:
          <div class="mb-6 bg-surface rounded-lg p-4 shadow-sm">
            <img class="rounded w-full h-auto" src="{{sideContent.image.src}}" alt="{{sideContent.image.alt}}" />
          </div>
          -->
          <div class="mb-6">
            <img class="rounded-lg shadow-sm w-full h-auto" src="{{sideContent.image.src}}" alt="{{sideContent.image.alt}}" />
          </div>

          <!-- Key Highlights -->
          <!--
          Highlights box style options:

          Option 1 - Left border accent (current):
          <div class="p-5 rounded-lg border-l-4 border-left-themed">

          Option 2 - Top border accent:
          <div class="p-5 rounded-lg border-t-4 border-top-themed">

          Option 3 - Colored background:
          <div class="p-5 rounded-lg bg-themed-light">

          Option 4 - Card with shadow:
          <div class="bg-surface p-5 rounded-lg shadow-sm border border-gray-200 border-themed">
          -->
          <div class="p-5 rounded-lg border-l-4 border-left-themed">
            <h3 class="text-lg font-semibold text-primary mb-3">{{sideContent.highlights.title}}</h3>
            <!--
            Highlights list style options:

            Option 1 - Vertical spacing (current):
            <ul class="space-y-3">

            Option 2 - Compact spacing:
            <ul class="space-y-2">

            Option 3 - Grid layout:
            <div class="grid grid-cols-1 gap-3">
            -->
            <ul class="space-y-3">
              {{#each sideContent.highlights.items}}
              <!--
              Highlight item style options:

              Option 1 - Icon + text (current):
              <li class="flex items-start">
                <i class="{{this.iconClass}} mt-1 mr-2 dynamic-style-14"></i>
                <span class="text-primary opacity-90">{{this.text}}</span>
              </li>

              Option 2 - Bullet points:
              <li class="text-primary opacity-90">• {{this.text}}</li>

              Option 3 - Card style:
              <li class="bg-surface p-2 rounded flex items-center">
              -->
              <li class="flex items-start">
                <i class="{{this.iconClass}} mt-1 mr-2 dynamic-style-14"></i>
                <span class="text-primary opacity-90">{{this.text}}</span>
              </li>
              {{/each}}
            </ul>
          </div>
        </div>
      </div>

      <!-- Page Number -->
      {{#if pageNumber.show}}
      <div class="page-number">{{pageNumber.current}} / {{pageNumber.total}}</div>
      {{/if}}
    </div>
  </body>
</html>
`;
