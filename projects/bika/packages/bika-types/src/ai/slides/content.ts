export const html = `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>{{title}}</title>
    <!-- Font Awesome CDN -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
      rel="stylesheet"
    />
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts - Inter and Noto Sans SC -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet"/>
    <!-- ECharts CDN -->
    <!-- <script src="https://fastly.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script> -->
    <style>
      body,
      html {
        width: 1366px;
        height: 768px;
        margin: 0;
        padding: 0;
        overflow: hidden;
        font-family: "Noto Sans SC", "Inter", sans-serif;
        color: var(--color-text);
        background-color: var(--color-background);
      }
      :root {
        --color-primary: {{color.primary}};
        --color-secondary: {{color.secondary}};
        --color-text: {{color.text}};
        --color-accent: {{color.accent}};
        --color-background: {{color.background}};
        --color-surface: {{color.surface}};
        --color-card: {{color.card}};
      }
      .slide-container {
        width: 1366px;
        height: 768px;
        position: relative;
        background-color: var(--color-background);
        padding: 40px 60px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
      }

      /*
      Card icon background style options:

      Option 1 - Circular with primary color:
      background-color: var(--color-primary)22; (22 for opacity)

      Option 2 - Square with rounded corners:
      border-radius: 12px;

      Option 3 - Diamond shape:
      transform: rotate(45deg); (apply to inner icon with opposite rotation)

      Option 4 - No background:
      background-color: transparent;
      */
      .card-icon-bg {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
        flex-shrink: 0;
        background-color: var(--color-primary)22;
      }
      .card-icon {
        font-size: 3rem;
        color: var(--color-primary);
      }
      .highlight-text {
        color: var(--color-primary);
        font-weight: 600;
      }
      .card-style {
        background-color: var(--color-card);
        border: 1px solid var(--color-text)10;
      }
      .revenue-card-style {
        background-color: var(--color-card);
        border: 1px solid var(--color-text)10;
      }
      .footer-style {
        background-color: var(--color-card);
        border: 1px solid var(--color-text)10;
      }
      .text-primary {
        color: var(--color-text);
      }
      .text-accent {
        color: var(--color-accent);
      }
      .border-text-light {
        border-color: var(--color-text)22;
      }
      .text-primary-icon {
        color: var(--color-primary);
      }


      /* Generated CSS classes from inline styles */
      .dynamic-style-0 {
        background: linear-gradient(to bottom right, var(--color-primary), var(--color-secondary));
      }

      .dynamic-style-1 {
        border-left: 4px solid var(--color-primary);
      }

      .dynamic-style-2 {
        border: 2px solid var(--color-primary);
      }

      .dynamic-style-3 {
        border-bottom: 2px solid var(--color-primary);
      }

</style>
  </head>
  <body>
    <div class="slide-container">
      <!-- Header Section -->
      <div class="header-section mb-10 flex justify-between items-start w-full">
        <div class="text-left max-w-[calc(100%-250px)]">
          <h1 class="text-4xl font-bold text-primary mb-4">{{pageTitle}}</h1>
          <p class="text-lg text-primary opacity-80 leading-relaxed pr-8">
            {{pageDescription}}
          </p>
        </div>
        <!--
        Revenue card style options:

        Option 1 - Bordered white card:
        <div class="flex-shrink-0 shadow-sm rounded-lg p-5 flex flex-col items-start min-w-[200px] bg-surface  ">

        Option 2 - Colored background card:
        <div class="flex-shrink-0 text-white shadow-lg rounded-lg p-5 flex flex-col items-start min-w-[200px] bg-primary">

        Option 3 - Gradient background:
        <div class="flex-shrink-0 text-white shadow-lg rounded-lg p-5 flex flex-col items-start min-w-[200px] dynamic-style-0">

        Option 4 - Minimal flat design:
        <div class="flex-shrink-0 p-5 flex flex-col items-start min-w-[200px] bg-surface dynamic-style-1">
        -->
        <div class="flex-shrink-0 revenue-card-style shadow-sm rounded-lg p-5 flex flex-col items-start min-w-[200px]">
          <div class="text-primary opacity-60 text-base mb-1">{{revenueCard.label}}</div>
          <div class="text-4xl font-bold text-primary mb-2 leading-none">{{revenueCard.value}}</div>
          <div class="text-primary opacity-60 text-base">
            {{revenueCard.growthLabel}} <span class="text-accent font-bold text-lg">{{revenueCard.growth}}</span>
          </div>
        </div>
      </div>

      <!-- Main Content - Three Cards Section -->
      <div class="grid grid-cols-3 gap-6 flex-grow">
        <!--
        Service card layout options:

        Option 1 - Standard 3-column grid:
        <div class="grid grid-cols-3 gap-6 flex-grow">

        Option 2 - 2-column grid with larger cards:
        <div class="grid grid-cols-2 gap-8 flex-grow">

        Option 3 - Single column layout:
        <div class="flex flex-col space-y-6 flex-grow">

        Option 4 - Masonry-style layout:
        <div class="columns-3 gap-6 flex-grow">
        -->

        <!-- Card 1 -->
        <!--
        Card style options:

        Option 1 - Standard white card with shadow:
        <div class="rounded-xl shadow-lg p-6 flex flex-col justify-between bg-surface">

        Option 2 - Colored background card:
        <div class="text-white rounded-xl shadow-lg p-6 flex flex-col justify-between bg-primary">

        Option 3 - Bordered card without shadow:
        <div class="rounded-xl p-6 flex flex-col justify-between bg-surface dynamic-style-2">

        Option 4 - Gradient background:
        <div class="text-white rounded-xl shadow-lg p-6 flex flex-col justify-between dynamic-style-0">
        -->
        <div class="card-style rounded-xl shadow-lg p-6 flex flex-col justify-between">
          <div>
            <div class="flex items-center mb-4">
              <div class="card-icon-bg">
                <i class="{{serviceCards[0].iconClass}} card-icon"></i>
              </div>
              <h2 class="text-2xl font-semibold text-primary">{{serviceCards[0].title}}</h2>
            </div>
            <p class="text-primary opacity-70 text-sm leading-relaxed mb-4">
              {{serviceCards[0].description}}
            </p>
          </div>
          <div class="space-y-2 mt-auto pt-4 border-t border-text-light">
            {{#each serviceCards[0].metrics}}
            <div class="flex justify-between items-center text-primary opacity-70 text-sm">
              <span>{{this.label}}</span>
              <span class="font-semibold text-primary">{{this.value}}</span>
            </div>
            {{/each}}
          </div>
        </div>

        <!-- Card 2 -->
        <div class="card-style rounded-xl shadow-lg p-6 flex flex-col justify-between">
          <div>
            <div class="flex items-center mb-4">
              <div class="card-icon-bg">
                <i class="{{serviceCards[1].iconClass}} card-icon"></i>
              </div>
              <h2 class="text-2xl font-semibold text-primary">{{serviceCards[1].title}}</h2>
            </div>
            <p class="text-primary opacity-70 text-sm leading-relaxed mb-4">
              {{serviceCards[1].description}}
            </p>
          </div>
          <div class="space-y-2 mt-auto pt-4 border-t border-text-light">
            {{#each serviceCards[1].metrics}}
            <div class="flex justify-between items-center text-primary opacity-70 text-sm">
              <span>{{this.label}}</span>
              <span class="font-semibold text-primary">{{this.value}}</span>
            </div>
            {{/each}}
          </div>
        </div>

        <!-- Card 3 -->
        <div class="card-style rounded-xl shadow-lg p-6 flex flex-col justify-between">
          <div>
            <div class="flex items-center mb-4">
              <div class="card-icon-bg">
                <i class="{{serviceCards[2].iconClass}} card-icon"></i>
              </div>
              <h2 class="text-2xl font-semibold text-primary">{{serviceCards[2].title}}</h2>
            </div>
            <p class="text-primary opacity-70 text-sm leading-relaxed mb-4">
              {{serviceCards[2].description}}
            </p>
          </div>
          <div class="space-y-2 mt-auto pt-4 border-t border-text-light">
            {{#each serviceCards[2].metrics}}
            <div class="flex justify-between items-center text-primary opacity-70 text-sm">
              <span>{{this.label}}</span>
              <span class="font-semibold text-primary">{{this.value}}</span>
            </div>
            {{/each}}
          </div>
        </div>
      </div>

      <!-- Bottom Decorative Element -->
      <div class="mt-10 flex justify-center w-full">
        <!--
        Footer decoration style options:

        Option 1 - Pill-shaped with icons:
        <div class="rounded-full p-3 px-8 shadow-md flex items-center space-x-3 text-lg font-medium bg-surface text-primary">
            <i class="fas fa-{{leftIcon}} text-primary"></i>
            <span>{{footerText}}</span>
            <i class="fas fa-{{rightIcon}} text-primary"></i>
        </div>

        Option 2 - Simple text with underline:
        <div class="pb-2 dynamic-style-3">
            <span class="text-lg font-medium text-primary">{{footerText}}</span>
        </div>

        Option 3 - Colored background badge:
        <div class="text-white rounded-lg px-6 py-3 flex items-center space-x-2 bg-primary">
            <i class="fas fa-{{icon}} text-white"></i>
            <span class="font-medium">{{footerText}}</span>
        </div>

        Option 4 - Minimal dots separator:
        <div class="flex items-center space-x-4 opacity-60 text-primary">
            <div class="w-2 h-2 rounded-full bg-primary"></div>
            <span class="text-sm font-medium">{{footerText}}</span>
            <div class="w-2 h-2 rounded-full bg-primary"></div>
        </div>
        -->
        <div class="footer-style rounded-full p-3 px-8 shadow-md flex items-center space-x-3 text-lg text-primary font-medium">
          <i class="fas fa-link text-primary-icon"></i>
          <span>{{footerText}}</span>
          <i class="fas fa-link text-primary-icon"></i>
        </div>
      </div>
    </div>
  </body>
</html>
`;
