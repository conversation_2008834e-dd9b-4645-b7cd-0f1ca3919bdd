export const html = `
<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>{{title}}</title>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
      rel="stylesheet"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700&family=Inter:wght@400;600;700&display=swap" rel="stylesheet"/>

    <style>
      body,
      html {
        width: 1366px;
        height: 768px;
        margin: 0;
        padding: 0;
        overflow: hidden;
        font-family: 'SF Pro Display', 'Inter', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
        color: var(--color-text);
        background: linear-gradient(135deg, var(--color-background) 0%, var(--color-surface) 100%);
      }

      :root {
        --color-primary: {{color.primary}};
        --color-secondary: {{color.secondary}};
        --color-text: {{color.text}};
        --color-accent: {{color.accent}};
        --color-background: {{color.background}};
        --color-surface: {{color.surface}};
        --color-card: {{color.card}};
      }

      .slide {
        width: 1366px;
        height: 768px;
        position: relative;
        clip-path: inset(0 0 0 0);
      }

      /* Logo styles */
      .brand-logo {
        width: 40px;
        height: 40px;
      }

      /* Image container for left/right layouts */
      .image-container {
        position: absolute;
        top: 0;
        width: 50%;
        height: 100%;
        z-index: 1;
      }

      .image-container img {
        object-fit: cover;
        width: 100%;
        height: 100%;
        opacity: 0.9;
      }

      /* Gradient overlays */
      .gradient-overlay {
        position: absolute;
        top: 0;
        width: 50%;
        height: 100%;
        z-index: 2;
      }

      /* Center layout styles */
      .layout-center .content-area {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        z-index: 10;
        padding: 0 80px;
      }

      /* Left layout styles */
      .layout-left .image-container {
        right: 0;
      }

      .layout-left .gradient-overlay {
        right: 0;
        background: linear-gradient(90deg, var(--color-background) 0%, rgba(255,255,255,0) 20%);
      }

      .layout-left .content-area {
        position: absolute;
        top: 0;
        left: 0;
        width: 50%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: left;
        z-index: 10;
        padding-left: 96px;
      }

      /* Right layout styles */
      .layout-right .image-container {
        left: 0;
      }

      .layout-right .gradient-overlay {
        left: 0;
        background: linear-gradient(-90deg, var(--color-background) 0%, rgba(255,255,255,0) 20%);
      }

      .layout-right .content-area {
        position: absolute;
        top: 0;
        right: 0;
        width: 50%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: right;
        z-index: 10;
        padding-right: 96px;
      }

      /* Title styles */
      .title-text {
        font-size: 3.75rem;
        font-weight: 700;
        line-height: 1.1;
        margin-bottom: 16px;
        color: var(--color-text);
      }

      /* Subtitle styles */
      .subtitle-text {
        font-size: 1.25rem;
        color: var(--color-text);
        opacity: 0.7;
        margin-bottom: 32px;
      }

      /* Accent line */
      .accent-line {
        width: 96px;
        height: 4px;
        background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
        margin-bottom: 32px;
      }

      .layout-center .accent-line {
        margin-left: auto;
        margin-right: auto;
      }

      .layout-right .accent-line {
        margin-left: auto;
        margin-right: 0;
      }

      /* Date text */
      .date-text {
        color: var(--color-text);
        opacity: 0.6;
        margin-top: 32px;
      }

      /* Background decorative shapes for center layout */
      .geometric-shape {
        position: absolute;
        border-radius: 50%;
        filter: blur(5px);
        opacity: 0.3;
      }

      .shape1 {
        width: 400px;
        height: 400px;
        background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
        top: -100px;
        left: -100px;
      }

      .shape2 {
        width: 300px;
        height: 300px;
        background: linear-gradient(135deg, var(--color-accent), var(--color-primary));
        bottom: -50px;
        right: -50px;
      }

      .shape3 {
        width: 200px;
        height: 200px;
        background: linear-gradient(135deg, var(--color-secondary), var(--color-accent));
        bottom: 100px;
        left: 50px;
      }

      /* Show/hide elements based on layout */
      .layout-center .image-container,
      .layout-center .gradient-overlay {
        display: none;
      }

      .layout-left .geometric-shape,
      .layout-right .geometric-shape {
        display: none;
      }

      /* Common utility classes */
      .text-primary {
        color: var(--color-text);
      }
      .text-accent {
        color: var(--color-accent);
      }
      .bg-surface {
        background-color: var(--color-surface);
      }
      .bg-card {
        background-color: var(--color-card);
      }
      .bg-primary {
        background-color: var(--color-primary);
      }
      .bg-secondary {
        background-color: var(--color-secondary);
      }
      .border-primary {
        border-color: var(--color-primary);
      }


      /* Generated CSS classes from inline styles */
      .logo-visibility {
        display: var(--show-logo, block);
      }

      .center-logo-visibility {
        display: var(--show-center-logo, block);
      }

</style>
  </head>
  <body>
    <div class="slide layout-{{layout}}">

      <!-- Background shapes for center layout -->
      <div class="geometric-shape shape1"></div>
      <div class="geometric-shape shape2"></div>
      <div class="geometric-shape shape3"></div>

      <!-- Image container for left/right layouts -->
      <div class="image-container">
        {{#if backgroundImage}}
        <img alt="Background Image" src="{{backgroundImage}}">
        {{else}}
        <!-- Fallback gradient background when no image provided -->
        <div class="absolute inset-0 bg-gradient-to-br from-[var(--color-primary)]/20 to-[var(--color-accent)]/20"></div>
        {{/if}}
      </div>
      <div class="gradient-overlay"></div>

      <!-- Top-left logo for left/right layouts -->
      <div class="absolute top-10 left-10 z-10 logo-visibility">
        <!-- Logo placeholder - can be replaced with actual brand logo -->
        {{#if icon}}
        <div class="w-10 h-10 flex items-center justify-center">
          <i class="{{icon}} text-2xl" style="color: var(--color-text)"></i>
        </div>
        {{/if}}
      </div>

      <!-- Main content area -->
      <div class="content-area">

        <!-- Logo for center layout -->
        <div class="mb-4 center-logo-visibility">
          <!-- Logo placeholder - can be replaced with actual brand logo -->
          {{#if icon}}
          <div class="w-10 h-10 flex items-center justify-center mx-auto">
            <i class="{{icon}} text-2xl" style="color: var(--color-text)"></i>
          </div>
          {{/if}}
        </div>

        <!-- Title -->
        <h1 class="title-text">{{title}}</h1>

        <!-- Subtitle -->
        <p class="subtitle-text">{{subTitle}}</p>

        <!-- Accent line -->
        <div class="accent-line"></div>

        <!-- Date -->
        <p class="date-text">{{date}}</p>
      </div>
    </div>

    <script>
      // Control logo visibility based on layout
      const layout = '{{layout}}' || 'center';
      const root = document.documentElement;

      if (layout === 'center') {
        root.style.setProperty('--show-logo', 'none');
        root.style.setProperty('--show-center-logo', 'block');
      } else {
        root.style.setProperty('--show-logo', 'block');
        root.style.setProperty('--show-center-logo', 'none');
      }
    </script>
  </body>
</html>
`;
