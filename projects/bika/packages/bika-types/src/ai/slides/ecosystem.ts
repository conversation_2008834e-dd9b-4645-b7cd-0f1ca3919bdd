export const html = `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>{{title}}</title>
    <!-- Font Awesome CDN -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
      rel="stylesheet"
    />
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts - Inter and Noto Sans SC -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet"/>

    <style>
      body,
      html {
        width: 1366px;
        height: 768px;
        margin: 0;
        padding: 0;
        overflow: hidden;
        font-family: "Inter", "Noto Sans SC", sans-serif;
        color: var(--color-text);
        background-color: var(--color-background);
      }

      :root {
        --color-primary: {{color.primary}};
        --color-secondary: {{color.secondary}};
        --color-text: {{color.text}};
        --color-accent: {{color.accent}};
        --color-background: {{color.background}};
        --color-surface: {{color.surface}};
        --color-card: {{color.card}};
      }

      /*
      Color scheme references are now dynamically provided through template variables.
      All colors are accessible via var(--color-primary), var(--color-secondary), etc.
      */

      .slide-container {
        width: 1366px;
        height: 768px;
        position: relative;
        background-color: var(--color-background);
        padding: 40px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
      }

      /*
      Model card animation options:

      Option 1 - Lift and shadow (current):
      .model-card:hover { transform: translateY(-5px); box-shadow: 0 15px 35px var(--color-primary)22; }

      Option 2 - Scale effect:
      .model-card:hover { transform: scale(1.02); }

      Option 3 - Tilt effect:
      .model-card:hover { transform: perspective(1000px) rotateX(3deg) translateY(-3px); }

      Option 4 - Border glow:
      .model-card:hover { box-shadow: 0 0 20px var(--color-primary)33; }
      */
      .model-card {
        transition: all 0.3s ease;
        cursor: pointer;
      }

      .model-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px var(--color-primary)22;
      }

      /*
      Icon background style options:

      Option 1 - Circle (current):
      border-radius: 50%;

      Option 2 - Rounded square:
      border-radius: 16px;

      Option 3 - Diamond shape:
      transform: rotate(45deg); (icon needs counter-rotation)
      */
      .icon-bg {
        height: 64px;
        width: 64px;
        border-radius: 50%;
      }

      /* Common utility classes */
      .text-primary {
        color: var(--color-text);
      }
      .text-accent {
        color: var(--color-accent);
      }
      .bg-surface {
        background-color: var(--color-surface);
      }
      .bg-card {
        background-color: var(--color-card);
      }

      /*
      Connector line styles:

      Option 1 - Simple lines (current):
      background-color: {{connectors.color}};

      Option 2 - Dotted lines:
      background: linear-gradient(to right, {{connectors.color}} 50%, transparent 50%);
      background-size: 8px 2px;

      Option 3 - Gradient lines:
      background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));

      Option 4 - Animated flow:
      background: linear-gradient(90deg, transparent, var(--color-primary), transparent);
      animation: flow 2s infinite;
      */
      .connector {
        position: absolute;
        z-index: 0;
      }

      /* Animation for flowing connectors */
      @keyframes flow {
        0% { background-position: -100% 0; }
        100% { background-position: 100% 0; }
      }


      /* Generated CSS classes from inline styles */
      .bg-themed {
        background-color: {{footer.bgColor}};
      }

      .border-left-themed {
        background-color: {{this.benefitsBgColor}};
        border-left-color: {{this.borderColor}};
      }

      .dynamic-style-3 {
        color: {{introduction.highlightColor}};
      }

      .border-top-themed {
        border-top-color: {{this.borderColor}};
      }

      .bg-themed-light {
        background-color: {{this.borderColor}};
      }

      .border-themed {
        border-color: {{this.borderColor}};
      }

      .dynamic-style-12 {
        color: {{this.iconColor}};
      }

      .dynamic-style-16 {
        color: {{footer.iconColor}};
      }

</style>
  </head>
  <body>
    <div class="slide-container">
      <!-- Header Section -->
      <div class="mb-6">
        <h1 class="text-4xl font-bold mb-4 text-primary">{{pageTitle}}</h1>
        <!-- Introduction Box -->
        <!--
        Introduction style options:

        Option 1 - Card with shadow (current):
        <div class="rounded-lg p-4 shadow-sm bg-themed">

        Option 2 - Border accent:
        <div class="rounded-lg p-4 border-l-4 border-left-themed">

        Option 3 - Gradient background:
        <div class="rounded-lg p-4 bg-gradient-to-r from-[var(--color-surface)] to-[{{introduction.bgColor}}]">

        Option 4 - Quote style:
        <blockquote class="border-l-4 pl-6 py-4 border-left-themed">
        -->
        <div class="rounded-lg p-4 shadow-sm bg-themed">
          <p class="text-xl text-primary">
            {{introduction.text}}
            <span class="font-bold dynamic-style-3">{{introduction.highlightTerm}}</span>
            {{introduction.textAfterHighlight}}
          </p>
        </div>
      </div>

      <!-- Main Content - Sales Models Grid -->
      <!--
      Grid layout options:

      Option 1 - 2x2 Grid (current):
      <div class="flex-1 grid grid-cols-2 gap-6 relative">

      Option 2 - 2x2 with larger gap:
      <div class="flex-1 grid grid-cols-2 gap-8 relative">

      Option 3 - Responsive grid:
      <div class="flex-1 grid grid-cols-1 lg:grid-cols-2 gap-6 relative">

      Option 4 - Flexible layout:
      <div class="flex-1 flex flex-wrap gap-6 relative">
      -->
      <div class="flex-1 grid grid-cols-2 gap-6 relative">
        <!-- Connectors between cards -->
        {{#if connectors.enabled}}
        <!--
        Connector positioning options:

        Option 1 - Cross connectors (current):
        Horizontal line connecting left and right
        Vertical lines connecting top and bottom

        Option 2 - Star pattern:
        Lines from center to each corner

        Option 3 - Box outline:
        Lines forming a rectangle around all cards
        -->
        <!-- Horizontal connector -->
        <div class="connector bg-themed"></div>
        <!-- Vertical connectors -->
        <div class="connector bg-themed"></div>
        {{/if}}

        <!-- Sales Model Cards -->
        {{#each salesModels}}
        <!--
        Card style options:

        Option 1 - Left border accent (current):
        <div class="model-card rounded-lg shadow-sm p-6 flex flex-col border-l-4 z-10 relative bg-surface border-left-themed">

        Option 2 - Top border accent:
        <div class="model-card rounded-lg shadow-sm p-6 flex flex-col border-t-4 z-10 relative bg-surface border-top-themed">

        Option 3 - Colored background:
        <div class="model-card rounded-lg shadow-sm p-6 flex flex-col z-10 relative bg-themed-light">

        Option 4 - Outline border:
        <div class="model-card rounded-lg shadow-sm p-6 flex flex-col border-2 z-10 relative bg-surface border-themed">
        -->
        <div class="model-card border rounded-lg shadow-sm p-6 flex flex-col border-l-4 z-10 relative bg-card border-left-themed">
          <!-- Card Header -->
          <div class="flex items-center mb-4">
            <div class="icon-bg flex items-center justify-center mr-4 bg-themed">
              <i class="{{this.iconClass}} text-3xl dynamic-style-12"></i>
            </div>
            <h2 class="text-2xl font-bold text-primary">{{this.title}}</h2>
          </div>

          <!-- Description -->
          <p class="opacity-80 mb-4 text-primary">{{this.description}}</p>

          <!-- Benefits Section -->
          <!--
          Benefits section style options:

          Option 1 - Rounded background box (current):
          <div class="mt-auto p-3 rounded-md bg-themed">

          Option 2 - Border accent:
          <div class="mt-auto p-3 border-l-4 rounded-r-md border-left-themed">

          Option 3 - Card style:
          <div class="mt-auto bg-surface border rounded-lg p-3 shadow-sm">

          Option 4 - Simple list:
          <div class="mt-auto space-y-2">
          -->
          <div class="mt-auto p-3 rounded-md bg-themed">
            {{#each this.benefits}}
            <!--
            Benefit item style options:

            Option 1 - Icon with text (current):
            <div class="flex items-center{{#unless @last}} mb-2{{/unless}}">

            Option 2 - Bullet points:
            <div class="flex items-start{{#unless @last}} mb-2{{/unless}}">

            Option 3 - Badge style:
            <span class="inline-flex items-center px-2 py-1 rounded-full text-sm{{#unless @last}} mr-2 mb-1{{/unless}}">
            -->
            <div class="flex items-center{{#unless @last}} mb-2{{/unless}}">
              <i class="{{this.iconClass}} mr-2 dynamic-style-12"></i>
              <p class="text-sm opacity-90 text-primary">{{this.text}}</p>
            </div>
            {{/each}}
          </div>
        </div>
        {{/each}}
      </div>

      <!-- Footer Section -->
      <!--
      Footer style options:

      Option 1 - Centered pill (current):
      <div class="mt-6 flex justify-center">
        <div class="px-8 py-3 rounded-full shadow-sm text-center z-10 bg-themed">

      Option 2 - Full width banner:
      <div class="mt-6 py-4 rounded-lg text-center bg-themed">

      Option 3 - Card style:
      <div class="mt-6 bg-surface border rounded-lg p-4 text-center shadow-sm">

      Option 4 - Simple text:
      <div class="mt-6 text-center">
      -->
      <div class="mt-6 flex justify-center">
        <div class="px-8 py-3 rounded-full shadow-sm text-center z-10 bg-themed">
          <p class="text-primary opacity-90">
            <i class="{{footer.iconClass}} mr-2 dynamic-style-16"></i>
            {{footer.text}}
            <i class="{{footer.iconClass}} ml-2 dynamic-style-16"></i>
          </p>
        </div>
      </div>
    </div>
  </body>
</html>
`;
