export const html = `
<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>{{title}}</title>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
      rel="stylesheet"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700&family=Inter:wght@400;600;700&display=swap" rel="stylesheet"/>

    <style>
      body,
      html {
        width: 1366px;
        height: 768px;
        margin: 0;
        padding: 0;
        overflow: hidden;
        font-family: 'SF Pro Display', 'Inter', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
        color: var(--color-text);
        background-color: var(--color-background);
      }

      :root {
        --color-primary: {{color.primary}};
        --color-secondary: {{color.secondary}};
        --color-text: {{color.text}};
        --color-accent: {{color.accent}};
        --color-background: {{color.background}};
        --color-surface: {{color.surface}};
        --color-card: {{color.card}};
      }

      /* TOC slide container */
      .toc-slide {
        width: 1366px;
        height: 768px;
        position: relative;
        background-color: var(--color-background);
        padding: 40px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
      }

      /* Background decorative elements */
      .slide-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
          radial-gradient(circle at 15% 85%, var(--color-accent)08 0%, transparent 50%),
          radial-gradient(circle at 85% 15%, var(--color-primary)06 0%, transparent 50%);
        pointer-events: none;
      }

      /* Header section */
      .slide-header {
        position: relative;
        z-index: 2;
        text-align: center;
        margin-bottom: 2rem;
      }

      .slide-title {
        font-size: 2.25rem;
        font-weight: 700;
        color: var(--color-accent);
        margin: 0 0 1rem 0;
        line-height: 1.2;
      }

      .slide-subtitle {
        font-size: 1.1rem;
        color: var(--color-text);
        opacity: 0.8;
        margin: 0;
        line-height: 1.4;
      }

      /* TOC container - responsive grid layout */
      .toc-container {
        position: relative;
        z-index: 2;
        flex: 1;
        display: grid;
        gap: 1.5rem;
        min-height: 0;
      }

      /* Dynamic grid layouts based on item count */
      .toc-container[data-count="1"],
      .toc-container[data-count="2"] {
        grid-template-columns: 1fr;
      }

      .toc-container[data-count="3"],
      .toc-container[data-count="4"] {
        grid-template-columns: 1fr 1fr;
      }

      .toc-container[data-count="5"],
      .toc-container[data-count="6"] {
        grid-template-columns: 1fr 1fr;
      }

      .toc-container[data-count="7"],
      .toc-container[data-count="8"] {
        grid-template-columns: 1fr 1fr;
      }

      /* For more than 8 items, use 3 columns */
      .toc-container[data-count="9"],
      .toc-container[data-count="10"],
      .toc-container[data-count="11"],
      .toc-container[data-count="12"] {
        grid-template-columns: 1fr 1fr 1fr;
      }

      /* For more than 12 items, use 4 columns */
      .toc-container[data-count="13"],
      .toc-container[data-count="14"],
      .toc-container[data-count="15"],
      .toc-container[data-count="16"] {
        grid-template-columns: repeat(4, 1fr);
      }

      /* TOC item styling */
      .toc-item {
      /* TOC item styling */
      .toc-item {
        background-color: var(--color-card);
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 2px 12px rgba(0,0,0,0.06);
        border: 1px solid var(--color-primary);
        border-opacity: 0.1;
        position: relative;
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        transition: all 0.3s ease;
        cursor: pointer;
      }

      .toc-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0,0,0,0.12);
        border-opacity: 0.2;
      }

      /* TOC item number */
      .toc-number {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        border: 2px solid var(--color-primary);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--color-primary);
        font-weight: 600;
        font-size: 0.9rem;
        flex-shrink: 0;
        background: transparent;
      }

      /* TOC item content */
      .toc-content {
        flex: 1;
        min-width: 0;
      }

      .toc-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--color-text);
        margin: 0 0 0.5rem 0;
        line-height: 1.3;
      }

      .toc-description {
        font-size: 0.85rem;
        color: var(--color-text);
        opacity: 0.7;
        line-height: 1.4;
        margin: 0;
      }

      .toc-meta {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-top: 0.5rem;
        font-size: 0.75rem;
        color: var(--color-accent);
        opacity: 0.8;
      }

      /* Arrow indicator */
      .toc-arrow {
        color: var(--color-primary);
        opacity: 0.4;
        font-size: 0.8rem;
        flex-shrink: 0;
        transition: all 0.3s ease;
      }

      .toc-item:hover .toc-arrow {
        opacity: 0.8;
        transform: translateX(4px);
      }

      /* Common utility classes */
      .text-primary {
        color: var(--color-text);
      }
      .text-accent {
        color: var(--color-accent);
      }
      .bg-surface {
        background-color: var(--color-surface);
      }
      .bg-card {
        background-color: var(--color-card);
      }
      .bg-primary {
        background-color: var(--color-primary);
      }
      .border-primary {
        border-color: var(--color-primary);
      }

      /* Responsive adjustments */
      .toc-container[data-count="1"] .toc-item,
      .toc-container[data-count="2"] .toc-item {
        padding: 2rem;
      }

      .toc-container[data-count="1"] .toc-title,
      .toc-container[data-count="2"] .toc-title {
        font-size: 1.25rem;
      }

      .toc-container[data-count="1"] .toc-description,
      .toc-container[data-count="2"] .toc-description {
        font-size: 0.95rem;
      }

      /* For 4+ columns, make items more compact */
      .toc-container[data-count="13"] .toc-item,
      .toc-container[data-count="14"] .toc-item,
      .toc-container[data-count="15"] .toc-item,
      .toc-container[data-count="16"] .toc-item {
        padding: 1rem;
      }

      .toc-container[data-count="13"] .toc-title,
      .toc-container[data-count="14"] .toc-title,
      .toc-container[data-count="15"] .toc-title,
      .toc-container[data-count="16"] .toc-title {
        font-size: 1rem;
      }

      .toc-container[data-count="13"] .toc-description,
      .toc-container[data-count="14"] .toc-description,
      .toc-container[data-count="15"] .toc-description,
      .toc-container[data-count="16"] .toc-description {
        font-size: 0.8rem;
      }
    </style>
  </head>
  <body>
    <div class="toc-slide">
      <!-- Background decorative elements -->
      <div class="slide-background"></div>

      <!-- Header section -->
      <div class="slide-header">
        <h1 class="slide-title">{{title}}</h1>
        {{#if subtitle}}
        <p class="slide-subtitle">{{subtitle}}</p>
        {{/if}}
      </div>

      <!-- TOC container with dynamic count attribute -->
      <div class="toc-container" data-count="{{items.length}}">
        {{#each items}}
        <div class="toc-item">
          <!-- TOC number -->
          <div class="toc-number">{{number}}</div>

          <!-- TOC content -->
          <div class="toc-content">
            <h3 class="toc-title">{{title}}</h3>
            {{#if description}}
            <p class="toc-description">{{description}}</p>
            {{/if}}
            {{#if duration}}
            <div class="toc-meta">
              <i class="fas fa-clock"></i>
              <span>{{duration}}</span>
            </div>
            {{/if}}
          </div>

          <!-- Arrow indicator -->
          <div class="toc-arrow">
            <i class="fas fa-chevron-right"></i>
          </div>
        </div>
        {{/each}}
      </div>
    </div>
  </body>
</html>
`;
