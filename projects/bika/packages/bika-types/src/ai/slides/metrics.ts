export const html = `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>{{title}}</title>
    <!-- Font Awesome CDN -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
      rel="stylesheet"
    />
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts - Inter and Noto Sans SC -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet"/>
    <!-- D3.js CDN for data visualization (optional) -->
    <!-- <script src="https://d3js.org/d3.v7.min.js"></script> -->
    <style>
      body,
      html {
        width: 1366px;
        height: 768px;
        margin: 0;
        padding: 0;
        overflow: hidden;
        font-family: "Inter", "Noto Sans SC", sans-serif;
        color: var(--color-text);
        background-color: var(--color-background);
      }
      :root {
        --color-primary: {{color.primary}};
        --color-secondary: {{color.secondary}};
        --color-text: {{color.text}};
        --color-accent: {{color.accent}};
        --color-background: {{color.background}};
        --color-surface: {{color.surface}};
        --color-card: {{color.card}};
      }

      .slide-container {
        width: 1366px;
        height: 768px;
        position: relative;
        background-color: var(--color-background);
        padding: 40px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
      }

      /*
      Data visualization animation options:

      Option 1 - Migration flow animation:
      @keyframes migrate { 0% { background-position: 0% 50%; } 100% { background-position: 100% 50%; } }
      .migration-arrow { animation: migrate 3s infinite; }

      Option 2 - Data point hover effects:
      .data-point { transition: all 0.3s ease; }
      .data-point:hover { transform: scale(1.05); }

      Option 3 - Pulse animation for highlights:
      @keyframes pulse-glow { 0%, 100% { box-shadow: 0 0 5px var(--color-primary)66; } 50% { box-shadow: 0 0 20px var(--color-primary)99; } }
      .highlight-metric { animation: pulse-glow 2s infinite; }
      */

      .highlight-box {
        border-left: 4px solid var(--color-primary);
      }

      .migration-arrow {
        position: relative;
        height: 6px;
        background: linear-gradient(to right, var(--color-primary), var(--color-accent));
        border-radius: 3px;
      }

      .data-point {
        transition: all 0.3s ease;
        cursor: pointer;
      }

      .data-point:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px var(--color-primary)33;
      }

      /*
      Card icon background style options:

      Option 1 - Circular with primary color:
      background-color: var(--color-primary)22;

      Option 2 - Square with rounded corners:
      border-radius: 12px;

      Option 3 - Gradient background:
      background: linear-gradient(135deg, var(--color-primary)22, var(--color-secondary)22);
      */

      .card-icon-bg {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--color-primary)22;
        margin-right: 12px;
        flex-shrink: 0;
      }

      .card-icon {
        font-size: 1.5rem;
        color: var(--color-primary);
      }

      /* Common utility classes */
      .text-primary {
        color: var(--color-text);
      }
      .text-accent {
        color: var(--color-accent);
      }
      .bg-surface {
        background-color: var(--color-surface);
      }
      .bg-card {
        background-color: var(--color-card);
      }
      .bg-primary {
        background-color: var(--color-primary);
      }
      .bg-secondary {
        background-color: var(--color-secondary);
      }
      .border-primary {
        border-color: var(--color-primary);
      }


      /* Generated CSS classes from inline styles */
      .ring-themed {
        --tw-ring-color: var(--color-accent);
        {{/if}};
      }

      .dynamic-style-1 {
        {{/if}};
      }

      .border-themed {
        background-color: var(--color-accent)11;
        border-color: var(--color-accent);
      }

      .bg-themed {
        background-color: {{this}};
      }

</style>
  </head>
  <body>
    <div class="slide-container">
      <!-- Header Section -->
      <div class="mb-6">
        <h1 class="text-4xl font-bold text-primary mb-2">{{pageTitle}}</h1>
        <!--
        Title underline style options:

        Option 1 - Solid color underline:
        <div class="w-32 h-1 bg-primary rounded"></div>

        Option 2 - Gradient underline:
        <div class="w-32 h-1 bg-gradient-to-r from-[var(--color-primary)] to-[var(--color-accent)] rounded"></div>

        Option 3 - No underline (remove entirely)

        Option 4 - Double lines:
        <div class="flex space-x-2">
            <div class="w-16 h-1 bg-primary rounded"></div>
            <div class="w-8 h-1 bg-secondary rounded"></div>
        </div>
        -->
        <div class="w-32 h-1 bg-primary rounded"></div>
      </div>

      <!-- Main Content Container -->
      <div class="flex flex-1 space-x-6">
        <!-- Left Column: Performance and Capabilities -->
        <div class="w-1/2 flex flex-col space-y-5">
          <!-- Performance Metrics Card -->
          <!--
          Performance card style options:

          Option 1 - Standard white card:
          <div class="bg-surface rounded-lg shadow-sm p-5 flex-1">

          Option 2 - Colored background:
          <div class="bg-primary text-white rounded-lg shadow-lg p-5 flex-1">

          Option 3 - Gradient background:
          <div class="bg-gradient-to-br from-[var(--color-primary)] to-[var(--color-secondary)] text-white rounded-lg shadow-lg p-5 flex-1">
          -->
          <div class="bg-card border   rounded-lg shadow-sm p-5 flex-1">
            <div class="flex items-center mb-4">
              <div class="card-icon-bg">
                <i class="{{leftColumn.performanceCard.iconClass}} card-icon"></i>
              </div>
              <h2 class="text-xl font-semibold text-primary">{{leftColumn.performanceCard.title}}</h2>
            </div>

            <!-- Performance Metrics Grid -->
            <div class="grid grid-cols-2 gap-4">
              {{#each leftColumn.performanceCard.metrics}}
              <!--
              Metric display style options:

              Option 1 - Highlighted metric:
              <div class="bg-primary22 rounded-lg p-3 text-center data-point {{#if this.highlight}}ring-2 ring-gray-200 ring-themed">

              Option 2 - Standard metric:
              <div class="bg-surface rounded-lg p-3 text-center data-point">

              Option 3 - Card-style metric:
              <div class="bg-white border border-primary33 rounded-lg p-3 text-center data-point">
              -->
              <div class="bg-primary11 rounded-lg p-3 text-center data-point {{#if this.highlight}}ring-2 ring-gray-200 ring-themed">
                <p class="text-sm text-primary opacity-70">{{this.label}}</p>
                <p class="text-2xl font-bold text-primary">{{this.value}}</p>
              </div>
              {{/each}}
            </div>
          </div>

          <!-- Capabilities Card -->
          <div class="bg-card border   rounded-lg shadow-sm p-5 flex-1">
            <div class="flex items-center mb-4">
              <div class="card-icon-bg">
                <i class="{{leftColumn.capabilitiesCard.iconClass}} card-icon"></i>
              </div>
              <h2 class="text-xl font-semibold text-primary">{{leftColumn.capabilitiesCard.title}}</h2>
            </div>

            <!-- Capabilities List -->
            <ul class="space-y-3">
              {{#each leftColumn.capabilitiesCard.items}}
              <!--
              Capability item style options:

              Option 1 - With checkmark icon:
              <li class="flex items-start">
                  <i class="fas fa-check-circle text-accent mt-1 mr-2 flex-shrink-0"></i>
                  <p class="text-primary opacity-80 {{#if this.highlighted}}font-semibold{{/if}}">{{this.text}}</p>
              </li>

              Option 2 - With bullet points:
              <li class="flex items-start">
                  <div class="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <p class="text-primary opacity-80">{{this.text}}</p>
              </li>

              Option 3 - Simple list without icons:
              <li class="text-primary opacity-80 pl-4 border-l-2 border-primary22">{{this.text}}</li>
              -->
              <li class="flex items-start">
                <i class="fas fa-check-circle text-accent mt-1 mr-2 flex-shrink-0"></i>
                <p class="text-primary opacity-80 {{#if this.highlighted}}font-semibold text-primary dynamic-style-1">{{this.text}}</p>
              </li>
              {{/each}}
            </ul>
          </div>
        </div>

        <!-- Right Column: Case Study -->
        <div class="w-1/2 bg-card border   rounded-lg shadow-sm p-5">
          <div class="flex items-center mb-4">
            <div class="card-icon-bg">
              <i class="{{rightColumn.caseStudyCard.iconClass}} card-icon"></i>
            </div>
            <h2 class="text-xl font-semibold text-primary">{{rightColumn.caseStudyCard.title}}</h2>
          </div>

          <!-- Highlight Summary Box -->
          <div class="highlight-box pl-4 py-3 mb-5 bg-primary11 rounded-r-lg">
            <p class="text-primary opacity-90">{{rightColumn.caseStudyCard.highlightBox.text}}</p>
          </div>

          <!-- Migration Visualization -->
          <div class="relative mb-6 px-2">
            <!-- Migration Steps -->
            <div class="flex justify-between items-center mb-3">
              {{#each rightColumn.caseStudyCard.migrationSteps}}
              <!--
              Migration step style options:

              Option 1 - Card style:
              <div class="bg-surface rounded-lg p-3 text-center flex-1 mx-1">

              Option 2 - Icon-focused style:
              <div class="text-center flex-1">
                  <div class="bg-primary22 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2">
                      <i class="{{this.iconClass}} text-primary"></i>
                  </div>
                  <p class="text-xs font-medium text-primary opacity-70">{{this.label}}</p>
              </div>
              -->
              <div class="bg-surface rounded-lg p-3 text-center flex-1 mx-1">
                <i class="{{this.iconClass}} text-primary opacity-70 mb-1 text-lg"></i>
                <p class="text-xs font-medium text-primary opacity-80">{{this.label}}</p>
              </div>
              {{/each}}
            </div>

            <!-- Migration Arrow -->
            <div class="migration-arrow w-full my-4 relative">
              <div class="absolute right-0 top-1/2 transform -translate-y-1/2">
                <div class="w-0 h-0 border-l-8 border-l-[var(--color-accent)] border-y-4 border-y-transparent"></div>
              </div>
            </div>

            <!-- Target Platform -->
            <div class="flex justify-center">
              <div class="bg-primary22 rounded-lg p-4 text-center">
                <i class="{{rightColumn.caseStudyCard.targetPlatform.iconClass}} text-primary mb-2 text-2xl"></i>
                <p class="text-sm font-semibold text-primary">{{rightColumn.caseStudyCard.targetPlatform.name}}</p>
              </div>
            </div>
          </div>

          <!-- Success Quote -->
          <!--
          Quote style options:

          Option 1 - Success highlight (green):
          <div class="bg-surface border-themed">

          Option 2 - Primary color highlight:
          <div class="bg-primary11 rounded-lg p-4 border-l-4 border-primary">

          Option 3 - Simple quote style:
          <div class="bg-surface border border-primary33 rounded-lg p-4">
          -->
          <div class="bg-surface rounded-lg p-4 border-l-4 border-gray-200 mb-4 border-themed">
            <p class="text-primary opacity-90 italic">
              <i class="fas fa-quote-left text-accent mr-2 opacity-60"></i>
              {{rightColumn.caseStudyCard.resultCard.quoteText}}
              <i class="fas fa-quote-right text-accent ml-2 opacity-60"></i>
            </p>
            {{#if rightColumn.caseStudyCard.resultCard.quoteAuthor}}
            <p class="text-sm text-primary opacity-60 mt-2 text-right">— {{rightColumn.caseStudyCard.resultCard.quoteAuthor}}</p>
            {{/if}}
          </div>

          <!-- Bottom Indicator -->
          <div class="flex justify-center">
            <div class="flex items-center space-x-3">
              {{#each rightColumn.caseStudyCard.bottomIndicator.colors}}
              <div class="w-3 h-3 rounded-full bg-themed"></div>
              {{/each}}
              <span class="text-sm text-primary opacity-60">{{rightColumn.caseStudyCard.bottomIndicator.text}}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer Message -->
      <!--
      Footer style options:

      Option 1 - Highlighted footer:
      <div class="mt-6 bg-primary11 rounded-lg p-4 text-center">

      Option 2 - Simple text footer:
      <div class="mt-6 text-center">

      Option 3 - Card-style footer:
      <div class="mt-6 bg-surface border border-primary33 rounded-lg p-4 text-center">
      -->
      <div class="mt-6 bg-primary11 rounded-lg p-4 text-center">
        <p class="text-primary opacity-90">
          <i class="{{footerMessage.iconClass}} text-accent mr-2"></i>
          {{footerMessage.text}}
        </p>
      </div>
    </div>
  </body>
</html>
`;
