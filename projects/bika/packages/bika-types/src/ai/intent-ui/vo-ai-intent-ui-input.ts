import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import { BaseAIIntentUIVOSchema } from '../vo-intent-ui-base';

export const InputAIIntentUIVOSchema = BaseAIIntentUIVOSchema.extend({
  type: z.literal('INPUT'),
  inputType: z.string().default('text').optional().describe('Input type'),
  name: iStringSchema.optional().describe('Field name, if empty, use title'),
  defaultValue: iStringSchema,
});

export type InputAIIntentUIVO = z.infer<typeof InputAIIntentUIVOSchema>;
