import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import { NodeResourceSchema } from '../../node/bo';
import { BaseAIIntentUIVOSchema } from '../vo-intent-ui-base';
import { AIIntentUITypeSchema } from '../vo-intent-ui-types';

export const FlowAIIntentUIVOSchema = BaseAIIntentUIVOSchema.extend({
  type: z.literal(AIIntentUITypeSchema.enum.FLOW),
  title: iStringSchema,
  resources: z.array(NodeResourceSchema),
  hiddenButtons: z.array(z.enum(['CONFIRM', 'AGAIN', 'CANCEL'])).optional(),
}).describe('Architecture UI with ResourcesBO');

export type FlowAIIntentUIVO = z.infer<typeof FlowAIIntentUIVOSchema>;
