import { AvatarLogoSchema } from 'basenext/avatar';
import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import { BaseAIIntentUIVOSchema } from '../vo-intent-ui-base';

export const ChoicesAIIntentUIChoiceItemSchema = z.object({
  key: z.string(),
  value: z.string().optional(),
  render: iStringSchema,
  img: z.union([AvatarLogoSchema, z.string()]).optional(),
  description: iStringSchema.optional(),
});

export type ChoicesAIIntentUIChoiceItemVO = z.infer<typeof ChoicesAIIntentUIChoiceItemSchema>;

export const ChoicesAIIntentUIVOSchema = BaseAIIntentUIVOSchema.extend({
  type: z.literal('CHOICES'),
  choices: z.array(ChoicesAIIntentUIChoiceItemSchema),
});
export type ChoicesAIIntentUIVO = z.infer<typeof ChoicesAIIntentUIVOSchema>;
