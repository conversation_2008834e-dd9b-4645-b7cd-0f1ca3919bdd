import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import { BaseAIIntentUIVOSchema } from '../vo-intent-ui-base';

export const ConfirmAIIntentUIVOSchema = BaseAIIntentUIVOSchema.extend({
  type: z.literal('CONFIRM'),
  yes: iStringSchema.optional().describe('Text for yes button'),
  no: iStringSchema.optional().describe('Text for no button'),
  default: z.boolean().optional().describe('Default dialog'),
  yesOnly: z.boolean().optional().describe('Whether to show only yes button and hide no button'),
  yesAction: z
    .union([z.literal('CLOSE'), z.literal('REDIRECT')])
    .optional()
    .describe('Action after clicking yes button'),
  redirect: z.string().optional().describe('Redirect URL after clicking yes button'),
}).describe('Yes/No UI control, yes or no');

export type ConfirmAIIntentUIVO = z.infer<typeof ConfirmAIIntentUIVOSchema>;
