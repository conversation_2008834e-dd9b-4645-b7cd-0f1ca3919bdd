import { z } from 'zod';
import { html as industryCasesHtml } from './slides/cases';
import { html as productComparisonHtml } from './slides/comparison';
import { html as servicesPageHtml } from './slides/content';
import { html as customPageHtml } from './slides/custom';
import { html as echartsHtml } from './slides/echarts';
import { html as partnerEcosystemHtml } from './slides/ecosystem';
import { html as endingPageHtml } from './slides/ending';
import { html as productFeaturesHtml } from './slides/features';
import { html as metricsHtml } from './slides/metrics';
import { html as strategyOverviewHtml } from './slides/overview';
import { html as businessPlatformHtml } from './slides/platform';
import { html as pricingModelsHtml } from './slides/pricing';
import { html as titltHtml } from './slides/title';
import { html as tableOfContentsHtml } from './slides/toc';
import { html as videoPageHtml } from './slides/video';

export const SLIDES_TEMPLATE = {
  title: titltHtml,
  content: servicesPageHtml,
  metrics: metricsHtml,
  echarts: echartsHtml,
  features: productFeaturesHtml,
  platform: businessPlatformHtml,
  overview: strategyOverviewHtml,
  cases: industryCasesHtml,
  ecosystem: partnerEcosystemHtml,
  pricing: pricingModelsHtml,
  comparison: productComparisonHtml,
  ending: endingPageHtml,
  custom: customPageHtml,
  video: videoPageHtml,
  toc: tableOfContentsHtml,
};

export const SlideSchema = z.object({
  html_content: z.string(),
  status: z.enum(['pending', 'generating', 'completed']).describe('Slide generation status'),
});

export const SlidesSchema = z.array(SlideSchema);

export const SlidesArtifactSchema = z.object({
  slides: SlidesSchema,
});

export const getSlidesArtifactSchemaWithLength = (length: number) =>
  z.object({
    slides: SlidesSchema.describe(
      'STRICTLY generate slides according to the EXACT number specified in the outline, IMPORTANT: The generated HTML must NOT contain any HTML comments (<!-- -->). Only include the actual functional HTML markup without any explanatory comments.',
    ).length(length),
  });

export type SlidesArtifactVO = z.infer<typeof SlidesArtifactSchema>;
export type Slide = z.infer<typeof SlideSchema>;

export const OutlineSchema = z.object({
  title: z.string().describe('The title of the presentation'),
  outline: z
    .array(
      z.object({
        title: z.string().describe('The title of the slide'),
        description: z
          .string()
          .describe("A detailed description of the slide content (make sure AI doesn't miss any user requirements)"),
        layout: z.enum([
          'title',
          'toc',
          'content',
          'echarts',
          'metrics',
          'features',
          'platform',
          'overview',
          'cases',
          'ecosystem',
          'pricing',
          'comparison',
          'ending',
          'custom',
          'video',
          // 'timeline',      // TODO: Template not implemented yet
          // 'team',          // TODO: Template not implemented yet
          // 'process',       // TODO: Template not implemented yet
          // 'testimonials',  // TODO: Template not implemented yet
          // 'contact',       // TODO: Template not implemented yet
          // 'agenda',        // TODO: Template not implemented yet
          // 'stats',         // TODO: Template not implemented yet
        ]).describe(`Choose layout based on content type:
- title: Cover page with 3 layout modes (center/left/right) - for opening slides
- toc: 2-column TOC grid - for course outline, agenda, chapters
- content: Top header + 3-column cards - for services, products overview, general content
- metrics: 2-column performance data - for KPIs, capabilities, case studies with detailed metrics
- echarts: Responsive chart grid (1-4 charts) - for data visualization, analytics, dashboards
- features: Left text + right feature grid - for product capabilities, feature highlights
- platform: 2-column detailed architecture view - for system architecture, technical platform intro
- overview: 3-pillar strategy layout - for strategic frameworks, key principles, high-level concepts
- cases: 3-column case study cards - for success stories, customer examples, industry cases
- ecosystem: Hub-spoke network diagram - for partnerships, integrations, relationships
- pricing: Multi-column pricing cards - for plans, packages, cost comparison
- comparison: Table-based feature comparison - for competitive analysis, before/after scenarios
- ending: Full-screen centered - for thank you, conclusions, contact
- custom: Flexible container - for any special content not fitting above layouts
- video: 16:9 video container - for multimedia, demos, presentations
`),
      }),
    )
    .describe('The structured outline of the slides'),
  color: z
    .object({
      primary: z.string(),
      secondary: z.string(),
      text: z.string(),
      accent: z.string(),
      background: z.string(),
      surface: z.string(),
      card: z.string(),
    })
    .describe(
      'Defines the global color theme for the presentation. A modern, harmonious, and aesthetically pleasing color palette (e.g., professional dark or clean light themes) should be chosen based on user preferences and content. All colors should be provided in rgb(xx,xx,xx) or rgba(xx,xx,xx,xx) format. The "card" color should be used for card backgrounds to ensure proper contrast and visibility. AI should use these colors consistently throughout all slides to maintain visual coherence.',
    ),
});
export type SlidesOutline = z.infer<typeof OutlineSchema>;
