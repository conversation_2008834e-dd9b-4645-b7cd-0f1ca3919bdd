import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import { AIIntentUITypeSchema } from './vo-intent-ui-types';

export const BaseAIIntentUIVOSchema = z.object({
  type: AIIntentUITypeSchema,
  title: iStringSchema.optional(),
  // intent: z.string().optional().describe('Used to identify this as a template selector, frontend needs to handle entry hiding logic'),
  description: z.string().optional(),
  hiddenInput: z.boolean().optional().describe('Hide text input box?'),
  hiddenClosable: z.boolean().optional().describe('Hide close button?'),
});

export type BaseAIIntentUIVO = z.infer<typeof BaseAIIntentUIVOSchema>;

export const BaseAIIntentUIResolveDTOSchema = z.object({
  type: AIIntentUITypeSchema,
});

export type BaseAIIntentUIResolveDTO = z.infer<typeof BaseAIIntentUIResolveDTOSchema>;
