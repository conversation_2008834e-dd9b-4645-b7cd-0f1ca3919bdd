/**
 * Configuration for a step wizard, used as parameters for AIIntent
 */
import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import { StepWizardTypeSchema } from './step-wizard-types';

// Step Wizard has its own Params! Any flexible object to simplify Step Wizard configuration,
// internal config handles zod validation
export const StepWizardParamsSchema = z.any().optional().describe('The params of all the steps');
export type StepWizardParams = z.infer<typeof StepWizardParamsSchema>;

// AI Intent Params
export const StepWizardIntentParamsAIOSchema = z.object({
  name: iStringSchema.optional(),
  type: z.literal('STEP_WIZARD').describe('The step by step wizard, ai intent type'),
  // References StepWizardConfigsMap
  stepWizardType: StepWizardTypeSchema.optional().describe('The name of the step wizard'),
  step: z.number().optional().describe('Current step number'),
  // Step Wizard has its own encapsulated params
  stepWizardParams: StepWizardParamsSchema,
});
export type StepWizardIntentParamsAIO = z.infer<typeof StepWizardIntentParamsAIOSchema>;
