import { Locale, iString } from 'basenext/i18n';
import { StepWizardParams } from './step-wizard-intent-params';
import { ApiFetchRequestContext } from '../../user/vo-user';
import { AIIntentParams } from '../bo-intent-params';
import { AIIntentUIResolveDTO } from '../dto';
import { ConfirmAIIntentUIVO } from '../intent-ui/vo-ai-intent-ui-confirm';
import { IntentResolverCompleteResult, AIIntentResolverState } from '../vo-intent-resolver';
import { AIIntentUIVO } from '../vo-intent-ui';

// export type StepWizardConfig_Step = z.infer<typeof StepWizardConfigStepSchema>;
export interface StepWizardConfig_Step {
  /**
   * Should this step be started based on condition check? If false, the step will be skipped
   * @param _stepWizardParams
   * @returns
   */
  startCondition?: (
    _intentResolver: AIIntentResolverState,
    _stepWizardParams: StepWizardParams,
  ) => Promise<{ stepWizardParams: StepWizardParams; start: boolean }>;

  // Static voice broadcast address, fill in URL, AI, or none
  staticVoice?: iString;
  message: iString;
  ui: (_stepWizardParams: StepWizardParams) => Promise<AIIntentUIVO>;
  uiResolve: (
    _uiResolveObj: AIIntentUIResolveDTO,
    _stepWizardParams: StepWizardParams,
    userInfo: { userId: string; locale: Locale },
  ) => Promise<{ stepWizardParams: StepWizardParams; nextStep: number | boolean }>;
}

// export const StepWizardConfigSchema = z.object({
//   name: z.string(),
//   steps: z.array(StepWizardConfigStepSchema),
//   doComplete: z.function().args(z.any(), StepWizardParamsSchema).returns(z.promise(
//     z.object({
//       nextIntent: z.null().or(
//         z.lazy(() => AIIntentParamsSchema),
//       ),
//     }),
//   )),
// });
export interface StepWizardConfig {
  name: iString;
  steps: StepWizardConfig_Step[];
  closable?: boolean;
  // Message to display when successful
  successMessage?: iString;

  // Voice to play when successful
  successStaticVoice?: iString;

  // parseUI after doCompletion, used in some scenarios for final processing
  successUI?: (_completeResult?: IntentResolverCompleteResult) => Promise<ConfirmAIIntentUIVO>;

  doComplete: (
    _stepWizardParams: StepWizardParams,
    _intentResolver: AIIntentResolverState,
    ctx: ApiFetchRequestContext,
  ) => Promise<{ nextIntent: AIIntentParams | null; args?: unknown }>;
}

// export type StepWizardConfig = z.infer<typeof StepWizardConfigSchema>;
