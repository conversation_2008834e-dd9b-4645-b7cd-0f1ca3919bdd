import { Locale, iStringParse } from 'basenext/i18n';
import type { AIIntentType } from './bo-intent-types';
import { AINodeCreateDTO, AIPageNodeCreateDTO } from './dto';
import { AIWizardVO } from './vo-ai';
import type { AIIntentUIVO } from './vo-intent-ui';
import type { AIIntentUIType } from './vo-intent-ui-types';
import { defaultTemplate } from '../utils/default-template';

export * from './default-launcher-commands';

export function defaultIntentUIVO(intentUIType: AIIntentUIType): AIIntentUIVO {
  switch (intentUIType) {
    case 'CHOICES': {
      return {
        hiddenInput: true,
        type: 'CHOICES',
        choices: [
          {
            key: 'choice-1',
            render: 'Choice 1',
          },
          {
            key: 'choice-2',
            render: 'Choice 2',
          },
        ],
      };
    }
    case 'CONFIRM': {
      return {
        type: 'CONFIRM',
        yes: 'Yes',
        no: 'No',
      };
    }
    case 'BILLING': {
      return {
        type: 'BILLING',
      };
    }
    case 'QUESTIONAIRE': {
      return {
        type: 'QUESTIONAIRE',
      };
    }
    case 'INPUT': {
      return {
        type: 'INPUT',
        defaultValue: 'Default Value',
      };
    }
    case 'PROMPT': {
      return {
        type: 'PROMPT',
        prompts: ['Prompt 1', 'Prompt 2'],
      };
    }
    case 'AUTH': {
      return {
        type: 'AUTH',
      };
    }
    case 'GUIDE': {
      return {
        type: 'UI_GUIDE',
      };
    }
    case 'FLOW': {
      return {
        type: 'FLOW',
        title: 'Flow Title',
        resources: defaultTemplate.resources,
      };
    }
    case 'TEMPLATES': {
      return {
        type: 'TEMPLATES',
        // templateId: 'base-crm',
        choices: [
          {
            key: 'template-1',
            render: 'Template 1',
          },
          {
            key: 'template-2',
            render: 'Template 2',
          },
        ],
        hiddenInput: true,
        hiddenClosable: true,
      };
    }
    case 'LAUNCHER_COMMANDS':
      return {
        type: 'LAUNCHER_COMMANDS',
        commands: [],
      };
    default:
      throw new Error(`Unknown intent UI type: ${intentUIType}`);
  }
}

export function defaultAIWizardVO(intentType: AIIntentType, intentUIType: AIIntentUIType): AIWizardVO {
  return {
    id: '',
    title: 'AI Wizard Title',
    resolutionStatus: 'NOT_STARTED',
    messages: [
      {
        id: 'ttt',
        role: 'assistant',
        parts: [
          {
            type: 'text',
            text: 'This is a message from AI',
          },
        ],
        voice: {
          'zh-CN': '/assets/audios/zh-CN/onboarding-init-2.mp3',
          en: '/assets/audios/en/onboarding-init-2.mp3',
          ja: '/assets/audios/ja/onboarding-init-2.mp3',
          'zh-TW': '/assets/audios/zh-CN/onboarding-init-2.mp3',
        },
        ui: defaultIntentUIVO(intentUIType),
      },
      {
        id: 'ttt',
        parts: [
          {
            type: 'text',
            text: 'This ia a message from Human',
          },
        ],

        role: 'user',
      },
      {
        id: 'ttt',
        parts: [
          {
            type: 'text',
            text: 'This ia a message from AI2',
          },
        ],
        role: 'assistant',
        voice: {
          'zh-CN': '/assets/audios/zh-CN/onboarding-init-2.mp3',
          en: '/assets/audios/en/onboarding-init-2.mp3',
          ja: '/assets/audios/ja/onboarding-init-2.mp3',
          'zh-TW': '/assets/audios/zh-CN/onboarding-init-2.mp3',
        },
        ui: defaultIntentUIVO(intentUIType),
      },
      {
        id: 'ttt',
        parts: [
          {
            type: 'text',
            text: 'This ia a message from Human2',
          },
        ],
        role: 'user',
      },
      {
        id: 'ttt',
        parts: [
          {
            type: 'text',
            text: 'This ia a message from Human3',
          },
        ],
        role: 'user',
      },
    ],
    intent: {
      type: 'CREATE_REMINDER',
      name: 'Create Reminder Intent',
      description: 'Create Reminder Intent Description',
    },
  };
}

export function defaultAIAgentNodeCreateDTO(locale: Locale): AINodeCreateDTO {
  return {
    resourceType: 'AI',
    name: iStringParse(
      {
        en: 'AI Agent',
        'zh-CN': 'AI 智能体',
        'zh-TW': 'AI 智能體',
        ja: 'AIエージェント',
      },
      locale,
    ),
    description: iStringParse(
      {
        en: 'AI Agent Description',
        'zh-CN': 'AI 智能体描述',
        'zh-TW': 'AI 智能體描述',
        ja: 'AIエージェントの説明',
      },
      locale,
    ),
  };
}

export function defaultAIPageNodeCreateDTO(locale: Locale): AIPageNodeCreateDTO {
  return {
    resourceType: 'PAGE',
    name: iStringParse(
      {
        en: 'AI Page',
        'zh-CN': 'AI 页面',
        'zh-TW': 'AI 頁面',
        ja: 'AI ページ',
      },
      locale,
    ),
    description: iStringParse(
      {
        en: 'AI Page Description',
        'zh-CN': 'AI 页面描述',
        'zh-TW': 'AI 頁面描述',
        ja: '説明',
      },
      locale,
    ),
  };
}
