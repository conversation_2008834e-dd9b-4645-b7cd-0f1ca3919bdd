import { z } from 'zod';
import { TalkExpertKeySchema } from '../../space/bo-talk';
import { AIIntentTypeSchema } from '../bo-intent-types';

export const SuperAgentIntentParamsSchema = z.object({
  type: z.literal(AIIntentTypeSchema.enum.SUPERVISOR),
  spaceId: z.string().optional(),
  name: z.string().optional(),
  agent: z
    .object({
      type: z.literal('expert'),
      expertKey: TalkExpertKeySchema,
    })
    .optional(),
});

export type SuperAgentIntentParams = z.infer<typeof SuperAgentIntentParamsSchema>;
