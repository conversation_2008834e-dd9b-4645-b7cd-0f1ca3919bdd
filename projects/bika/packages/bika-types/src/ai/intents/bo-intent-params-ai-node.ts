import { AvatarLogoSchema } from 'basenext';
import { z } from 'zod';
import { AIIntentTypeSchema } from '../bo-intent-types';

export const AINodeIntentParamsSchema = z.object({
  type: z.literal(AIIntentTypeSchema.enum.AI_NODE),
  nodeId: z.string().optional(),
  icon: AvatarLogoSchema.nullish(),
  nodeName: z.string().optional(),
});

export type AINodeIntentParams = z.infer<typeof AINodeIntentParamsSchema>;
