import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';

export const SMTPEmailAccountName = z.literal('SMTP Email');
export const SMTPEmailAccountDescription = z.literal('SMTP Email');
export const IMAPEmailAccountName = z.literal('IMAP Email');
export const IMAPEmailAccountDescription = z.literal('IMAP Email');
export const Webhook = z.literal('WEBHOOK');
export const WebhookName = z.literal('WebHook Integration');
export const WebhookDescription = z.literal('WebHook Integration');
export const MySQL = z.literal('MYSQL');
export const MySQLName = z.literal('MySQL Data Source Integration');
export const MySQLDescription = z.literal('MySQL Data Source Integration');
export const PostgreSQL = z.literal('POSTGRESQL');
export const PostgreSQLName = z.literal('PostgreSQL Data Source Integration');
export const PostgreSQLDescription = z.literal('PostgreSQL Data Source Integration');
export const Vika = z.literal('VIKA');
export const VikaName = z.literal('Vika Data Source Integration');
export const VikaDescription = z.literal('Vika Data Source Integration');
export const HardwareDevice = z.literal('HARDWARE_DEVICE');
export const HardwareDeviceName = z.literal('Hardware Device Binding');
export const HardwareDeviceDescription = z.literal('Hardware Device Integration Binding');
export const Wechat = z.literal('WECHAT');
export const WechatName = z.literal('WeChat Integration');
export const WechatDescription = z.literal('WeChat Integration');
export const Google = z.literal('GOOGLE');
export const GoogleName = z.literal('Google Integration');
export const GoogleDescription = z.literal('Google Integration');
export const GitHub = z.literal('GITHUB');
export const GitHubName = z.literal('GitHub Integration');
export const GitHubDescription = z.literal('GitHub Integration');
export const LinkedIn = z.literal('LINKEDIN');
export const LinkedInName = z.literal('LinkedIn Integration');
export const LinkedInDescription = z.literal('LinkedIn Integration');
export const Twitter = z.literal('TWITTER');
export const Telegram = z.literal('TELEGRAM');
export const DingTalk = z.literal('DING_TALK');
export const FeiShu = z.literal('FEI_SHU');
export const Slack = z.literal('SLACK');
export const WeCom = z.literal('WE_COM');
export const DingDingName = z.literal('DingTalk Integration');
export const Siri = z.literal('SIRI');
export const SiriName = z.literal('Siri Integration');
export const SiriDescription = z.literal('Siri Integration');

export const UserIntegrationTypes = ['HARDWARE_DEVICE', 'SIRI', 'WECHAT', 'GOOGLE', 'GITHUB'] as const;
export const UserIntegrationTypeSchema = z.enum(UserIntegrationTypes);
export type UserIntegrationType = z.infer<typeof UserIntegrationTypeSchema>;

export const SpaceIntegrationsTypes = [
  'SMTP_EMAIL_ACCOUNT',
  'IMAP_EMAIL_ACCOUNT',
  'TWITTER',
  'TWITTER_OAUTH_1A',
  'WEBHOOK',
  'MYSQL',
  'POSTGRESQL',
  'VIKA',
  'AIRTABLE',
  'AITABLE',
  'APITABLE',
  'DING_TALK',
  'TELEGRAM',
  'LINKEDIN',
  'FEI_SHU',
  'WE_COM',
  'SLACK',
  'AWS_OCR',
  'OPENAI',
  'DEEPSEEK',
  'MAKE_COM',
  'ZAPIER',
  'ZOOM',
  'ALICLOUD_TONGYI',
  'CLAUDE_AI', // Claude AI
  'TENCENT_HUNYUAN',
  'GOOGLE_AI', // Google AI
  'BYTEDANCE_DOUBAO',
  // Amazon Bedrock AI
  'AMAZON_BEDROCK',
  'AZURE_AI', // Azure AI
] as const;

export const SpaceIntegrationTypeSchema = z.enum(SpaceIntegrationsTypes);
export type SpaceIntegrationType = z.infer<typeof SpaceIntegrationTypeSchema>;

export const IntegrationTypes = [...UserIntegrationTypes, ...SpaceIntegrationsTypes] as const;
export const IntegrationTypeSchema = z.union([UserIntegrationTypeSchema, SpaceIntegrationTypeSchema]);
export type IntegrationType = z.infer<typeof IntegrationTypeSchema>;

export const IntegrationCategories = ['API', 'AUTH', 'AI_TEXT_MODEL'] as const;
export const IntegrationCategorySchema = z.enum(IntegrationCategories);

export type IntegrationCategory = z.infer<typeof IntegrationCategorySchema>;

/**
 * Integration: Third-party platforms that connect to our system
 * Zapier, Make.com, Integromat, IFTTT,......
 */
const BaseIntegrationSchema = z.object({
  type: IntegrationTypeSchema,
  name: iStringSchema,
  description: iStringSchema.optional(),
});
export const BaseUserIntegrationSchema = BaseIntegrationSchema.extend({
  type: UserIntegrationTypeSchema,
  name: iStringSchema,
  description: iStringSchema.optional(),
});
export const BaseSpaceIntegrationSchema = BaseIntegrationSchema.extend({
  type: SpaceIntegrationTypeSchema,
});
