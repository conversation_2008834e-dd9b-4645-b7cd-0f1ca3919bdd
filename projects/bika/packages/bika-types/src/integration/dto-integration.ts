import { PaginationSchema } from 'basenext/pagination';
import { z } from 'zod';
import { SpaceIntegrationTypeSchema } from './bo-base';
import { SpaceIntegrationSchema } from './bo-integrations';

const BaseSchema = z.object({
  spaceId: z.string(),
});

const IntegrationListBaseSchema = z.object({
  type: SpaceIntegrationTypeSchema.optional(),
  sort: z
    .string()
    .refine((value) => ['name', 'createdAt'].includes(value), { message: 'Invalid sort field' })
    .default('createdAt'),
  direction: z
    .string()
    .optional()
    .transform((value) => (value === 'asc' ? value : 'desc')),
});

export const IntegrationListDTOSchema = BaseSchema.merge(IntegrationListBaseSchema).merge(PaginationSchema);

export type IntegrationListDTOZodInput = z.input<typeof IntegrationListDTOSchema>;
export type IntegrationListDTOZodOutput = z.output<typeof IntegrationListDTOSchema>;
export type IntegrationListDTOWithoutSpaceID = Omit<IntegrationListDTOZodInput, 'spaceId'>;

export const IntegrationInfoDTOSchema = BaseSchema.extend({
  id: z.string(),
});

export type IntegrationInfoDTO = z.infer<typeof IntegrationInfoDTOSchema>;

export const IntegrationCreateDTOSchema = BaseSchema.extend({
  data: SpaceIntegrationSchema,
});

export type IntegrationCreateDTO = z.infer<typeof IntegrationCreateDTOSchema>;

export const IntegrationUpdateDTOSchema = BaseSchema.extend({
  id: z.string(),
  data: SpaceIntegrationSchema,
});

export type IntegrationUpdateDTO = z.infer<typeof IntegrationUpdateDTOSchema>;

export const IntegrationDeleteDTOSchema = BaseSchema.extend({
  id: z.string(),
});

export type IntegrationDeleteDTO = z.infer<typeof IntegrationDeleteDTOSchema>;
