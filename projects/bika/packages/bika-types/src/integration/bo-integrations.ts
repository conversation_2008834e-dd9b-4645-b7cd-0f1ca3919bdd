import { iStringParse, Locale } from 'basenext/i18n';
import { z } from 'zod';
import {
  AmazonBedrockAIProviderIntegrationSchema,
  AzureAIProviderIntegrationSchema,
  DeepSeekIntegrationSchema,
  OpenAIIntegrationSchema,
} from './bo-ai-integrations';
import {
  BaseSpaceIntegrationSchema,
  BaseUserIntegrationSchema,
  UserIntegrationTypeSchema,
  SpaceIntegrationTypeSchema,
  IntegrationType,
} from './bo-base';
import { IMAPInputSchema, SMTPInputSchema } from '../shared/input';

export const IntegrationRelationTypeSchema = z.union([z.literal('SPACE'), z.literal('USER')]);
export type IntegrationRelationType = z.infer<typeof IntegrationRelationTypeSchema>;

// ====== Webhook integration ======
export const WebhookIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.WEBHOOK),
  url: z.string(),
});
export type WebhookIntegration = z.infer<typeof WebhookIntegrationSchema>;

// ====== SMTP Email integration ======
export const SMTPEmailIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.SMTP_EMAIL_ACCOUNT),
}).and(SMTPInputSchema);
export type SMTPEmailIntegration = z.infer<typeof SMTPEmailIntegrationSchema>;

export const IMAPEmailIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.IMAP_EMAIL_ACCOUNT),
}).and(IMAPInputSchema);
export type IMAPEmailIntegration = z.infer<typeof IMAPEmailIntegrationSchema>;

// ====== MySQL integration ======
export const MySQLIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.MYSQL),
  // property: MySQLPropertySchema,
  host: z.string(),
  port: z.number(),
  user: z.string(),
  password: z.string(),
  database: z.string(),
});
export type MySQLIntegration = z.infer<typeof MySQLIntegrationSchema>;

// ====== PostgreSQL integration ======
export const PostgreSQLIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.POSTGRESQL),
  host: z.string(),
  port: z.number(),
  user: z.string(),
  password: z.string(),
  database: z.string(),
});
export type PostgreSQLIntegration = z.infer<typeof PostgreSQLIntegrationSchema>;

// ====== Vika DataSource integration ======

export const VikaDataSourceIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.VIKA),
  token: z.string(),
});
export type VikaDataSourceIntegration = z.infer<typeof VikaDataSourceIntegrationSchema>;

// ====== Hardware Device integration ======
export const HardwareDeviceIntegrationSchema = BaseUserIntegrationSchema.extend({
  type: z.literal(UserIntegrationTypeSchema.enum.HARDWARE_DEVICE),
  brand: z.string().optional(),
  deviceId: z.string().optional(),
  deviceName: z.string().optional(),
  deviceType: z.string().optional(),
  osName: z.string().optional(),
  osVersion: z.string().optional(),
  pushToken: z.string().optional(),
});
export type HardwareDeviceIntegration = z.infer<typeof HardwareDeviceIntegrationSchema>;

// ====== Wechat integration ======
export const WeChatIntegrationSchema = BaseUserIntegrationSchema.extend({
  type: z.literal(UserIntegrationTypeSchema.enum.WECHAT),
});
// export const WeChatIntegrationSchema = WeChatTypeSchema.merge(WeChatPropertySchema);
export type WeChatIntegration = z.infer<typeof WeChatIntegrationSchema>;

// ====== Google integration ======
export const GoogleIntegrationSchema = BaseUserIntegrationSchema.extend({
  type: z.literal(UserIntegrationTypeSchema.enum.GOOGLE),
});
export type GoogleIntegration = z.infer<typeof GoogleIntegrationSchema>;

// ====== GitHub integration ======
export const GitHubIntegrationSchema = BaseUserIntegrationSchema.extend({
  type: z.literal(UserIntegrationTypeSchema.enum.GITHUB),
});
export type GitHubIntegration = z.infer<typeof GitHubIntegrationSchema>;

// ====== dingding integration ======
export const DingDingIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.DING_TALK),
  webHookUrl: z.string(),
});
export type DingDingIntegration = z.infer<typeof DingDingIntegrationSchema>;

// ====== Siri integration ======
export const SiriIntegrationSchema = BaseUserIntegrationSchema.extend({
  type: z.literal(UserIntegrationTypeSchema.enum.SIRI),
  accessKey: z.string(),
});
export type SiriIntegration = z.infer<typeof SiriIntegrationSchema>;

// ====== Twitter integration ======

export const TwitterIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.TWITTER),
  clientId: z.string(),
  clientSecret: z.string(),
});
export type TwitterIntegration = z.infer<typeof TwitterIntegrationSchema>;

// ====== Twitter OAuth 1.0a User Context ======

export const TwitterOAuth1aIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.TWITTER_OAUTH_1A),
  apiKey: z.string(),
  apiSecret: z.string(),
  accessToken: z.string(),
  accessTokenSecret: z.string(),
});
export type TwitterOAuth1aIntegration = z.infer<typeof TwitterOAuth1aIntegrationSchema>;

// ====== Telegram integration ======

export const TelegramIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.TELEGRAM),
  token: z.string(),
});
export type TelegramIntegration = z.infer<typeof TelegramIntegrationSchema>;

export const FeiShuIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.FEI_SHU),
  webHookUrl: z.string(),
});
export type FeiShuIntegration = z.infer<typeof FeiShuIntegrationSchema>;

// ====== Slack integration ======

export const SlackIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.SLACK),
  webHookUrl: z.string(),
});
export type SlackIntegration = z.infer<typeof SlackIntegrationSchema>;

// ====== WeCom integration ======

export const WeComIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.WE_COM),
  webHookUrl: z.string(),
});
export type WeComIntegration = z.infer<typeof WeComIntegrationSchema>;

export type WebHookUrlIntegration = DingDingIntegration | FeiShuIntegration | SlackIntegration | WeComIntegration;

export const LinkedInIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.LINKEDIN),
  token: z.string(),
});
export type LinkedInIntegration = z.infer<typeof LinkedInIntegrationSchema>;

export const AwsOcrIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.AWS_OCR),
  token: z.string(),
});
export type AwsOcrIntegration = z.infer<typeof AwsOcrIntegrationSchema>;

export const AliyunTongyiIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.ALICLOUD_TONGYI),
  apiKey: z.string(),
});
export type AliyunTongyiIntegration = z.infer<typeof AliyunTongyiIntegrationSchema>;

/**
 * Claude AI Integration
 */
export const ClaudeAIIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.CLAUDE_AI),
  apiKey: z.string(),
});
export type ClaudeAIIntegration = z.infer<typeof ClaudeAIIntegrationSchema>;

/**
 * Google AI Integration
 */
export const GoogleAIIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.GOOGLE_AI),
  apiKey: z.string(),
});
export type GoogleAIIntegration = z.infer<typeof GoogleAIIntegrationSchema>;

export const TencentHunyuanIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.TENCENT_HUNYUAN),
  apiKey: z.string(),
});
export type TencentHunyuanIntegration = z.infer<typeof TencentHunyuanIntegrationSchema>;

export const ByteDouBaoIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.BYTEDANCE_DOUBAO),
  apiKey: z.string(),
});
export type ByteDouBaoIntegration = z.infer<typeof ByteDouBaoIntegrationSchema>;

export const ZapierIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.ZAPIER),
  token: z.string(),
});
export type ZapierIntegration = z.infer<typeof ZapierIntegrationSchema>;

export const MakeComIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.MAKE_COM),
  token: z.string(),
});
export type MakeComIntegration = z.infer<typeof MakeComIntegrationSchema>;

export const ZoomIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.ZOOM),
  token: z.string(),
});
export type ZoomIntegration = z.infer<typeof ZoomIntegrationSchema>;

export const AirtableIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.AIRTABLE),
  token: z.string(),
});
export type AirtableIntegration = z.infer<typeof AirtableIntegrationSchema>;

export const AitableIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.AITABLE),
  token: z.string(),
});
export type AitableIntegration = z.infer<typeof AitableIntegrationSchema>;

export const ApitableIntegrationSchema = BaseSpaceIntegrationSchema.extend({
  type: z.literal(SpaceIntegrationTypeSchema.enum.APITABLE),
  token: z.string(),
});
export type ApitableIntegration = z.infer<typeof ApitableIntegrationSchema>;

export const SpaceIntegrationSchema = z.union([
  PostgreSQLIntegrationSchema,
  MySQLIntegrationSchema,
  VikaDataSourceIntegrationSchema,
  WebhookIntegrationSchema,
  SMTPEmailIntegrationSchema,
  IMAPEmailIntegrationSchema,
  TwitterIntegrationSchema,
  TwitterOAuth1aIntegrationSchema,
  DingDingIntegrationSchema,
  TelegramIntegrationSchema,
  FeiShuIntegrationSchema,
  WeComIntegrationSchema,
  SlackIntegrationSchema,
  LinkedInIntegrationSchema,
  AwsOcrIntegrationSchema,
  OpenAIIntegrationSchema,
  DeepSeekIntegrationSchema,
  AzureAIProviderIntegrationSchema,
  AmazonBedrockAIProviderIntegrationSchema,
  MakeComIntegrationSchema,
  ZapierIntegrationSchema,
  ZoomIntegrationSchema,
  AirtableIntegrationSchema,
  AitableIntegrationSchema,
  ApitableIntegrationSchema,
  AliyunTongyiIntegrationSchema,
  ClaudeAIIntegrationSchema,
  TencentHunyuanIntegrationSchema,
  GoogleAIIntegrationSchema,
  ByteDouBaoIntegrationSchema,
]);
export type SpaceIntegration = z.infer<typeof SpaceIntegrationSchema>;

export const UserIntegrationSchema = z.union([
  HardwareDeviceIntegrationSchema,
  SiriIntegrationSchema,
  WeChatIntegrationSchema,
  GoogleIntegrationSchema,
  GitHubIntegrationSchema,
]);
export type UserIntegration = z.infer<typeof UserIntegrationSchema>;

export const IntegrationSchema = z.union([SpaceIntegrationSchema, UserIntegrationSchema]);
export type Integration = z.infer<typeof IntegrationSchema>;

export const defaultIntegrationBO = (type: IntegrationType, locale: Locale): Integration | null => {
  switch (type) {
    // Space integration
    case 'POSTGRESQL': {
      return {
        type: 'POSTGRESQL',
        name: iStringParse(
          {
            en: 'PostgreSQL integration',
            'zh-CN': 'Postgresql 数据源集成',
            'zh-TW': 'Postgresql 數據源集成',
            ja: 'Postgresql データソース統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'Postgresql integration',
            'zh-CN': 'Postgresql 数据源集成',
            'zh-TW': 'Postgresql 數據源集成',
            ja: 'Postgresql データソース統合',
          },
          locale,
        ),
        host: '',
        port: 5432,
        user: '',
        password: '',
        database: '',
      };
    }
    case 'AMAZON_BEDROCK': {
      return {
        type: 'AMAZON_BEDROCK',
        name: iStringParse(
          {
            en: 'Amazon Bedrock integration',
            'zh-CN': 'Amazon Bedrock 集成',
            'zh-TW': 'Amazon Bedrock 集成',
            ja: 'Amazon Bedrock 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'Amazon Bedrock integration',
            'zh-CN': 'Amazon Bedrock 集成',
            'zh-TW': 'Amazon Bedrock 集成',
            ja: 'Amazon Bedrock 統合',
          },
          locale,
        ),
        apiKey: '',
      };
    }
    case 'AZURE_AI': {
      return {
        type: 'AZURE_AI',
        name: iStringParse(
          {
            en: 'Azure AI integration',
            'zh-CN': 'Azure AI 集成',
            'zh-TW': 'Azure AI 集成',
            ja: 'Azure AI 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'Azure AI integration',
            'zh-CN': 'Azure AI 集成',
            'zh-TW': 'Azure AI 集成',
            ja: 'Azure AI 統合',
          },
          locale,
        ),
        apiKey: '',
      };
    }
    case 'MYSQL': {
      return {
        type: 'MYSQL',
        name: iStringParse(
          {
            en: 'MySQL integration',
            'zh-CN': 'MySQL 数据源集成',
            'zh-TW': 'MySQL 數據源集成',
            ja: 'MySQL データソース統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'MySQL integration',
            'zh-CN': 'MySQL 数据源集成',
            'zh-TW': 'MySQL 數據源集成',
            ja: 'MySQL データソース統合',
          },
          locale,
        ),
        host: '',
        port: 3306,
        user: '',
        password: '',
        database: '',
      };
    }
    case 'VIKA': {
      return {
        type: 'VIKA',
        name: iStringParse(
          {
            en: 'vika integration',
            'zh-CN': 'vika 数据源集成',
            'zh-TW': 'vika 數據源集成',
            ja: 'vika データソース統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'vika integration',
            'zh-CN': 'vika 数据源集成',
            'zh-TW': 'vika 數據源集成',
            ja: 'vika データソース統合',
          },
          locale,
        ),
        token: '',
      };
    }
    case 'WEBHOOK': {
      return {
        type: 'WEBHOOK',
        name: iStringParse(
          {
            en: 'WebHook integration',
            'zh-CN': 'WebHook 集成',
            'zh-TW': 'WebHook 集成',
            ja: 'WebHook 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'WebHook integration',
            'zh-CN': 'WebHook 集成',
            'zh-TW': 'WebHook 集成',
            ja: 'WebHook 統合',
          },
          locale,
        ),
        url: '',
      };
    }
    case 'SMTP_EMAIL_ACCOUNT': {
      return {
        type: 'SMTP_EMAIL_ACCOUNT',
        name: iStringParse(
          {
            en: 'SMTP Email integration',
            'zh-CN': 'SMTP邮箱',
            'zh-TW': 'SMTP郵箱',
            ja: 'SMTP メール',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'SMTP Email integration',
            'zh-CN': 'SMTP邮箱',
            'zh-TW': 'SMTP郵箱',
            ja: 'SMTP メール',
          },
          locale,
        ),
        host: '',
        port: 465,
        username: '',
        password: '',
      };
    }
    case 'TWITTER': {
      return {
        type: 'TWITTER',
        name: iStringParse(
          {
            en: 'Twitter integration',
            'zh-CN': 'Twitter 集成',
            'zh-TW': 'Twitter 集成',
            ja: 'Twitter 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'Twitter integration',
            'zh-CN': 'Twitter 集成',
            'zh-TW': 'Twitter 集成',
            ja: 'Twitter 統合',
          },
          locale,
        ),
        clientId: '',
        clientSecret: '',
      };
    }
    case 'TWITTER_OAUTH_1A': {
      return {
        type: 'TWITTER_OAUTH_1A',
        name: iStringParse(
          {
            en: 'Twitter OAuth 1.0a integration',
            'zh-CN': 'Twitter OAuth 1.0a 集成',
            'zh-TW': 'Twitter OAuth 1.0a 集成',
            ja: 'Twitter OAuth 1.0a 統合',
          },
          locale,
        ),
        apiKey: '',
        apiSecret: '',
        accessToken: '',
        accessTokenSecret: '',
      };
    }
    case 'DING_TALK': {
      return {
        type: 'DING_TALK',
        name: iStringParse(
          {
            en: 'DingTalk integration',
            'zh-CN': '钉钉集成',
            'zh-TW': '釘釘集成',
            ja: 'DingTalk 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'DingTalk integration',
            'zh-CN': '钉钉集成',
            'zh-TW': '釘釘集成',
            ja: 'DingTalk 統合',
          },
          locale,
        ),
        webHookUrl: '',
      };
    }
    case 'TELEGRAM': {
      return {
        type: 'TELEGRAM',
        name: iStringParse(
          {
            en: 'Telegram integration',
            'zh-CN': 'Telegram 集成',
            'zh-TW': 'Telegram 集成',
            ja: 'Telegram 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'Telegram integration',
            'zh-CN': 'Telegram 集成',
            'zh-TW': 'Telegram 集成',
            ja: 'Telegram 統合',
          },
          locale,
        ),
        token: '',
      };
    }
    case 'FEI_SHU': {
      return {
        type: 'FEI_SHU',
        name: iStringParse(
          {
            en: 'FeiShu integration',
            'zh-CN': '飞书集成',
            'zh-TW': '飛書集成',
            ja: 'FeiShu 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'FeiShu integration',
            'zh-CN': '飞书集成',
            'zh-TW': '飛書集成',
            ja: 'FeiShu 統合',
          },
          locale,
        ),
        webHookUrl: '',
      };
    }
    case 'WE_COM': {
      return {
        type: 'WE_COM',
        name: iStringParse(
          {
            en: 'WeCom integration',
            'zh-CN': '企业微信集成',
            'zh-TW': '企業微信集成',
            ja: 'WeCom 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'WeCom integration',
            'zh-CN': '企业微信集成',
            'zh-TW': '企業微信集成',
            ja: 'WeCom 統合',
          },
          locale,
        ),
        webHookUrl: '',
      };
    }
    case 'SLACK': {
      return {
        type: 'SLACK',
        name: iStringParse(
          {
            en: 'Slack integration',
            'zh-CN': 'Slack 集成',
            'zh-TW': 'Slack 集成',
            ja: 'Slack 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'Slack integration',
            'zh-CN': 'Slack 集成',
            'zh-TW': 'Slack 集成',
            ja: 'Slack 統合',
          },
          locale,
        ),
        webHookUrl: '',
      };
    }
    case 'LINKEDIN': {
      return {
        type: 'LINKEDIN',
        name: iStringParse(
          {
            en: 'LinkedIn integration',
            'zh-CN': 'LinkedIn 集成',
            'zh-TW': 'LinkedIn 集成',
            ja: 'LinkedIn 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'LinkedIn integration',
            'zh-CN': 'LinkedIn 集成',
            'zh-TW': 'LinkedIn 集成',
            ja: 'LinkedIn 統合',
          },
          locale,
        ),
        token: '',
      };
    }
    case 'AWS_OCR': {
      return {
        type: 'AWS_OCR',
        name: iStringParse(
          {
            en: 'AWS Textract integration',
            'zh-CN': 'AWS Textract 集成',
            'zh-TW': 'AWS Textract 集成',
            ja: 'AWS Textract 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'AWS Textract integration',
            'zh-CN': 'AWS Textract 集成',
            'zh-TW': 'AWS Textract 集成',
            ja: 'AWS Textract 統合',
          },
          locale,
        ),
        token: '',
      };
    }
    case 'DEEPSEEK': {
      return {
        type: 'DEEPSEEK',
        name: iStringParse(
          {
            en: 'DeepSeek integration',
            'zh-CN': 'DeepSeek 集成',
            'zh-TW': 'DeepSeek 集成',
            ja: 'DeepSeek 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'OpenAI integration',
            'zh-CN': 'OpenAI 集成',
            'zh-TW': 'OpenAI 集成',
            ja: 'OpenAI 統合',
          },
          locale,
        ),
        apiKey: '',
      };
    }
    case 'OPENAI': {
      return {
        type: 'OPENAI',
        name: iStringParse(
          {
            en: 'OpenAI integration',
            'zh-CN': 'OpenAI 集成',
            'zh-TW': 'OpenAI 集成',
            ja: 'OpenAI 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'OpenAI integration',
            'zh-CN': 'OpenAI 集成',
            'zh-TW': 'OpenAI 集成',
            ja: 'OpenAI 統合',
          },
          locale,
        ),
        apiKey: '',
      };
    }
    case 'MAKE_COM': {
      return {
        type: 'MAKE_COM',
        name: iStringParse(
          {
            en: 'Make.com integration',
            'zh-CN': 'Make.com 集成',
            'zh-TW': 'Make.com 集成',
            ja: 'Make.com 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'Make.com integration',
            'zh-CN': 'Make.com 集成',
            'zh-TW': 'Make.com 集成',
            ja: 'Make.com 統合',
          },
          locale,
        ),
        token: '',
      };
    }
    case 'ZAPIER': {
      return {
        type: 'ZAPIER',
        name: iStringParse(
          {
            en: 'Zapier integration',
            'zh-CN': 'Zapier 集成',
            'zh-TW': 'Zapier 集成',
            ja: 'Zapier 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'Zapier integration',
            'zh-CN': 'Zapier 集成',
            'zh-TW': 'Zapier 集成',
            ja: 'Zapier 統合',
          },
          locale,
        ),
        token: '',
      };
    }

    // User integration
    // TODO: 调整 User Integration Schema 中的 name 和 description
    case 'HARDWARE_DEVICE': {
      return {
        type: 'HARDWARE_DEVICE',
        name: iStringParse(
          {
            en: 'Hardware Device integration',
            'zh-CN': '硬件设备绑定',
            'zh-TW': '硬件设备绑定',
            ja: 'Hardware Device 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'Hardware Device integration',
            'zh-CN': '硬件设备集成绑定',
            'zh-TW': '硬件设备集成绑定',
            ja: 'Hardware Device 統合',
          },
          locale,
        ),
      };
    }
    case 'SIRI': {
      return {
        type: 'SIRI',
        name: iStringParse(
          {
            en: 'Siri integration',
            'zh-CN': 'Siri 集成',
            'zh-TW': 'Siri 集成',
            ja: 'Siri 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'Siri integration',
            'zh-CN': 'Siri 集成',
            'zh-TW': 'Siri 集成',
            ja: 'Siri 統合',
          },
          locale,
        ),
        accessKey: '',
      };
    }
    case 'WECHAT': {
      return {
        type: 'WECHAT',
        name: iStringParse(
          {
            en: 'WeChat integration',
            'zh-CN': 'wechat 集成',
            'zh-TW': 'wechat 集成',
            ja: 'WeChat 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'WeChat integration',
            'zh-CN': 'Zwechat 集成',
            'zh-TW': 'wechat 集成',
            ja: 'WeChat 統合',
          },
          locale,
        ),
      };
    }
    case 'ZOOM': {
      return {
        type: 'ZOOM',
        name: iStringParse(
          {
            en: 'Zoom integration',
            'zh-CN': 'Zoom 集成',
            'zh-TW': 'Zoom 集成',
            ja: 'Zoom 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'Zoom integration',
            'zh-CN': 'Zoom 集成',
            'zh-TW': 'Zoom 集成',
            ja: 'Zoom 統合',
          },
          locale,
        ),
        token: '',
      };
    }
    case 'AIRTABLE': {
      return {
        type: 'AIRTABLE',
        name: iStringParse(
          {
            en: 'Airtable integration',
            'zh-CN': 'Airtable 集成',
            'zh-TW': 'Airtable 集成',
            ja: 'Airtable 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'Airtable integration',
            'zh-CN': 'Airtable 集成',
            'zh-TW': 'Airtable 集成',
            ja: 'Airtable 統合',
          },
          locale,
        ),
        token: '',
      };
    }
    case 'AITABLE': {
      return {
        type: 'AITABLE',
        name: iStringParse(
          {
            en: 'Aitable integration',
            'zh-CN': 'Aitable 集成',
            'zh-TW': 'Aitable 集成',
            ja: 'Aitable 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'Aitable integration',
            'zh-CN': 'Aitable 集成',
            'zh-TW': 'Aitable 集成',
            ja: 'Aitable 統合',
          },
          locale,
        ),
        token: '',
      };
    }
    case 'APITABLE': {
      return {
        type: 'APITABLE',
        name: iStringParse(
          {
            en: 'Apitable integration',
            'zh-CN': 'Apitable 集成',
            'zh-TW': 'Apitable 集成',
            ja: 'Apitable 統合',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'Apitable integration',
            'zh-CN': 'Apitable 集成',
            'zh-TW': 'Apitable 集成',
            ja: 'Apitable 統合',
          },
          locale,
        ),
        token: '',
      };
    }
    case 'IMAP_EMAIL_ACCOUNT': {
      return {
        type: 'IMAP_EMAIL_ACCOUNT',
        name: iStringParse(
          {
            en: 'IMAP Email integration',
            'zh-CN': 'IMAP 邮箱',
            'zh-TW': 'IMAP 郵箱',
            ja: 'IMAP メール',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'By configuring an IMAP email account, users can integrate the ability to receive emails into the system. With automation, specific actions can be triggered when particular emails are received, such as automatically creating tasks, archiving emails, or triggering alerts. Suitable for scenarios where information needs to be extracted from emails and responded to.',
            'zh-CN':
              '通过配置IMAP邮箱账户，用户可以将接收邮件的功能集成到系统中。通过自动化，当接收到特定邮件时，可以触发特定的操作，例如自动创建任务、归档邮件或触发警报。适用于需要从邮件中提取信息并进行响应的场景。',
            'zh-TW':
              '通過配置IMAP郵箱賬戶，用戶可以將接收郵件的功能集成到系統中。通過自動化，當接收到特定郵件時，可以觸發特定的操作，例如自動創建任務、歸檔郵件或觸發警報。適用於需要從郵件中提取信息並進行響應的場景。',
            ja: 'IMAPメールアカウントを設定することで、ユーザーはシステムにメール受信機能を統合できます。自動化により、特定のメールを受信した際に特定のアクションをトリガーすることができます。例えば、タスクの自動作成、メールのアーカイブ、アラートのトリガーなどです。メールから情報を抽出し、対応する必要があるシナリオに適しています。',
          },
          locale,
        ),
        host: '',
        port: 993,
        username: '',
        password: '',
      };
    }
    case 'ALICLOUD_TONGYI': {
      return {
        type: 'ALICLOUD_TONGYI',
        name: iStringParse(
          {
            en: 'Tongyi Qianwen (Qwen)',
            'zh-CN': '通义千问 (Qwen)',
            'zh-TW': '通義千問 (Qwen)',
            ja: '通義千問 (Qwen)',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'Top-performance foundation models from Alibaba Cloud',
            'zh-CN': '阿里云通义千问，AI大模型，提供了丰富的自然语言处理能力，支持多种场景的应用。',
            'zh-TW': '阿里雲通義千問，AI大模型，提供了豐富的自然語言處理能力，支持多種場景的應用。',
            ja: 'アリババクラウドの高性能ファウンデーションモデル',
          },
          locale,
        ),
        apiKey: '',
      };
    }

    case 'CLAUDE_AI': {
      return {
        type: 'CLAUDE_AI',
        name: iStringParse(
          {
            en: 'Claude.ai',
            'zh-CN': 'Claude.ai',
            'zh-TW': 'Claude.ai',
            ja: 'Claude.ai',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'The Claude model family are large language models (including Haiku, Sonnet, Opus) developed by Anthropic, providing intelligent conversations, content generation, and data analysis services. It can understand complex queries and provide accurate answers, helping users improve work efficiency and creativity.',
            'zh-CN':
              'Claude 系列模型是由 Anthropic 公司开发的大语言模型（包含 Haiku、Sonnet、Opus），能够提供智能对话、内容生成和数据分析等服务。它能够理解复杂的查询，并提供精确的回答，帮助用户提高工作效率和创造力。',
            'zh-TW':
              'Claude 系列模型是由 Anthropic 開發的大語言模型（包含 Haiku、Sonnet、Opus），能夠提供智能對話、內容生成和數據分析等服務。它能夠理解複雜的查詢，並提供精確的回答，幫助用戶提高工作效率和創造力。',
            ja: 'Claude 系列模型は Anthropic 公司が開発した大言語モデル（Haiku、Sonnet、Opus を含む）で、知的な会話、コンテンツ生成、データ分析を提供します。複雑なクエリを理解し、正確な回答を提供することで、ユーザーの作業効率と創造性を向上させます。',
          },
          locale,
        ),
        apiKey: '',
      };
    }
    case 'GOOGLE_AI': {
      return {
        type: 'GOOGLE_AI',
        name: iStringParse(
          {
            en: 'Google AI',
            'zh-CN': 'Google AI',
            'zh-TW': 'Google AI',
            ja: 'Google AI',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'Google AI is a series of large language models (including Gemini) developed by Google, capable of providing intelligent conversations, content generation, and data analysis services. It can understand complex queries and provide accurate answers, helping users improve work efficiency and creativity.',
            'zh-CN':
              'Google AI 是由 Google 公司开发的一系列大语言模型（包含 Gemini 等），能够提供智能对话、内容生成和数据分析等服务。它能够理解复杂的查询，并提供精确的回答，帮助用户提高工作效率和创造力。',
            'zh-TW':
              'Google AI 是由 Google 公司開發的一系列大語言模型（包含 Gemini 等），能夠提供智能對話、內容生成和數據分析等服務。它能夠理解複雜的查詢，並提供精確的回答，幫助用戶提高工作效率和創造力。',
            ja: 'Google AI は Google 社が開発した一連の大規模言語モデル（Gemini を含む）で、インテリジェントな会話、コンテンツ生成、データ分析などのサービスを提供します。複雑なクエリを理解し、正確な回答を提供することで、ユーザーの作業効率と創造性を向上させます。',
          },
          locale,
        ),
        apiKey: '',
      };
    }
    case 'TENCENT_HUNYUAN': {
      return {
        type: 'TENCENT_HUNYUAN',
        name: iStringParse(
          {
            en: 'Tencent Hunyuan',
            'zh-CN': '腾讯混元',
            'zh-TW': '騰訊混元',
            ja: 'テンセント混元',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'Tencent Hunyuan is a large language model developed by Tencent, featuring powerful Chinese composition, logical reasoning in complex contexts, and reliable task execution.',
            'zh-CN':
              '腾讯混元大模型（Tencent Hunyuan）是由腾讯研发的大语言模型，具备强大的中文创作能力，复杂语境下的逻辑推理能力，以及可靠的任务执行能力。',
            'zh-TW':
              '騰訊混元大模型（Tencent Hunyuan）是由騰訊研發的大語言模型，具備強大的中文創作能力，複雜語境下的邏輯推理能力，以及可靠的任務執行能力。',
            ja: 'テンセント混元（Tencent Hunyuan）は、テンセントによって開発された大規模言語モデルであり、強力な中国語の作成能力、複雑な文脈における論理的推論能力、そして信頼できるタスク実行能力を備えています。',
          },
          locale,
        ),
        apiKey: '',
      };
    }
    case 'BYTEDANCE_DOUBAO': {
      return {
        type: 'BYTEDANCE_DOUBAO',
        name: iStringParse(
          {
            en: 'ByteDance Doubao',
            'zh-CN': '字节豆包',
            'zh-TW': '字节豆包',
            ja: 'バイト豆包',
          },
          locale,
        ),
        description: iStringParse(
          {
            en: 'ByteDance Doubao integration for intelligent conversations and content generation.',
            'zh-CN': '字节豆包集成，提供智能对话和内容生成服务。',
            'zh-TW': '字节豆包集成，提供智能对话和内容生成服务。',
            ja: 'バイト豆包の統合、インテリジェントな会話とコンテンツ生成を提供します。',
          },
          locale,
        ),
        apiKey: '',
      };
    }

    default:
      return null;
  }
};
