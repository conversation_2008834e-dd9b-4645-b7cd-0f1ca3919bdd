import { AvatarLogoSchema } from 'basenext/avatar';
import { z } from 'zod';

export const UserLinkTypeSchema = z.enum(['AUTH0', 'CLERK', 'GITHUB', 'GOOGLE', 'APPLE', 'WEIXIN', 'MICROSOFT']);
export type UserLinkType = z.infer<typeof UserLinkTypeSchema>;

export const UserBOSchema = z.object({
  id: z.string().optional(),
  username: z.string().optional(),
  name: z.string().optional(),
  email: z.string().optional(),
  phone: z.string().optional(),
  avatar: AvatarLogoSchema.optional(),
  hashedPassword: z.string().optional(),
  templateId: z.string().optional(),
  timezone: z.string().optional(),
});

export type UserBO = z.infer<typeof UserBOSchema>;

export const UserSettingsColorsSchema = z.union([
  z.literal('DEFAULT'),
  // custom colors
  z.object({
    sidebarBackgroundColor: z.string().optional(),
    contentBackgroundColor: z.string().optional(),
  }),
]);
