import { AvatarLogoSchema } from 'basenext/avatar';
import { LocaleSchema, Locale } from 'basenext/i18n';
import { BasePaginationInfoSchema } from 'basenext/pagination';
import { z } from 'zod';
import { UserSettingsColorsSchema } from './bo-user';
import { CoinTransactionReasonSchema } from '../store/bo';
import { MemberVOSchema } from '../unit/vo-member';
import { ThemeStyleSchema, ThemeModeSchema } from '../website/bo-theme';

const NotificationSettingSchema = z.object({
  email: z.boolean().optional().default(true),
  push: z.boolean().optional().default(true),
  SMS: z.boolean().optional().default(true),
});
export type INotificationSettings = z.infer<typeof NotificationSettingSchema>;
/**
 * User's personal settings
 */
export const UserSettingsVOSchema = z.object({
  // Dark or light mode? Default is dark
  themeMode: ThemeModeSchema.optional(),
  // Theme color, default is purple
  themeStyle: ThemeStyleSchema.optional(),

  // Advanced mode with full customization, rarely used
  customColors: UserSettingsColorsSchema.optional(),

  locale: LocaleSchema.optional(),
  notification: NotificationSettingSchema.optional(),

  // Whether the questionnaire has been completed
  questionaire: z.boolean().optional(),
});
export type UserSettingsVO = z.infer<typeof UserSettingsVOSchema>;

export const UserCoinsVOSchema = z.object({
  balance: z.number(),
  credit: z.number(),
  currency: z.number(),
});
export type UserCoinsVO = z.infer<typeof UserCoinsVOSchema>;

export const UserCoinTransactionVOSchema = z.object({
  amount: z.number(),
  description: z.string().optional(),
  type: z.string(),
  coinType: z.string(),
  reason: CoinTransactionReasonSchema,
  createdAt: z.string(),
  member: MemberVOSchema.optional(),
});
export type UserCoinTransactionVO = z.infer<typeof UserCoinTransactionVOSchema>;

export interface SessionVO {
  id: string;
  expiresAt: string; // ISO datetime string
  activeAt?: string; // ISO datetime string
  fresh: boolean;
  userId: string;
  ip?: string;
  hostname?: string;
  version?: string;
}
export const UserMetadataJPOSchema = z.object({
  // Referral code of the referrer
  referralCode: z.string().optional(),

  // Referrer's user ID, note: includes both User Referral and Space Member Invite
  referralUserId: z.string().optional(),

  // Whether the referrer has been rewarded (note: refers to others). Includes space member invites
  referralAwarded: z.boolean().optional(),

  // Has the mobile app installation reward been given? (personal)
  mobileAppInstallAward: z.boolean().optional(),

  // Whether the user is from China. Updated on each login based on domain origin. Used for tagging
  isChinaUser: z.boolean().optional(),

  isPremiumPlanNotified: z.boolean().optional(),

  // Node type first visit tracking - records which node types the user has visited
  nodeTypeFirstVisits: z.record(z.string(), z.boolean()).optional(),
});

export type UserMetadataJPO = z.infer<typeof UserMetadataJPOSchema>;

export const UserVOSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email().nullable(),
  phone: z.string().nullable().optional(),
  settings: UserSettingsVOSchema.optional(),
  timeZone: z.string().optional(),
  avatar: AvatarLogoSchema,
  metadata: UserMetadataJPOSchema.optional(),
  createdAt: z.string().optional(),
});
export type UserVO = z.infer<typeof UserVOSchema>;

export function genDefaultUserName(locale: Locale, name: string) {
  const shortName = name.slice(-5);
  switch (locale) {
    case 'en':
      return `Bika User ${shortName}`;
    case 'ja':
      return `Bikaユーザー ${shortName}`;
    case 'zh-CN':
      return `Bika用户 ${shortName}`;
    case 'zh-TW':
      return `Bika用戶 ${shortName}`;
    default:
      throw new Error(`Unsupported locale: ${locale}`);
  }
}

/**
 * Note: This request is shared between OpenAPI, tRPC, and HTTP REST API
 */
export interface ApiFetchRequestContext {
  req: Request;
  resHeaders?: Headers;
  session: SessionVO | null;
  locale: Locale;
}

export interface AuthVO {
  user: UserVO;
  session: SessionVO;
}

export const UserDeviceVOSchema = z.object({
  brand: z.string().optional(),
  deviceName: z.string().optional(),
  deviceType: z.string().optional(),
  osName: z.string().optional(),
  osVersion: z.string().optional(),
  pushToken: z.string().optional(),
});
export type UserDeviceVO = z.infer<typeof UserDeviceVOSchema>;

export const UserLicenseVOSchema = z.object({
  licenseKey: z.string(),
  expiredAt: z.string().datetime(),
  createdAt: z.string().datetime(),
});

export type UserLicenseVO = z.infer<typeof UserLicenseVOSchema>;

export const CreditTransactionPaginationVOSchema = BasePaginationInfoSchema.extend({
  data: UserCoinTransactionVOSchema.array(),
});

export type CreditTransactionPaginationVO = z.infer<typeof CreditTransactionPaginationVOSchema>;
