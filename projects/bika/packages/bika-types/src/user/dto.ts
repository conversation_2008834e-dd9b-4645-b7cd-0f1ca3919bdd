import { AvatarLogoSchema } from 'basenext/avatar';
import { LocaleSchema } from 'basenext/i18n';
import { z } from 'zod';
import { UserSettingsColorsSchema } from './bo-user';
import { ThemeStyleSchema, ThemeModeSchema } from '../website/bo-theme';

export const UpdateUserDTOSchema = z.object({
  avatar: AvatarLogoSchema.optional(),
  name: z.string().optional(),
  email: z.string().nullable().optional(),
  phone: z.string().nullable().optional(),
  themeMode: ThemeModeSchema.optional(),
  themeStyle: ThemeStyleSchema.optional(),
  customColors: UserSettingsColorsSchema.optional(),
  timeZone: z.string().optional(),
  lang: LocaleSchema.optional(),
});
export type UpdateUserDTO = z.infer<typeof UpdateUserDTOSchema>;

export const UpdateUserPasswordDTOSchema = z.object({
  oldPassword: z.string().optional(),
  password: z.string(),
  confirmPassword: z.string(),
});

export type UpdateUserPasswordDTO = z.infer<typeof UpdateUserPasswordDTOSchema>;
