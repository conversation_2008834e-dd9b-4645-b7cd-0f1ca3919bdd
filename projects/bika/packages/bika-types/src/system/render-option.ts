import { LocaleSchema } from 'basenext/i18n';
import { z } from 'zod';

export const RenderOptionSchema = z.object({
  locale: LocaleSchema.optional(),
  timeZone: z.string().optional(),
});

/**
 * Rendering options for VO (View Objects). Before returning to the client,
 * small processing is needed based on these options, such as returning only Chinese
 * for internationalization to reduce network transmission and user confusion
 */
export type RenderOption = z.infer<typeof RenderOptionSchema>;
