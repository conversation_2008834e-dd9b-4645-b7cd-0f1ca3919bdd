import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';

// all features types
export const FeatureTypes = [
  'node-resource',
  'automation-trigger',
  'automation-action',
  'database-field',
  'database-view',
  'dashboard-widget',
  'integration',
  'mission',
  'formula',
  'ai-wizard',

  'space',

  'ai-model',

  // TODO: to remove
  'ai-intent-ui',
  // expert
] as const;

export const FeatureTypeSchema = z.enum(FeatureTypes);
export type FeatureType = (typeof FeatureTypes)[number];

const FeatureAbilityConfigSchema = z.object({
  label: z.string(),
  iconPath: z.string(),
  display: z.enum(['SHOW', 'HIDDEN', 'COMING_SOON']),
  description: z.string(),
  // Other third-party links? Such as third-party integration official websites, other Action documentation, etc., will be displayed in help as related links
  links: z
    .array(
      z.object({
        url: z.string(),
        text: iStringSchema.optional(),
      }),
    )
    .optional(),
  screenshots: z
    .array(
      z.object({
        url: z.string(),
      }),
    )
    .optional(),
});

export type IFeatureAbilityConfig = z.infer<typeof FeatureAbilityConfigSchema>;

export const FeatureAbilityConfigDetailSchema = FeatureAbilityConfigSchema.extend({
  key: z.string(), // ability key
  featureType: z.enum(FeatureTypes),
});

/**
 * Feature information, used for search engine indexing, configuration (config), and help documentation display
 */
export type IFeatureAbilityConfigDetail = z.infer<typeof FeatureAbilityConfigDetailSchema>;

export const SpaceFeatureTypes = [
  'CREATE_MISSION',
  'CREATE_REPORT',

  'FLOW',
  'AI_WIZARD',
  'AI_LAUNCHER',
  'IMPORT',
  'RECORD_DETAIL',
  'RECORD_REQUEST',
  'REPORT_DETAIL',
  'DOC_DETAIL',
  'SPACE_SIDEBAR',
  'SPACE_SETTING_INFO',
  'SPACE_SETTING_MEMBER',
  'SPACE_SETTING_ROLE',
  // share
  'PERMISSION',
  'NOTIFICATION',
] as const;
export const SpaceFeatureTypesSchema = z.enum(SpaceFeatureTypes);
export type SpaceFeatureType = (typeof SpaceFeatureTypes)[number];
