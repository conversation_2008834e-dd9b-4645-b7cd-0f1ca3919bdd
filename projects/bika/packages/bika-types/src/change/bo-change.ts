import { AvatarLogoSchema, iStringSchema } from 'basenext';
import { z } from 'zod';
import { DatabaseFieldTypeSchema } from '../database/bo-field-type';
import { CellValueBaseSchema } from '../database/bo-record';
import { AttachmentCellVOSchema } from '../database/vo';

export const NodeResourceChangeKeyEnum = z.enum(['name', 'description', 'icon', 'resourceType', 'preNodeId'] as const);

export type NodeResourceChangeKey = z.infer<typeof NodeResourceChangeKeyEnum>;

export const ChangeLogTypeEnum = z.enum(['NODE_RESOURCE', 'DATABASE_RECORD'] as const);

export type ChangeLogType = z.infer<typeof ChangeLogTypeEnum>;
const NodeResourceChangeBOSchema = z.object({
  type: z.literal(ChangeLogTypeEnum.enum.NODE_RESOURCE),
  data: z.record(NodeResourceChangeKeyEnum, z.object({ previous: CellValueBaseSchema, current: CellValueBaseSchema })),
});
export type NodeResourceChangeBO = z.infer<typeof NodeResourceChangeBOSchema>;

export const DatabaseRecordChangeTypeEnum = ['UPDATE', 'CREATE', 'FIELD_TYPE_CHANGE'] as const;
// delete is in trash center, so dont record in change
export const DatabaseRecordChangeTypeEnumSchema = z.enum(DatabaseRecordChangeTypeEnum);

export type DatabaseRecordChangeType = z.infer<typeof DatabaseRecordChangeTypeEnumSchema>;

const DatabaseRecordFieldChangeBOSchema = z.object({
  id: z.string().optional(),
  name: iStringSchema,
  type: DatabaseFieldTypeSchema,
  // TODO: Need to optimize, there are issues with bo conversion, using any to prevent errors
  property: z.any().optional(),
});

const BaseDatabaseRecordChangeBOSchema = z.object({
  recordId: z.string(),
  type: z.literal(ChangeLogTypeEnum.enum.DATABASE_RECORD),
  changeType: DatabaseRecordChangeTypeEnumSchema,
  // fields: z.array(z.pick(DatabaseFieldSchema, ['id', 'name', 'type', 'property'])),
  fields: z.array(DatabaseRecordFieldChangeBOSchema),
  updatedBy: z
    .object({
      userId: z.string(),
      userName: z.string(),
      avatar: AvatarLogoSchema.optional(),
    })
    .optional(),
});

const RecordCellChangeSchema = z.union([
  CellValueBaseSchema,
  AttachmentCellVOSchema.array(),
  z.array(CellValueBaseSchema),
]);

export const DatabaseRecordUpdateChangeBOSchema = BaseDatabaseRecordChangeBOSchema.extend({
  changeType: z.literal(DatabaseRecordChangeTypeEnumSchema.enum.UPDATE),
  data: z.record(
    z.string(),
    z.object({
      previous: z.object({
        data: RecordCellChangeSchema.optional(), // recover from data
        values: RecordCellChangeSchema.optional(), // used to show directly, only saved link、member、lookup field values
      }),
      current: z.object({
        data: RecordCellChangeSchema,
        values: RecordCellChangeSchema.optional(),
      }),
    }),
  ),
});

export type DatabaseRecordUpdateChangeBO = z.infer<typeof DatabaseRecordUpdateChangeBOSchema>;

export const DatabaseFieldTypeChangeBOSchema = DatabaseRecordUpdateChangeBOSchema.extend({
  changeType: z.literal(DatabaseRecordChangeTypeEnumSchema.enum.FIELD_TYPE_CHANGE),
  previousFields: DatabaseRecordFieldChangeBOSchema.array().optional(),
});

export type DatabaseFieldTypeChangeBO = z.infer<typeof DatabaseFieldTypeChangeBOSchema>;

export const DatabaseRecordCreateChangeBOSchema = BaseDatabaseRecordChangeBOSchema.extend({
  changeType: z.literal(DatabaseRecordChangeTypeEnumSchema.enum.CREATE),
  // fieldName Redundant, use when the field is deleted
  // recover from data, values is used to show directly, only saved link、member、lookup field values
  data: z.record(z.string(), z.object({ data: RecordCellChangeSchema, values: RecordCellChangeSchema.optional() })),
});

export type DatabaseRecordCreateChangeBO = z.infer<typeof DatabaseRecordCreateChangeBOSchema>;

/**
 * What field was changed? For example, changing the value of a field
 */
export const DatabaseRecordChangeBOSchema = z.union([
  DatabaseRecordUpdateChangeBOSchema,
  DatabaseRecordCreateChangeBOSchema,
  DatabaseFieldTypeChangeBOSchema,
]);

export type DatabaseRecordChangeBO = z.infer<typeof DatabaseRecordChangeBOSchema>;

export const ChangeBOSchema = z.union([NodeResourceChangeBOSchema, DatabaseRecordChangeBOSchema]);

export type ChangeBO = z.infer<typeof ChangeBOSchema>;
// Storage structure, stored in OpenObserve
export const RecordChangeLogSchema = z.object({
  id: z.string(),
  // audit log type
  kind: z.literal('RECORD_CHANGE_LOG'),
  // space id
  spaceid: z.string(),
  // database id
  databaseid: z.string(),
  // record id
  recordid: z.string(),
  // change log
  data: z.string(), // DatabaseRecordChangeBOSchema, data type conversion exists, same key with different value types will cause errors, so store as json string
});

export type RecordChangeLog = z.infer<typeof RecordChangeLogSchema>;

export const ChangeLogSchema = z.discriminatedUnion('kind', [RecordChangeLogSchema]);

export type ChangeLog = z.infer<typeof ChangeLogSchema>;
