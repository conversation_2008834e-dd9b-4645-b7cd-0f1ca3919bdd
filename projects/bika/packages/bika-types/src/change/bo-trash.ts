import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import { ActionSchema } from '../automation/bo-actions';
import { NativeTriggerSchema } from '../automation/bo-triggers';
import { DatabaseRecordSchema } from '../database/bo-database';
import { NodeResourceSchema } from '../node/bo';

export const TrashTypeEnumSchema = z.enum(['NODE_RESOURCE', 'RECORD', 'TRIGGER', 'ACTION']);

export type TrashTypeEnum = z.infer<typeof TrashTypeEnumSchema>;

export const TrashBOBaseSchema = z.object({
  id: z.string().optional(),
  trashType: TrashTypeEnumSchema,
});

export const NodeResourceTrashBOSchema = TrashBOBaseSchema.extend({
  trashType: z.literal(TrashTypeEnumSchema.enum.NODE_RESOURCE),
  bo: z.object({
    resource: NodeResourceSchema,
    preNodeId: z.string().optional(),
    parentId: z.string().optional(),
  }),
});
export type NodeResourceTrashBO = z.infer<typeof NodeResourceTrashBOSchema>;

export const RecordTrashBOSchema = TrashBOBaseSchema.extend({
  trashType: z.literal(TrashTypeEnumSchema.enum.RECORD),
  bo: z.object({
    databaseId: z.string(),
    databaseName: iStringSchema,
    records: z.array(DatabaseRecordSchema),
  }),
});

export type RecordTrashBO = z.infer<typeof RecordTrashBOSchema>;

export const TriggerTrashBOSchema = TrashBOBaseSchema.extend({
  trashType: z.literal(TrashTypeEnumSchema.enum.TRIGGER),
  bo: z.object({
    trigger: NativeTriggerSchema,
    automationId: z.string(),
    preTriggerId: z.string().optional(),
  }),
});

export type TriggerTrashBO = z.infer<typeof TriggerTrashBOSchema>;

export const ActionTrashBOSchema = TrashBOBaseSchema.extend({
  trashType: z.literal(TrashTypeEnumSchema.enum.ACTION),
  bo: z.object({
    action: ActionSchema,
    automationId: z.string(),
    preActionId: z.string().optional(),
    parentActionId: z.string().optional(),
  }),
});

export type ActionTrashBO = z.infer<typeof ActionTrashBOSchema>;

export const TrashBOSchema = z.union([
  NodeResourceTrashBOSchema,
  RecordTrashBOSchema,
  TriggerTrashBOSchema,
  ActionTrashBOSchema,
]);

export type TrashBO = z.infer<typeof TrashBOSchema>;

// Storage structure, store to OpenObserve
export const SpaceTrashLogModelSchema = z.object({
  // audit log type
  kind: z.literal('SPACE_TRASH_LOG'),
  // user id
  trashid: z.string(),
  // space id
  spaceid: z.string(),

  // event format data
  data: z.string().optional(), // Pass string to open observe, parse with TrashBOSchema when used in business logic
});

export type SpaceTrashLogModel = z.infer<typeof SpaceTrashLogModelSchema>;
