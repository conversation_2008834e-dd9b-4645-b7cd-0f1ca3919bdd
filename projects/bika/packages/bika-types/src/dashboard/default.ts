import { Locale, iStringParse } from 'basenext/i18n';
import { DashboardSchema } from './bo-dashboard';
import { WidgetBO } from './bo-widgets';
import { DashboardCreateBOSchema, DashboardCreateBO } from './dto-dashboard';
import { WidgetVO } from './vo-dashboard';

/**
 *
 *
 * @param type
 * @returns
 */
export function defaultWidgetVO(type: WidgetBO['type']): WidgetVO {
  const configs: Record<WidgetBO['type'], WidgetVO> = {
    CHART: {
      id: '',
      width: '100%',
      type: 'CHART',
      name: 'Chart Widget',
      description: 'This widget displays charts based on data from a database.',
      datasource: {
        type: 'CUSTOM',
        xAxis: {
          type: 'category',
          data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            type: 'line',
            data: [120, 200, 150, 80, 70, 110],
          },
        ],
      },
    },
    NUMBER: {
      id: '',
      width: '100%',
      type: 'NUMBER',
      name: 'Number Widget',
      datasource: {
        metricsType: 'COUNT_RECORDS',
        type: 'DATABASE',
      },
      targetValue: 1,
      summaryDescription: 'Total',
    },
    TEXT: {
      id: '',
      width: '100%',
      type: 'TEXT',
      name: 'Title',
      datasource: {
        type: 'CUSTOM',
        text: {
          en: 'title name',
          'zh-CN': '标题',
          'zh-TW': '標題',
          ja: 'タイトル',
        },
      },
      description: 'This widget displays text content.',
    },
    PIVOT_TABLE: {
      id: '',
      width: '100%',
      type: 'PIVOT_TABLE',
      name: 'Pivot Table Widget',
      datasource: {
        type: 'DATABASE',
        fields: {
          rows: [],
          columns: [],
          values: [],
        },
      },
    },
    EMBED: {
      id: '',
      width: '100%',
      type: 'EMBED',
      name: 'Embed Widget',
      description: 'This widget embeds external content.',
      url: 'https://bika.ai/zh-CN',
    },
  };

  return configs[type];
}

export const defaultDashboardCreateBO = (locale: Locale) =>
  DashboardCreateBOSchema.parse({
    resourceType: 'DASHBOARD',
    name: iStringParse(
      {
        en: 'New Unnamed Dashbaord',
        'zh-CN': '新的未命名仪表盘',
        'zh-TW': '新的未命名儀表板',
        ja: '新しい未命名ダッシュボード',
      },
      locale,
    ),
    description: undefined,
  });

export function defaultDashboardBO(id: { id?: string; templateId?: string }, create: DashboardCreateBO) {
  return DashboardSchema.parse({
    id: id.id,
    templateId: id.templateId,
    widgets: [], // TODO: provide default widgets as examples for users
    ...create,
  });
}
