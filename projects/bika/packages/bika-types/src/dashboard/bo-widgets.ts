import { AvatarLogoSchema } from 'basenext/avatar';
import { iStringParse, iStringSchema, type Locale } from 'basenext/i18n';
import { z } from 'zod';
import {
  WidgetMetricsSchema,
  WidgetMetricsTypeSchema,
  ChartWidgetTypeSchema,
  DatasourceChartSchema,
  DatasourceDatabaseSchema,
  DatasourceMySQLSchema,
  DatasourcePostgreSQLSchema,
} from './bo-datasource';
import { DatabaseFieldSchema } from '../database/bo-field';
import { RecipientVOSchema } from '../system/recipient-vo';

export const WidgetTypes = [
  'CHART',
  'NUMBER', // statistical data table, summary number
  'TEXT',
  'PIVOT_TABLE',
  'EMBED', // embedded document
  // The following are not open for users to operate persistence on UI
  // To open them, drag them up, and the BOs below need to be pulled into the Schema
  'LIST',
  'ICONS', // icon display
  'BIKA', // Welcome & Announcement
  'PROGRESS_BAR',
  'AI', // AI Widget, gen by AI
] as const;

export const WidgetTypeSchema = z.enum(WidgetTypes);
export type WidgetType = z.infer<typeof WidgetTypeSchema>;

export const CustomWidgetTypes = ['ICONS', 'LIST', 'BIKA', 'PROGRESS_BAR', 'SUBSCRIBE_INFO'] as const;
export const CustomWidgetTypeSchema = z.enum(CustomWidgetTypes);
export type CustomWidgetType = z.infer<typeof CustomWidgetTypeSchema>;

export const WidgetLayoutSchema = z.object({
  x: z.number(),
  y: z.number(),
  w: z.number(),
  h: z.number(),
});
export type WidgetLayout = z.infer<typeof WidgetLayoutSchema>;

const BaseWidgetSchema = z.object({
  id: z.string().optional(),
  templateId: z.string().nullish(),
  type: WidgetTypeSchema,
  name: iStringSchema,
  description: iStringSchema.optional(),
  width: z.enum(['25%', '50%', '100%']).default('50%').optional(),
  layout: z
    .object({
      x: z.number(),
      y: z.number(),
      w: z.number(),
      h: z.number(),
    })
    .optional(),

  bottomButtonUrl: z.string().optional(),
  bottomButtonName: iStringSchema.optional(),
  topRightButtonUrl: z.string().optional(),
  topRightButtonName: iStringSchema.optional(),
});

export const WidgetDatasourceTypes = ['DATABASE', 'CUSTOM', 'MYSQL', 'POSTGRE_SQL'] as const;
export const WidgetDatasourceTypeSchema = z.enum(WidgetDatasourceTypes);
export type WidgetDatasourceType = z.infer<typeof WidgetDatasourceTypeSchema>;

export const ChartWidgetDatabaseDatasourceSchema = DatasourceDatabaseSchema.extend({
  type: z.literal(WidgetDatasourceTypeSchema.enum.DATABASE).default(WidgetDatasourceTypeSchema.enum.DATABASE),
  chartType: ChartWidgetTypeSchema.default('line'),
  metricsType: WidgetMetricsTypeSchema.default('COUNT_RECORDS'),
  metrics: WidgetMetricsSchema.optional(),
  dimension: z.string().optional(), // xAxis field
  dimensionTemplateId: z.string().optional(),
});

export type ChartWidgetDatabaseDatasource = z.infer<typeof ChartWidgetDatabaseDatasourceSchema>;

export const ChartWidgetDatasourceSchema = z.union([
  ChartWidgetDatabaseDatasourceSchema,
  DatasourceChartSchema.extend({
    type: z.literal(WidgetDatasourceTypeSchema.enum.CUSTOM),
  }),
  DatasourceMySQLSchema.extend({
    type: z.literal(WidgetDatasourceTypeSchema.enum.MYSQL),
  }),
  DatasourcePostgreSQLSchema.extend({
    type: z.literal(WidgetDatasourceTypeSchema.enum.POSTGRE_SQL),
  }),
]);

export type ChartWidgetDatasource = z.infer<typeof ChartWidgetDatasourceSchema>;

export const widgetDateFormatSchema = z.enum(['YEAR_MONTH_DAY', 'YEAR_SEASON', 'YEAR_MONTH', 'YEAR_WEEK', 'YEAR']);
export type WidgetDateFormat = z.infer<typeof widgetDateFormatSchema>;

export const ChartWidgetSchema = BaseWidgetSchema.extend({
  type: z.literal(WidgetTypeSchema.enum.CHART),
  datasource: ChartWidgetDatasourceSchema,
  settings: z
    .object({
      showEmptyValues: z.boolean().optional(),
      showDataTips: z.boolean().optional(),
      excludeZeroPoint: z.boolean().optional(),
      theme: z.string().optional(),
      sortByAxis: z.enum(['X', 'Y']).default('X').optional(),
      sortRule: z.enum(['ASC', 'DESC']).default('ASC').optional(),
      isSepareted: z.boolean().optional(), // Separate multiple values
      showWidgetDateFormat: z.boolean().optional(),
      dateFormat: widgetDateFormatSchema.default('YEAR_MONTH_DAY').optional(),
    })
    .optional(),
});
export type ChartWidgetBO = z.infer<typeof ChartWidgetSchema>;

export const NumberWidgetDatabaseDatasourceSchema = DatasourceDatabaseSchema.extend({
  type: z.literal(WidgetDatasourceTypeSchema.enum.DATABASE),
  metricsType: WidgetMetricsTypeSchema.default('COUNT_RECORDS'),
  metrics: WidgetMetricsSchema.optional(),
});

export type NumberWidgetDatabaseDatasource = z.infer<typeof NumberWidgetDatabaseDatasourceSchema>;

export const NumberWidgetCustomDatasourceSchema = z.object({
  type: z.literal(WidgetDatasourceTypeSchema.enum.CUSTOM),
  number: z.number(),
});
export type NumberWidgetCustomDatasource = z.infer<typeof NumberWidgetCustomDatasourceSchema>;

export const NumberWidgetDatasourceSchema = z.union([
  NumberWidgetDatabaseDatasourceSchema,
  NumberWidgetCustomDatasourceSchema,
  DatasourceMySQLSchema.extend({
    type: z.literal(WidgetDatasourceTypeSchema.enum.MYSQL),
  }),
  DatasourcePostgreSQLSchema.extend({
    type: z.literal(WidgetDatasourceTypeSchema.enum.POSTGRE_SQL),
  }),
]);

export type NumberWidgetDatasource = z.infer<typeof NumberWidgetDatasourceSchema>;

export const NumberWidgetSchema = BaseWidgetSchema.extend({
  type: z.literal(WidgetTypeSchema.enum.NUMBER),
  targetValue: z.number().or(z.string()).optional(),
  summaryDescription: iStringSchema.optional(),
  datasource: NumberWidgetDatasourceSchema,
  // filters: z.record(z.array(z.record(z.record(z.number())))),
});
export type NumberWidgetBO = z.infer<typeof NumberWidgetSchema>;

export const TextWidgetSchema = BaseWidgetSchema.extend({
  type: z.literal(WidgetTypeSchema.enum.TEXT),
  datasource: z.object({
    type: z.literal(WidgetDatasourceTypeSchema.enum.CUSTOM),
    text: iStringSchema,
  }),
});

export type TextWidget = z.infer<typeof TextWidgetSchema>;

export const IconsWidgetBOSchema = BaseWidgetSchema.extend({
  type: z.literal('ICONS'),
  icons: z.array(
    z.object({
      icon: AvatarLogoSchema,
      name: iStringSchema,
      url: z.string(),
    }),
  ),
});

export type IconsWidgetBO = z.infer<typeof IconsWidgetBOSchema>;

// export const WidgetsSchema = z.union([
//   ChartWidgetSchema,
//   NumberWidgetSchema,
//   DatabaseWidgetBaseSchema,
// ]);
// export type Widget = z.infer<typeof WidgetsSchema>;

const ListWidgetItemSchema = z.object({
  id: z.string(),
  icon: AvatarLogoSchema.optional(),
  name: z.string(),
  // description: z.string().optional(),
  tags: z.array(z.string()).optional(),
  recipients: z.array(RecipientVOSchema).optional(),
  createdAt: z.string().datetime().optional(),
  url: z.string(),
  dot: z.boolean().optional(),
  // type: z.string().optional(),
  // status: MissionStatusSchema.optional(),
});
export type ListWidgetItemBO = z.infer<typeof ListWidgetItemSchema>;

export const ListWidgetBOSchema = BaseWidgetSchema.extend({
  type: z.literal('LIST'),
  items: z.array(ListWidgetItemSchema),
});
export type ListWidgetBO = z.infer<typeof ListWidgetBOSchema>;

export const PivotTableAggregationSchema = z.enum(['avg', 'sum', 'min', 'max', 'count']);
export type PivotTableAggregation = z.infer<typeof PivotTableAggregationSchema>;

export const DatabasePivotTableWidgetDatasourceSchema = z.object({
  type: z.literal(WidgetDatasourceTypeSchema.Enum.DATABASE),
  databaseId: z.string().optional(),
  databaseTemplateId: z.string().optional(),
  viewId: z.string().optional(),
  viewTemplateId: z.string().optional(),
  fields: z.object({
    rows: z.array(z.object({ fieldId: z.string().optional(), fieldTemplateId: z.string().optional() })),
    columns: z.array(z.object({ fieldId: z.string().optional(), fieldTemplateId: z.string().optional() })),
    values: z.array(
      z
        .object({
          fieldId: z.string().or(z.literal('COUNT_RECORDS')).optional(),
          fieldTemplateId: z.string().optional(),
          aggregation: PivotTableAggregationSchema,
        })
        .or(z.literal(WidgetMetricsTypeSchema.Enum.COUNT_RECORDS)),
    ),
  }),
});

export type PivotTableWidgetDatasource = z.infer<typeof DatabasePivotTableWidgetDatasourceSchema>;

export const PivotTableWidgetSchema = BaseWidgetSchema.extend({
  type: z.literal(WidgetTypeSchema.enum.PIVOT_TABLE),
  datasource: DatabasePivotTableWidgetDatasourceSchema,
  settings: z
    .object({
      showTotalRow: z.boolean().optional(),
      columnSort: z.enum(['ASC', 'DESC', 'DEFAULT']).default('DEFAULT').optional(),
      rowSort: z.enum(['ASC', 'DESC', 'DEFAULT']).default('DEFAULT').optional(),
      rowDateFormat: widgetDateFormatSchema.default('YEAR_MONTH_DAY').optional(),
      columnDateFormat: widgetDateFormatSchema.default('YEAR_MONTH_DAY').optional(),
    })
    .optional(),
  columnField: DatabaseFieldSchema.optional(),
  rowField: DatabaseFieldSchema.optional(),
});

export type PivotTableWidgetBO = z.infer<typeof PivotTableWidgetSchema>;

export const PivotTableFieldsColumnSchema = z.object({
  field: z.string(),
  title: z.string().optional(),
  description: z.string().optional(),
});

export type PivotTableFieldsColumn = z.output<typeof PivotTableFieldsColumnSchema> & {
  children: PivotTableFieldsColumn[];
};

export const PivotTableDataConfigMetaSchema = z.object({
  field: z.string(),
  name: z.string(),
  description: z.string().optional(),
});

export const PivotTableColumnSchema = z.object({
  colId: z.string(), // fieldId
  field: z.string(), // field name
  aggFunc: PivotTableAggregationSchema.optional(),
  rowGroup: z.boolean().optional(), // used at row field
  pivot: z.boolean().optional(), // used at column field
  valueGetter: z.string().optional(), // used at value field
  headerName: z.string().optional(), // used at value field
  pivotComparator: z.function().args(z.string(), z.string()).returns(z.number()).optional(),
});

export type PivotTableColumn = z.infer<typeof PivotTableColumnSchema>;

// standard pivot table data
export const PivotTableWidgetRenderVOSchema = z.object({
  columnDefs: PivotTableColumnSchema.array(),
  rowData: z.array(z.record(z.number().or(z.string()))),
  settings: z
    .object({
      showTotalRow: z.boolean().optional(),
      columnSort: z.enum(['ASC', 'DESC', 'DEFAULT']).default('DEFAULT').optional(),
      rowSort: z.enum(['ASC', 'DESC', 'DEFAULT']).default('DEFAULT').optional(),
    })
    .optional(),
  columnField: DatabaseFieldSchema.optional(),
  rowField: DatabaseFieldSchema.optional(),
  fields: DatabaseFieldSchema.array().optional(),
});

export type PivotTableWidgetRenderVO = z.infer<typeof PivotTableWidgetRenderVOSchema>;

export const EmbedWidgetSchema = BaseWidgetSchema.extend({
  type: z.literal(WidgetTypeSchema.enum.EMBED),
  url: z.string().optional(),
});
export type EmbedWidgetBO = z.infer<typeof EmbedWidgetSchema>;

// Special configuration for Bika Welcome, also used as space station announcement
export const BikaWelcomeWidgetSchema = BaseWidgetSchema.extend({
  type: z.literal('BIKA'),
  announcement: iStringSchema.optional(),
});

export type BikaWelcomeWidgetBO = z.infer<typeof BikaWelcomeWidgetSchema>;

// Progress Bar
export const ProgressBarWidgetSchema = BaseWidgetSchema.extend({
  type: z.literal('PROGRESS_BAR'),
  title: z.string(), // title, e.g. attachment capacity
  used: z.number(), // value, e.g. used capacity
  usedLabel: z.string(), // label, e.g. "Used"
  unusedLabel: z.string(), // label, e.g. "Remaining"
  total: z.number(), // value, e.g. total capacity, progress bar percentage is calculated from these two values
  color: z.string().optional(), // progress bar color, e.g. #FF0000

  // Don't use z.function() since trpc transfomer will remove it
  // buttons: z
  //   .object({
  //     name: z.string(),
  //     onclick: z.function().args().returns(z.void()),
  //   })
  //   .array()
  //   .optional(),
});

export type ProgressBarWidgetBO = z.infer<typeof ProgressBarWidgetSchema>;

export const SubscribeInfoWidgetSchema = BaseWidgetSchema.extend({
  type: z.literal('SUBSCRIBE_INFO'),
  title: z.string(),
  tips: iStringSchema.optional(),
  content: z.custom<any>((data) => true),

  // Don't use z.function() since trpc transfomer will remove it
  // buttons: z
  //   .object({
  //     name: z.string(),
  //     onclick: z.function().args().returns(z.void()),
  //   })
  //   .array()
  //   .optional(),
});
export type SubscribeInfoWidgetBO = z.infer<typeof SubscribeInfoWidgetSchema>;

export const WidgetSchema = z.discriminatedUnion('type', [
  ChartWidgetSchema,
  NumberWidgetSchema,
  TextWidgetSchema,
  PivotTableWidgetSchema,
  EmbedWidgetSchema,
  // The following are custom widgets, not related to persistence
  // BikaWelcomeWidgetSchema,
  // ListWidgetBOSchema,
  // IconsWidgetBOSchema,
  // ProgressBarWidgetSchema,
  // SubscribeInfoWidgetSchema,
]);
export type WidgetBO = z.infer<typeof WidgetSchema>;

export const defaultDashboardWidgetBO = (widget: WidgetType, locale: Locale): WidgetBO | null => {
  switch (widget) {
    case 'CHART': {
      const name = iStringParse(
        {
          en: 'New Chart',
          'zh-CN': '新图表',
          'zh-TW': '新圖表',
          ja: '新しいチャート',
        },
        locale,
      );
      return {
        // Defaults
        datasource: {
          type: 'DATABASE',
          chartType: 'line',
          metricsType: 'COUNT_RECORDS',
          dimension: '',
          // xAxis: {
          //   type: 'category',
          //   data: ['xAxis'],
          // },
          // yAxis: {
          //   type: 'value',
          // },
          // series: [
          //   {
          //     type: 'line',
          //     data: [0],
          //   },
          // ],
        },
        settings: {
          showEmptyValues: true,
          showDataTips: true,
          excludeZeroPoint: false,
          theme: 'theme_color_1',
          sortByAxis: 'X',
          sortRule: 'ASC',
        },
        name,

        // Overwrite with input
        // ...widget,
        type: 'CHART',
      };
    }
    case 'NUMBER': {
      const name = iStringParse(
        {
          en: 'New Number',
          'zh-CN': '新数字',
          'zh-TW': '新數字',
          ja: '新しい数字',
        },
        locale,
      );
      return {
        // Defaults
        datasource: {
          type: 'DATABASE',
          metricsType: 'COUNT_RECORDS',
        },
        name,

        // Overwrite with input
        // ...widget,
        type: 'NUMBER',
      };
    }
    case 'TEXT': {
      const name = iStringParse(
        {
          en: 'New Text',
          'zh-CN': '新文字',
          'zh-TW': '新文字',
          ja: '新しいテキスト',
        },
        locale,
      );
      return {
        // Defaults
        datasource: {
          type: 'CUSTOM',
          text: {
            en: 'New Text',
            'zh-CN': '新文字',
            'zh-TW': '新文字',
            ja: '新しいテキスト',
          },
        },
        name,

        // Overwrite with input
        // ...widget,
        type: 'TEXT',
      };
    }
    case 'PIVOT_TABLE': {
      const name = iStringParse(
        {
          en: 'New Pivot Table',
          'zh-CN': '新透视表',
          'zh-TW': '新透視表',
          ja: '新しいピボットテーブル',
        },
        locale,
      );
      return {
        name,
        type: 'PIVOT_TABLE',
        datasource: {
          type: 'DATABASE',
          // databaseId: '',
          fields: {
            rows: [
              {
                fieldId: '',
              },
            ],
            columns: [
              {
                fieldId: '',
              },
            ],
            values: [
              {
                aggregation: PivotTableAggregationSchema.Enum.count,
                fieldId: 'COUNT_RECORDS',
              },
            ],
          },
        },
      };
    }
    case 'EMBED': {
      const name = iStringParse(
        {
          en: 'New Embed',
          'zh-CN': '新嵌入文档',
          'zh-TW': '新嵌入文檔',
          ja: '新しい埋め込み',
        },
        locale,
      );
      return {
        name,

        // Overwrite with input
        // ...widget,
        type: 'EMBED',
      };
    }

    default:
      return null;
  }
};
