import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import { WidgetLayoutSchema, WidgetSchema } from './bo-widgets';
import { BaseCreateNodeResourceBOSchema, NodeUpdateBOSchema } from '../node/base';

export const DashboardUpdateBOSchema = NodeUpdateBOSchema.extend({
  resourceType: z.literal('DASHBOARD'),
  name: iStringSchema.optional(),
  description: iStringSchema.optional(),
  widgets: z.array(z.object({ id: z.string(), layout: WidgetLayoutSchema.optional() })).optional(),
});

export type DashboardUpdateBO = z.infer<typeof DashboardUpdateBOSchema>;

export const DashboardCreateBOSchema = BaseCreateNodeResourceBOSchema.extend({
  resourceType: z.literal('DASHBOARD'),
  widgets: z.array(WidgetSchema).optional(),
});

export type DashboardCreateBO = z.infer<typeof DashboardCreateBOSchema>;
