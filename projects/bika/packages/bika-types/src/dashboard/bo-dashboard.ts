import { iStringParse, Locale } from 'basenext/i18n';
import { z } from 'zod';
import { WidgetSchema } from './bo-widgets';
import { BaseNodeResourceBOSchema, DashboardNodeType } from '../node/base';
/**
 * Dashboard Filters
 * - Time Filters:
 *   - Month and Year
 *   - Quarter and Year
 *   - Single Date
 *   - Date Range
 *   - Relative Date
 *   - All Options
 * - Numeric Filters:
 *   - Numeric Range
 *   - Numeric Interval
 *   - Numeric List
 * - Text Filters:
 *   - Text List
 *   - Text Interval
 * - Option (Single/Multi Select?) Filters:
 *   - Option List
 *   - Option Interval
 */
export const DashboardFilterSchema = z.object({
  filterType: z.literal('DATETIME'),
  datetimeType: z.enum(['YEAR_MONTH', 'YEAR_QUARTER', 'DATE', 'DATETIME_RANGE']),
  widgetsParametersMap: z.record(z.any()),
});

export type DashboardFilter = z.infer<typeof DashboardFilterSchema>;

export const DashboardSchema = BaseNodeResourceBOSchema.extend({
  resourceType: DashboardNodeType,
  widgets: z.array(WidgetSchema),
  filters: z.array(DashboardFilterSchema).optional(),
});
export type Dashboard = z.infer<typeof DashboardSchema>;

export const defaultDashboardBO = (dashboard: Partial<Dashboard>, locale: Locale): Dashboard => ({
  // Defaults
  name: iStringParse(
    {
      en: 'New Dashboard',
      'zh-CN': '新仪表盘',
      'zh-TW': '新儀表板',
      ja: '新しいダッシュボード',
    },
    locale,
  ),
  widgets: [],

  // Overwrite with input
  ...dashboard,
  resourceType: 'DASHBOARD',
});
