import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import { DatabaseFieldTypeSchema } from '../database/bo-field-type';
// Datasource is used externally, usually combined with extend({type: 'XXX'})

/**
 * Usually optional, when configured, automatically reads data source database data and generates charts
 */
export const DatasourceDatabaseSchema = z.object({
  databaseId: z.string().optional(),
  viewId: z.string().optional(),
  databaseTemplateId: z.string().optional(),
  viewTemplateId: z.string().optional(),
});

export type DatasourceDatabase = z.infer<typeof DatasourceDatabaseSchema>;

export const WidgetMetricsTypes = [
  // Total records
  'COUNT_RECORDS',
  // Statistics by field
  'AGGREGATION_BY_FIELD',
] as const;
export const WidgetMetricsTypeSchema = z.enum(WidgetMetricsTypes);
export type WidgetMetricsType = z.infer<typeof WidgetMetricsTypeSchema>;

export const ChartMetricsAggregateTypes = ['SUM', 'AVG', 'MAX', 'MIN'] as const;
export const ChartMetricsAggregateTypeSchema = z.enum(ChartMetricsAggregateTypes);

export type AggregateType = z.infer<typeof ChartMetricsAggregateTypeSchema>;
export const NumberMetricsAggregateTypes = [
  // Hidden
  'HIDDEN',
  // Not filled
  'NOT_FILLED',
  // Filled
  'FILLED',
] as const;
export const NumberMetricsAggregateTypeSchema = z.enum(NumberMetricsAggregateTypes);
export type NumberMetricsAggregateType = z.infer<typeof NumberMetricsAggregateTypeSchema>;

export const WidgetMetricsSchema = z.object({
  aggregationType: z.union([ChartMetricsAggregateTypeSchema, NumberMetricsAggregateTypeSchema]),
  fieldId: z.string().optional(),
  fieldType: DatabaseFieldTypeSchema.optional(),
  fieldTemplateId: z.string().optional(),
});
export type WidgetMetrics = z.infer<typeof WidgetMetricsSchema>;

// Follow echart's type
export const ChartWidgetTypes = [
  // Line chart
  'line',
  // Pie chart
  'pie',
  // Bar chart
  'bar',
] as const;
export const ChartWidgetTypeSchema = z.enum(ChartWidgetTypes);
export type ChartWidgetType = z.infer<typeof ChartWidgetTypeSchema>;

/**
 * Chart datasets, consistent with echarts datasets, used for VO values and BO custom
 *
 * Details: https://echarts.apache.org/examples/en/editor.html?c=line-smooth
 */

export const ChartAxisLabelFormatterSchema = z.object({
  formatter: z.string().optional(),
});

export const DatasourceChartSchema = z.object({
  xAxis: z
    .object({
      name: iStringSchema.default('xAxis name').optional(),
      type: z.literal('category'),
      boundaryGap: z.boolean().optional(),
      data: z.array(z.string()),
      axisLabel: ChartAxisLabelFormatterSchema.optional(),
    })
    .default({ type: 'category', data: ['xAxis'] }),
  yAxis: z
    .object({
      name: iStringSchema.default('yAxis name').optional(),
      type: z.literal('value'),
      axisLabel: ChartAxisLabelFormatterSchema.optional(),
    })
    .default({ type: 'value' }),
  series: z
    .array(
      z.object({
        type: ChartWidgetTypeSchema,
        data: z.array(z.union([z.number(), z.string()])),
      }),
    )
    .default([{ type: 'line', data: [0] }]),
});

export type EChartRenderVO = z.infer<typeof DatasourceChartSchema>;

export const DatasourceMySQLSchema = z.object({
  connectionUrl: z.string().default('connectionUrl'),
  sql: z.string().default('sql'),
});

export const DatasourcePostgreSQLSchema = z.object({
  connectionUrl: z.string().default('connectionUrl'),
  sql: z.string().default('sql'),
});
