import { AvatarLogoSchema } from 'basenext/avatar';
import { z } from 'zod';
import { NodeShareScopeSchema } from '../permission/access-vos';
import { AccessPrivilegeSchema } from '../permission/bo';

export const SpaceAuditLogTypes = [
  'space.create',
  'space.update',
  'template.install',
  'node.get',
  'node.create',
  'node.update',
  'node.move',
  'node.delete',
  'node.publish',
  'node.detach',
  'node.import',
  'node.export',
  'node.upgrade',
  'share.grant',
  'share.revoke',
  'share.restore',
  'share.scope.update',
  'share.password.create',
  'share.password.update',
  'share.password.delete',
  'invitation.link.create',
  'invitation.link.delete',
  'invitation.email.send',
  'invitation.email.resend',
  'invitation.email.delete',
  'invitation.link.accept',
  'invitation.email.accept',
  'space.outgoing-webhook',
] as const;

export const SpaceAuditLogTypeSchema = z.enum(SpaceAuditLogTypes);
export type SpaceAuditLogType = z.infer<typeof SpaceAuditLogTypeSchema>;

// const ClientInfoSchema = z.object({
//   ip: z.string(),
//   version: z.string(),
// });

// const ServerInfoSchema = z.object({
//   hostname: z.string(),
//   platform: z.string(),
// });

const SpaceCreateEventSchema = z.object({
  type: z.literal(SpaceAuditLogTypeSchema.enum['space.create']),
  name: z.string().describe('User name'),
  email: z.string().optional().describe('User email'),
});

const SpaceUpdateEventSchema = z.object({
  type: z.literal('space.update'),
});

const TemplateInstallEventSchema = z.object({
  type: z.literal('template.install'),
  id: z.string().describe('Template ID'),
  name: z.string().describe('Template name'),
  spaceid: z.string().describe('Space ID'),
  nodeid: z.string().describe('Node ID'),
});

const NodeGetEventSchema = z.object({
  type: z.literal('node.get'),
  id: z.string(),
  name: z.string(),
  spaceid: z.string().describe('Space ID'),
});

const NodeCreateEventSchema = z.object({
  type: z.literal('node.create'),
  id: z.string(),
  name: z.string(),
  parent: z.string(),
  spaceid: z.string().describe('Space ID'),
});

const NodeUpdateEventSchema = z.object({
  type: z.literal('node.update'),
  id: z.string(),
  name: z.string(),
  spaceid: z.string().describe('Space ID'),
});

const NodeMoveEventSchema = z.object({
  type: z.literal('node.move'),
  id: z.string(),
  name: z.string(),
  spaceid: z.string().describe('Space ID'),
});

const NodeDeleteEventSchema = z.object({
  type: z.literal('node.delete'),
  name: z.string(),
  spaceid: z.string().describe('Space ID'),
});

const NodePublishEventSchema = z.object({
  type: z.literal('node.publish'),
  id: z.string(),
  name: z.string(),
  spaceid: z.string().describe('Space ID'),
  templateid: z.string().describe('Template ID after publishing'),
});

const NodeDetachEventSchema = z.object({
  type: z.literal('node.detach'),
  id: z.string(),
  name: z.string(),
  spaceid: z.string().describe('Space ID'),
});

const NodeImportEventSchema = z.object({
  type: z.literal('node.import'),
  id: z.string(),
  name: z.string(),
  parent: z.string(),
  spaceid: z.string().describe('Space ID'),
});

const NodeExportEventSchema = z.object({
  type: z.literal('node.export'),
  id: z.string(),
  name: z.string(),
  spaceid: z.string().describe('Space ID'),
  url: z.string().describe('Download URL for exported file'),
});

const NodeUpgradeEventSchema = z.object({
  type: z.literal('node.upgrade'),
  id: z.string(),
  name: z.string(),
  spaceid: z.string().describe('Space ID'),
});

const ShareGantPermissionEventSchema = z.object({
  type: z.literal('share.grant'),
  id: z.string().describe('Node ID'),
  name: z.string().describe('Node name'),
  units: z.string().describe('List of authorized units'),
  privilege: AccessPrivilegeSchema.describe('Privilege'),
});

const ShareRevokePermissionEventSchema = z.object({
  type: z.literal('share.revoke'),
  id: z.string().describe('Node ID'),
  name: z.string().describe('Node name'),
  unit: z.string().describe('Authorized unit'),
});

const ShareRestorePermissionEventSchema = z.object({
  type: z.literal('share.restore'),
  id: z.string().describe('Node ID'),
  name: z.string().describe('Node name'),
});

const ShareUpdateScopeEventSchema = z.object({
  type: z.literal('share.scope.update'),
  id: z.string().describe('Node ID'),
  name: z.string().describe('Node name'),
  scope: NodeShareScopeSchema.describe('Share scope'),
});

const ShareCreatePasswordEventSchema = z.object({
  type: z.literal('share.password.create'),
  id: z.string().describe('Node ID'),
  name: z.string().describe('Node name'),
});

const ShareUpdatePasswordEventSchema = z.object({
  type: z.literal('share.password.update'),
  id: z.string().describe('Node ID'),
  name: z.string().describe('Node name'),
});

const ShareDeletePasswordEventSchema = z.object({
  type: z.literal('share.password.delete'),
  id: z.string().describe('Node ID'),
  name: z.string().describe('Node name'),
});

const LinkInvitationCreateEventSchema = z.object({
  type: z.literal('invitation.link.create'),
  token: z.string().describe('Link invitation ID'),
});

const LinkInvitationDeleteEventSchema = z.object({
  type: z.literal('invitation.link.delete'),
  token: z.string().describe('Link invitation ID'),
});

const InviteEmailSendEventSchem = z.object({
  type: z.literal('invitation.email.send'),
  emails: z.string().describe('List of invitation emails'),
});

const InviteEmailResendEventSchem = z.object({
  type: z.literal('invitation.email.resend'),
  email: z.string().email().describe('Invitation email'),
});

const InviteEmailDeleteEventSchem = z.object({
  type: z.literal('invitation.email.delete'),
  email: z.string().email().describe('Invitation email'),
});

const InvitationLinkAcceptEventSchema = z.object({
  type: z.literal('invitation.link.accept'),
  token: z.string().describe('Link invitation ID'),
});

const InvitationEmailAcceptEventSchema = z.object({
  type: z.literal('invitation.email.accept'),
  email: z.string().email().describe('Invitation email'),
});

export const AuditSpaceOutgoingWebhookAction = z.object({
  type: z.literal('space.outgoing-webhook'),
  vo: z.string(), // OutgoingWebhookVOSchema,
  // http: z.any().optional(),
});

export const SpaceAuditLogDataSchema = z.discriminatedUnion('type', [
  SpaceCreateEventSchema,
  SpaceUpdateEventSchema,
  TemplateInstallEventSchema,
  NodeGetEventSchema,
  NodeCreateEventSchema,
  NodeUpdateEventSchema,
  NodeMoveEventSchema,
  NodeDeleteEventSchema,
  NodePublishEventSchema,
  NodeDetachEventSchema,
  NodeImportEventSchema,
  NodeExportEventSchema,
  NodeUpgradeEventSchema,
  ShareGantPermissionEventSchema,
  ShareRevokePermissionEventSchema,
  ShareRestorePermissionEventSchema,
  ShareUpdateScopeEventSchema,
  ShareCreatePasswordEventSchema,
  ShareUpdatePasswordEventSchema,
  ShareDeletePasswordEventSchema,
  LinkInvitationCreateEventSchema,
  LinkInvitationDeleteEventSchema,
  InviteEmailSendEventSchem,
  InviteEmailResendEventSchem,
  InviteEmailDeleteEventSchem,
  InvitationLinkAcceptEventSchema,
  InvitationEmailAcceptEventSchema,
  AuditSpaceOutgoingWebhookAction,
]);
export type SpaceAuditLogData = z.infer<typeof SpaceAuditLogDataSchema>;

// Storage structure, stored in OpenObserve
export const SpaceAuditLogSchema = z.object({
  // audit log type
  kind: z.literal('SPACE_AUDIT_LOG'),
  // user id
  userid: z.string(),
  // space id
  spaceid: z.string(),
  // event name
  type: SpaceAuditLogTypeSchema,
  // event format data
  data: z.string().optional(), // Pass in string for open observe, parse with SpaceAuditLogSchema in business logic
  // data: SpaceAuditEventDataSchema.optional(),
  // data: z.record(z.unknown()).optional(),
  // client info
  client_ip: z.string().optional(),
  client_version: z.string().optional(),
  // server info
  server_hostname: z.string().optional(),
  server_platform: z.string().optional(),
  createdat: z.string().datetime(),
});
export type SpaceAuditLog = z.infer<typeof SpaceAuditLogSchema>;

export const AuditEventActorSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().nullish(),
  avatar: AvatarLogoSchema.optional(),
});

export type AuditEventActor = z.infer<typeof AuditEventActorSchema>;
