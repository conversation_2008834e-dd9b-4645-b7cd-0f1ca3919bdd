import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import {
  AutomationNotificationTypeSchema,
  DatabaseRecordNotificationTypeSchema,
  DatabaseRecordCommentNotificationTypeSchema,
  MissionNotificationTypeSchema,
  NodeRequestAccessNotificationTypeSchema,
  ReminderNotificationTypeSchema,
  ReportNotificationTypeSchema,
  SystemNotificationTypeSchema,
  DatabaseRecordMentionNotificationTypeSchema,
} from './bo-notification-type';

// System info, directly supports multiple languages
export const SystemNotificationPropertySchema = z.object({
  type: SystemNotificationTypeSchema,
  title: iStringSchema,
  content: iStringSchema,
});
export type SystemNotificationProperty = z.infer<typeof SystemNotificationPropertySchema>;

export const ReminderNotificationPropertySchema = z.object({
  type: ReminderNotificationTypeSchema,
  spaceId: z.string(),
  reminderId: z.string(),
});
export type ReminderNotificationProperty = z.infer<typeof ReminderNotificationPropertySchema>;

export const ReportNotificationPropertySchema = z.object({
  type: ReportNotificationTypeSchema,
  spaceId: z.string(),
  reportId: z.string(),
});
export type ReportNotificationProperty = z.infer<typeof ReportNotificationPropertySchema>;

export const MissionNotificationPropertySchema = z.object({
  type: MissionNotificationTypeSchema,
  missionId: z.string(),
});
export type MissionNotificationProperty = z.infer<typeof MissionNotificationPropertySchema>;

export const AutomationRunFailedNotificationPropertySchema = z.object({
  type: AutomationNotificationTypeSchema,
  automationId: z.string(),
  actionId: z.string(),
  runHistoryId: z.string(),
  errorMessage: z.string(),
});

export const DatabaseRecordNotificationPropertySchema = z.object({
  type: DatabaseRecordNotificationTypeSchema,
  databaseId: z.string(),
  recordId: z.string(),
  spaceId: z.string(),
});
export type DatabaseNotificationProperty = z.infer<typeof DatabaseRecordNotificationPropertySchema>;

export const DatabaseRecordCommentNotificationPropertySchema = z.object({
  type: DatabaseRecordCommentNotificationTypeSchema,
  databaseId: z.string(),
  recordId: z.string(),
  commentId: z.string(),
});

export const DatabaseRecordMentionNotificationPropertySchema = z.object({
  type: DatabaseRecordMentionNotificationTypeSchema,
  fromUserId: z.string().nullable(),
  databaseId: z.string(),
  recordId: z.string(),
  spaceId: z.string(),
  fieldId: z.string(),
});

export const NodeRequestAccessNotificationPropertySchema = z.object({
  type: NodeRequestAccessNotificationTypeSchema,
  spaceId: z.string(),
  nodeId: z.string(),
  from: z.string().describe('Requester user ID'),
});

export type NodeRequestAccessNotificationProperty = z.infer<typeof NodeRequestAccessNotificationPropertySchema>;

export const NotificationPropertySchema = z.union([
  SystemNotificationPropertySchema,
  ReminderNotificationPropertySchema,
  ReportNotificationPropertySchema,
  MissionNotificationPropertySchema,
  AutomationRunFailedNotificationPropertySchema,
  DatabaseRecordNotificationPropertySchema,
  DatabaseRecordCommentNotificationPropertySchema,
  DatabaseRecordMentionNotificationPropertySchema,
  NodeRequestAccessNotificationPropertySchema,
]);
export type NotificationProperty = z.infer<typeof NotificationPropertySchema>;
