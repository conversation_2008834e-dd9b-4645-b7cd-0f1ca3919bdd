import { iStringSchema } from 'basenext/i18n';
import { PaginationSchema } from 'basenext/pagination';
import { z } from 'zod';
import { ActionSchema } from './bo-actions';
import { AutomationStatusSchema } from './bo-automation';
import { NativeTriggerSchema } from './bo-triggers';
import { RecordDataSchema } from '../database/bo-record';
import { AutomationNodeType, BaseCreateNodeResourceBOSchema, NodeUpdateBOSchema } from '../node/base';

// ===== Automation =====
export const AutomationUpdateBOSchema = NodeUpdateBOSchema.extend({
  resourceType: z.literal('AUTOMATION'),
  name: iStringSchema.optional(),
  description: iStringSchema.optional(),
  actions: z
    .array(
      z.object({
        id: z.string(),
      }),
    )
    .optional(), // automation action sorted array
});
export type AutomationUpdateBO = z.infer<typeof AutomationUpdateBOSchema>;

export const AutomationCreateBOSchema = BaseCreateNodeResourceBOSchema.extend({
  resourceType: AutomationNodeType,
  status: AutomationStatusSchema.optional(),
  actions: z.array(ActionSchema).optional(),
  triggers: z.array(NativeTriggerSchema).optional(),
});
export type AutomationCreateBO = z.infer<typeof AutomationCreateBOSchema>;

export const AutomationManuallyRunDTOSchema = z.object({
  automationId: z.string(),
  triggerId: z.string().optional(), // There may be multiple Manual triggers, specify the trigger to get the corresponding set run result
  cellData: RecordDataSchema.optional(),
});
export type AutomationManuallyRunDTO = z.infer<typeof AutomationManuallyRunDTOSchema>;

// ===== Action =====
export const AutomationActionCreateDTOSchema = z.object({
  automationId: z.string(),
  parentActionId: z.string().optional(),
  // bo
  action: ActionSchema,
});
export type AutomationActionCreateDTO = z.infer<typeof AutomationActionCreateDTOSchema>;

export const AutomationActionUpdateDTOSchema = z.object({
  actionId: z.string(),
  // bo
  action: ActionSchema.optional(),
  preActionId: z.string().optional().nullable(),
});
export type AutomationActionUpdateDTO = z.infer<typeof AutomationActionUpdateDTOSchema>;

export const AutomationActionTestDTOSchema = z.object({
  actionId: z.string(),
});
export type AutomationActionTestDTO = z.infer<typeof AutomationActionTestDTOSchema>;

// ===== Trigger =====
export const AutomationTriggerUpdateDTOSchema = z.object({
  triggerId: z.string(),
  // bo
  trigger: NativeTriggerSchema.optional(),
  preTriggerId: z.string().optional().nullable(),
});
export type AutomationTriggerUpdateDTO = z.infer<typeof AutomationTriggerUpdateDTOSchema>;

export const AutomationTriggerCreateDTOSchema = z.object({
  automationId: z.string(),
  // bo
  trigger: NativeTriggerSchema,
});
export type AutomationTriggerCreateDTO = z.infer<typeof AutomationTriggerCreateDTOSchema>;

export const AutomationTriggerTestDTOSchema = z.object({
  triggerId: z.string(),
});
export type AutomationTriggerTestDTO = z.infer<typeof AutomationTriggerTestDTOSchema>;

// ===== Automation Run History =====
const RunHistoryListBaseSchema = z.object({
  automationId: z.string(),
  sort: z
    .string()
    .refine((value) => ['name', 'createdAt'].includes(value), { message: 'Invalid sort field' })
    .default('createdAt'),
  direction: z
    .string()
    .optional()
    .transform((value) => (value === 'asc' ? value : 'desc')),
});
export const RunHistoryListDTOSchema = RunHistoryListBaseSchema.merge(PaginationSchema);
export type RunHistoryListDTO = z.infer<typeof RunHistoryListDTOSchema>;
