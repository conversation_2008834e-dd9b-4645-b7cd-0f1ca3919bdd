import { iStringParse, type Locale } from 'basenext/i18n';
import { Action } from './bo-actions';
import { AutomationSchema, type Automation } from './bo-automation';
import { AutomationCreateBOSchema, type AutomationCreateBO } from './dto-automation';
import { AutomationVO } from './vo-automation';

export function defaultAutomationCreateBO(locale: Locale) {
  return AutomationCreateBOSchema.parse({
    resourceType: 'AUTOMATION',
    name: iStringParse(
      {
        en: 'new automation',
        'zh-CN': '新的自动化流程',
        'zh-TW': '新的自動化流程',
        ja: '新しい自動化',
      },
      locale,
    ),
    description: undefined,
  });
}

export function defaultAutomationActionBO(): Action {
  return {
    actionType: 'DUMMY_ACTION',
    description: '',
    input: {},
  };
}
export function defaultAutomationBO(
  id: {
    id?: string;
    templateId?: string;
  },
  init: AutomationCreateBO,
): Automation {
  return AutomationSchema.parse({
    resourceType: 'AUTOMATION',
    name: init.name,
    description: init.description,
    id: id.id,
    templateId: id.templateId,
    status: 'INACTIVE',
    triggers: [],
    actions: [],
  });
}

export const defaultAutomationVO = (id: { id?: string; templateId?: string }, locale: Locale): AutomationVO => ({
  id: id.id || id.templateId!,
  name: iStringParse(
    {
      en: 'new automation',
      'zh-CN': '新的自动化流程',
      'zh-TW': '新的自動化流程',
      ja: '新しい自動化',
    },
    locale,
  ),
  triggers: [
    {
      id: 'story',
      type: 'DUMMY_TRIGGER',
      bo: {
        triggerType: 'DUMMY_TRIGGER',
      },
      isVerified: false,
    },
  ],
  actions: [
    {
      id: 'story',
      type: 'DUMMY_ACTION',
      bo: {
        actionType: 'DUMMY_ACTION',
      },
      isVerified: true,
    },
  ],
  isActive: false,
  isVerified: false,
});
