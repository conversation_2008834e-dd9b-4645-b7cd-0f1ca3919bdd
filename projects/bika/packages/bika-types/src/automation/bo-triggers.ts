import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import {
  DatabaseInputSchema,
  DatabaseWithFilterInputSchema,
  DatetimeFieldReachedInputSchema,
  FormInputSchema,
  HttpIfChangeTriggerInputSchema,
  InboundEmailTriggerInputSchema,
  ManuallyTriggerInputSchema,
  SchedulerInputSchema,
  WebhookReceivedTriggerInputSchema,
} from './bo-automation-input';
import { HttpIfChangeTriggerStateSchema, InboundEmailTriggerStateSchema, TriggerStateSchema } from './bo-trigger-state';

export const TriggerTypes = [
  'FORM_SUBMITTED',
  'MANUALLY',
  'MEMBER_JOINED',
  'DATETIME_FIELD_REACHED',
  'RECORD_CREATED',
  'RECORD_MATCH',
  'SCHEDULER',
  'DUMMY_TRIGGER',
  'HTTP_IF_CHANGE',
  // Create a URL for incoming webhook
  'WEBHOOK_RECEIVED',

  // Button field
  'BUTTON_CLICK',

  'INBOUND_EMAIL',
] as const;

export const TriggerTypeSchema = z.enum(TriggerTypes);
export type TriggerType = z.infer<typeof TriggerTypeSchema>;

export const TriggerInputSchema = z.union([
  DatabaseInputSchema,
  DatabaseWithFilterInputSchema,
  DatetimeFieldReachedInputSchema,
  FormInputSchema,
  HttpIfChangeTriggerInputSchema,
  InboundEmailTriggerInputSchema,
  ManuallyTriggerInputSchema,
  SchedulerInputSchema,
  WebhookReceivedTriggerInputSchema,
]);
export type TriggerInput = z.infer<typeof TriggerInputSchema>;

export const BaseTriggerSchema = z.object({
  triggerType: z.string(),
  templateId: z.string().optional(),
  description: iStringSchema.optional(),
  input: TriggerInputSchema.optional(),
  state: TriggerStateSchema.optional(),
  // add for editor
  id: z.string().optional(),
});
export type BaseTrigger = z.infer<typeof BaseTriggerSchema>;

export const DatetimeFieldReachedTriggerSchema = BaseTriggerSchema.extend({
  triggerType: z.literal(TriggerTypeSchema.enum.DATETIME_FIELD_REACHED),
  input: DatetimeFieldReachedInputSchema,
});
export type DatetimeFieldReachedTrigger = z.infer<typeof DatetimeFieldReachedTriggerSchema>;

export const FormSubmittedTriggerSchema = BaseTriggerSchema.extend({
  triggerType: z.literal(TriggerTypeSchema.enum.FORM_SUBMITTED),
  input: FormInputSchema,
});
export type FormSubmittedTrigger = z.infer<typeof FormSubmittedTriggerSchema>;

export const ManuallyTriggerSchema = BaseTriggerSchema.extend({
  triggerType: z.literal(TriggerTypeSchema.enum.MANUALLY),
  // optional for backward compatibility with versions without input
  input: ManuallyTriggerInputSchema.optional(),
});
export type ManuallyTrigger = z.infer<typeof ManuallyTriggerSchema>;

export const MemberJoinedTriggerSchema = BaseTriggerSchema.extend({
  triggerType: z.literal(TriggerTypeSchema.enum.MEMBER_JOINED),
});
export type MemberJoinedTrigger = z.infer<typeof MemberJoinedTriggerSchema>;

export const RecordCreatedTriggerSchema = BaseTriggerSchema.extend({
  triggerType: z.literal(TriggerTypeSchema.enum.RECORD_CREATED),
  input: DatabaseInputSchema,
});
export type RecordCreatedTrigger = z.infer<typeof RecordCreatedTriggerSchema>;

export const RecordMatchTriggerSchema = BaseTriggerSchema.extend({
  triggerType: z.literal(TriggerTypeSchema.enum.RECORD_MATCH),
  input: DatabaseWithFilterInputSchema,
});
export type RecordMatchTrigger = z.infer<typeof RecordMatchTriggerSchema>;

export const SchedulerTriggerSchema = BaseTriggerSchema.extend({
  triggerType: z.literal('SCHEDULER'),
  input: SchedulerInputSchema,
});
export type SchedulerTrigger = z.infer<typeof SchedulerTriggerSchema>;

// Dummy Trigger, only for displaying the trigger name and description in the "Coming Soon" type template
export const DummyTriggerSchema = BaseTriggerSchema.extend({
  triggerType: z.literal(TriggerTypeSchema.enum.DUMMY_TRIGGER),
  input: z.any().optional(),
});
export type DummyTrigger = z.infer<typeof DummyTriggerSchema>;

// Periodically check HTTP, and trigger if the HTTP result is different from the last time
export const HttpIfChangeTriggerSchema = BaseTriggerSchema.extend({
  triggerType: z.literal('HTTP_IF_CHANGE'),
  input: HttpIfChangeTriggerInputSchema,
  state: HttpIfChangeTriggerStateSchema.optional(),
});
export type HttpIfChangeTrigger = z.infer<typeof HttpIfChangeTriggerSchema>;

export const WebhookReceivedTriggerSchema = BaseTriggerSchema.extend({
  triggerType: z.literal('WEBHOOK_RECEIVED'),
  input: WebhookReceivedTriggerInputSchema,
});
export type WebhookReceivedTrigger = z.infer<typeof WebhookReceivedTriggerSchema>;

export const InboundEmailTriggerSchema = BaseTriggerSchema.extend({
  triggerType: z.literal(TriggerTypeSchema.enum.INBOUND_EMAIL),
  state: InboundEmailTriggerStateSchema.optional(),
  input: InboundEmailTriggerInputSchema,
});
export type InboundEmailTrigger = z.infer<typeof InboundEmailTriggerSchema>;

export const ButtonClickTriggerSchema = BaseTriggerSchema.extend({
  triggerType: z.literal(TriggerTypeSchema.enum.BUTTON_CLICK),
});
export type ButtonClickTrigger = z.infer<typeof ButtonClickTriggerSchema>;

export const NativeTriggerSchema = z.discriminatedUnion('triggerType', [
  DatetimeFieldReachedTriggerSchema,
  FormSubmittedTriggerSchema,
  ManuallyTriggerSchema,
  MemberJoinedTriggerSchema,
  RecordCreatedTriggerSchema,
  RecordMatchTriggerSchema,
  SchedulerTriggerSchema,
  DummyTriggerSchema,
  HttpIfChangeTriggerSchema,
  WebhookReceivedTriggerSchema,
  InboundEmailTriggerSchema,
  ButtonClickTriggerSchema,
]);

export type Trigger = z.infer<typeof NativeTriggerSchema>;

export const TriggerSchema = z
  .object({
    triggerType: TriggerTypeSchema.or(z.string()),
  })
  .catchall(z.any());
export type AnyTrigger = z.infer<typeof TriggerSchema>;
