import { iStringSchema } from 'basenext/i18n';
import { BasePaginationInfoSchema } from 'basenext/pagination';
import { z } from 'zod';
import { ActionOutputSchema } from './bo-action-output';
import { BaseActionTestResultSchema } from './bo-action-state';
import { ActionSchema, ActionTypeSchema } from './bo-actions';
import { TriggerOutputSchema } from './bo-trigger-output';
import { BaseTriggerTestResultSchema } from './bo-trigger-state';
import { TriggerTypeSchema, NativeTriggerSchema } from './bo-triggers';
import { AutomationActionVOSchema } from './vo-action';
import { RenderOptionSchema } from '../system/render-option';

export const AutomationTriggerVOSchema = z
  .object({
    id: z.string(),
    type: TriggerTypeSchema,
    description: z.string().optional(),
    bo: NativeTriggerSchema,
    isVerified: z.boolean(),
  })
  .default({
    id: 'new',
    type: 'DUMMY_TRIGGER',
    bo: {
      triggerType: 'DUMMY_TRIGGER',
    },
    isVerified: false,
  });
export type AutomationTriggerVO = z.infer<typeof AutomationTriggerVOSchema>;

export const AutomationVOSchema = z
  .object({
    id: z.string(),
    name: z.string(),
    description: z.string().optional(),
    isActive: z.boolean(),
    actions: z.array(AutomationActionVOSchema),
    triggers: z.array(AutomationTriggerVOSchema),
    isVerified: z.boolean(),
  })
  .default({
    id: 'new',
    name: '',
    isActive: false,
    triggers: [],
    actions: [],
    isVerified: false,
  });
export type AutomationVO = z.infer<typeof AutomationVOSchema>;

export const AutomationRunHistoryStatusSchema = z.enum([
  'RUNNING',
  'DELAY',
  'SUCCESS',
  'FAILURE',
  'TIMEOUT',
  'CANCELLED',
]);
export type AutomationRunHistoryStatus = z.infer<typeof AutomationRunHistoryStatusSchema>;

export const AutomationRunHistoryVOSchema = z.object({
  id: z.string(),
  status: AutomationRunHistoryStatusSchema,
  startAt: z.string(),
  endAt: z.string().optional(),
  manual: z.boolean().optional(),
});
export type AutomationRunHistoryVO = z.infer<typeof AutomationRunHistoryVOSchema>;

export const AutomationRunHistoryPageVOSchema = BasePaginationInfoSchema.extend({
  data: z.array(AutomationRunHistoryVOSchema),
});
export type AutomationRunHistoryPageVO = z.infer<typeof AutomationRunHistoryPageVOSchema>;

export const TriggerRunDetailVOSchema = z
  .object({ id: z.string(), output: TriggerOutputSchema })
  .and(NativeTriggerSchema);
export type TriggerRunDetailVO = z.infer<typeof TriggerRunDetailVOSchema>;

const ItemActionRunResultVOSchema = z.object({
  id: z.string(),
  templateId: z.string().optional(),
  output: ActionOutputSchema,
});
export type ItemActionRunResultVO = z.infer<typeof ItemActionRunResultVOSchema>;
export const ActionRunDetailVOSchema = z
  .object({
    id: z.string(),
    output: ActionOutputSchema,
    // Execution results of nested sub-actions for each loop item. Two-dimensional array structure [items.length][actions.length]
    itemActionRunResults: z.array(z.array(ItemActionRunResultVOSchema)).optional(),
  })
  .and(ActionSchema);
export type ActionRunDetailVO = z.infer<typeof ActionRunDetailVOSchema>;

export const AutomationRunHistoryDataSchema = z.object({
  triggers: z.array(TriggerRunDetailVOSchema),
  actions: z.array(ActionRunDetailVOSchema),
});
export type AutomationRunHistoryData = z.infer<typeof AutomationRunHistoryDataSchema>;

export const AutomationRunHistoryDetailVOSchema = AutomationRunHistoryVOSchema.and(AutomationRunHistoryDataSchema);
export type AutomationRunHistoryDetailVO = z.infer<typeof AutomationRunHistoryDetailVOSchema>;

const TriggerFakeResultVOSchema = z
  .object({
    id: z.string(),
    templateId: z.string().optional(),
    type: TriggerTypeSchema,
    description: iStringSchema.optional(),
  })
  .and(BaseTriggerTestResultSchema);
const BaseActionFakeResultVOSchema = z
  .object({
    id: z.string(),
    templateId: z.string().optional(),
    type: ActionTypeSchema,
    description: iStringSchema.optional(),
  })
  .and(BaseActionTestResultSchema);
const ActionFakeResultVOSchema = z
  .object({
    // nested actions
    actions: z.array(BaseActionFakeResultVOSchema).optional(),
  })
  .and(BaseActionFakeResultVOSchema);
export type ActionFakeResultVO = z.infer<typeof ActionFakeResultVOSchema>;
const AutomationVariablesVOSchema = z.object({
  triggers: z.array(TriggerFakeResultVOSchema),
  actions: z.array(ActionFakeResultVOSchema),
});
export type AutomationVariablesVO = z.infer<typeof AutomationVariablesVOSchema>;

export const AutomationRenderOptsSchema = RenderOptionSchema.extend({
  templateNodeId: z.string().optional(),
});
export type AutomationRenderOpts = z.infer<typeof AutomationRenderOptsSchema>;
