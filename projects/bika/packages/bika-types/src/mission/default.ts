import { iStringParse, type Locale } from 'basenext/i18n';
import type { Mission } from './missions';
import { MissionVO } from './vo-mission';
// BO: Use getMissionsTypesConfig to get default values

// Convert BO to VO
export function defaultMissionVO(bo: Mission, locale: Locale): MissionVO {
  return {
    id: bo.id || bo.templateId!,
    spaceId: null!,
    assignees: [],
    name: iStringParse(bo.name, locale),
    description: iStringParse(bo.description, locale),
    templateId: bo.templateId,
    type: bo.type,
    status: 'PENDING',
    bo,
  };
}
