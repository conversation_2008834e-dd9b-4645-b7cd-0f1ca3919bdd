/* eslint-disable @typescript-eslint/no-use-before-define */
import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import {
  CommentRecordMissionStateSchema,
  CreateMultiRecordsMissionStateSchema,
  CreateRecordMissionStateSchema,
  InviteMemberMissionStateSchema,
  MissionStateSchema,
} from './mission-states';
import { MissionTypeSchema, REMINDER_LITERAL } from './mission-types';
// eslint-disable-next-line import/no-cycle
import { ActionSchema } from '../automation/bo-actions';
import { ReminderSchema } from '../reminder/reminder';
import { ExtractTypeWithProperty } from '../shared';
import {
  DatabaseBaseInputSchema,
  DatabaseOptionalViewBaseInputSchema,
  DatabaseRecordBaseInputSchema,
  DatabaseViewBaseInputSchema,
  FormBaseInputSchema,
  NodeBaseInputSchema,
} from '../shared/input';
import { BikaDueDateSchema } from '../system/datetime/due-date-bo';
import { ToSchema } from '../unit/bo-to';

const AssignTypeSchema = z
  .union([
    // Everyone shares the same mission, default
    z.literal('SHARE'),
    // Each person has their own mission, create new ones separately
    z.literal('DEDICATED'),
  ])
  .default('SHARE')
  .describe('How to assign the mission');
export type AssignType = z.infer<typeof AssignTypeSchema>;

const BaseMissionSchema = z.object({
  type: MissionTypeSchema,
  id: z.string().optional().describe('Mission ID'),
  templateId: z.string().optional().describe('Template ID'),
  name: iStringSchema.describe('Mission name'),
  description: iStringSchema.optional().describe('Mission description'),
  // content: z.string().optional().describe('Content to be displayed when the user opens the completion detail page'),
  canReject: z.boolean().optional().describe('Whether the mission can be rejected'),
  canCompleteManually: z.boolean().optional().describe('Whether the mission can be manually completed'),
  canTransfer: z.boolean().optional().describe('Whether the mission can be transferred'),
  dueDate: BikaDueDateSchema.optional().describe('Deadline, date range of the mission'),
  /**
   * Actions to execute after mission completion
   * The two templates currently used both use CREATE_MISSION Action, it is recommended to use SequenceMission directly
   * Or, consider using Complete Trigger/Action replacement in the future
   *
   * @deprecated
   */
  afterActions: z.lazy(() =>
    z.array(ActionSchema).optional().describe('Actions to be executed after successful completion of the mission'),
  ),

  // State, usually dynamic, not configured. The default state only has "completed"
  state: MissionStateSchema.optional(),

  // Force completion? When the interface opens, if there is a forced completion mission, it will pop up (if not completed); will pop up again after refresh
  // Force popup? Principle: When the red dot trpc returns, if there is a forced popup, it will pop up.
  forcePopup: z.boolean().default(false).optional().describe('Whether to force popup?'),

  // Mission assignment mode
  assignType: AssignTypeSchema.optional(),

  // Assign to whom?
  // Does each person have a new Mission, or does one Mission have multiple assignees?
  to: z.array(ToSchema).describe('Assign the mission to who assignees'),

  reminders: z
    .array(ReminderSchema)
    .optional()
    .describe('Extra reminders, early reminders to be sent if the mission is not completed'),
  buttonText: iStringSchema.optional().describe('Button text'),
});

// Quest (Recursive)
const BaseMissionQuestSchema = BaseMissionSchema.extend({
  type: z.literal('QUEST'),
});
type BaseMissionQuestWithRecursiveZodInput = z.input<typeof BaseMissionQuestSchema> & {
  missions: MissionZodInput[];
};
type BaseMissionQuestWithRecursiveZodOutput = z.output<typeof BaseMissionQuestSchema> & {
  missions: MissionZodOutput[];
};
export const QuestMissionSchema: z.ZodType<
  BaseMissionQuestWithRecursiveZodOutput,
  z.ZodTypeDef,
  BaseMissionQuestWithRecursiveZodInput
> = BaseMissionQuestSchema.extend({
  missions: z.lazy(() => MissionSchema.array()),
});
export type QuestMission = z.infer<typeof QuestMissionSchema>;

const BaseMissionSequenceSchema = BaseMissionSchema.extend({
  type: z.literal('SEQUENCE'),
});
type BaseMissionSequenceWithRecursiveZodInput = z.input<typeof BaseMissionSequenceSchema> & {
  missions: MissionZodInput[];
};
type BaseMissionSequenceWithRecursiveZodOutput = z.output<typeof BaseMissionSequenceSchema> & {
  missions: MissionZodOutput[];
};
export const SequenceMissionSchema: z.ZodType<
  BaseMissionSequenceWithRecursiveZodOutput,
  z.ZodTypeDef,
  BaseMissionSequenceWithRecursiveZodInput
> = BaseMissionSequenceSchema.extend({
  missions: z.lazy(() => MissionSchema.array().nonempty()),
});
export type SequenceMission = z.infer<typeof SequenceMissionSchema>;

// === meet ===
export const GoogleMeetMissionSchema = BaseMissionSchema.extend({
  type: z.literal('GOOGLE_MEET'),
});
export type GoogleMeetMission = z.infer<typeof GoogleMeetMissionSchema>;
export const VoovMeetMissionSchema = BaseMissionSchema.extend({
  type: z.literal('VOOV_MEET'),
  topic: z.string(),
  startTime: z.string().datetime(),
  duration: z.number(),
  password: z.string().optional(),
  meetingContent: z.string().optional(),
});
export type VoovMeetMission = z.infer<typeof VoovMeetMissionSchema>;
export const ZoomMeetMissionSchema = BaseMissionSchema.extend({
  type: z.literal('ZOOM_MEET'),
});
export type ZoomMeetMission = z.infer<typeof ZoomMeetMissionSchema>;

export const MissionCreateRecordSchema = BaseMissionSchema.extend({
  type: z.literal('CREATE_RECORD'),
  mirrorId: z.string().optional(),
  mirrorTemplateId: z.string().optional(),
  state: CreateRecordMissionStateSchema.optional(),
}).and(DatabaseOptionalViewBaseInputSchema);
export type MissionCreateRecord = z.infer<typeof MissionCreateRecordSchema>;

// Create multiple records, only ends after all are created
export const MissionCreateMultiRecordsSchema = BaseMissionSchema.extend({
  type: z.literal('CREATE_MULTI_RECORDS'),
  // How many to create?
  amount: z.number().int().positive(),
  state: CreateMultiRecordsMissionStateSchema.optional(),
}).and(DatabaseOptionalViewBaseInputSchema);
export type MissionCreateMultiRecords = z.infer<typeof MissionCreateMultiRecordsSchema>;

export const AICreateRecordsMissionSchema = BaseMissionSchema.extend({
  type: z.literal('AI_CREATE_RECORDS'),
  amount: z.number().int().positive(),
  state: CreateRecordMissionStateSchema.optional(),
}).and(DatabaseBaseInputSchema);
export type AICreateRecordsMission = z.infer<typeof AICreateRecordsMissionSchema>;

export const MissionCreateTaskSchema = BaseMissionSchema.extend({
  type: z.literal('CREATE_TASK'),
});
export type MissionCreateTask = z.infer<typeof MissionCreateTaskSchema>;

export const ReminderMissionSchema = BaseMissionSchema.extend({
  type: REMINDER_LITERAL,
});
export type ReminderMission = z.infer<typeof ReminderMissionSchema>;

export const MissionSubmitFormSchema = BaseMissionSchema.extend({
  type: z.literal('SUBMIT_FORM'),
}).and(FormBaseInputSchema);
export type MissionSubmitForm = z.infer<typeof MissionSubmitFormSchema>;

export const MissionSubmitMultipleFormSchema = BaseMissionSchema.extend({
  type: z.literal('SUBMIT_MULTIPLE_FORM'),
  formIds: z.array(z.string()).optional(),
  formTemplateIds: z.array(z.string()).optional(),
});
export type MissionSubmitMultipleForm = z.infer<typeof MissionSubmitMultipleFormSchema>;

export const MissionUpdateRecordSchema = BaseMissionSchema.extend({
  type: z.literal('UPDATE_RECORD'),
}).and(DatabaseRecordBaseInputSchema);
export type MissionUpdateRecord = z.infer<typeof MissionUpdateRecordSchema>;

export const MissionEnterViewSchema = BaseMissionSchema.extend({
  type: z.literal('ENTER_VIEW'),
}).and(DatabaseViewBaseInputSchema);
export type MissionEnterView = z.infer<typeof MissionEnterViewSchema>;

export const MissionApprovalSchema = BaseMissionSchema.extend({
  type: z.literal('APPROVAL'),
});
export type MissionApproval = z.infer<typeof MissionApprovalSchema>;

export const MissionRedirectSpaceNodeSchema = BaseMissionSchema.extend({
  type: z.literal('REDIRECT_SPACE_NODE').describe('Redirect to a space node'),
  wizardGuideId: z.string().optional(),
}).and(NodeBaseInputSchema);
export type RedirectSpaceNodeMission = z.infer<typeof MissionRedirectSpaceNodeSchema>;

export const UIGoToTargetSchema = z.enum(['TODO']);
export const MissionUILauncherSchema = BaseMissionSchema.extend({
  type: z.literal('UI_LAUNCHER'),
  target: UIGoToTargetSchema,
});
export type MissionUILauncher = z.infer<typeof MissionUILauncherSchema>;

export const CommentRecordMissionSchema = BaseMissionSchema.extend({
  type: z.literal('COMMENT_RECORD'),
  state: CommentRecordMissionStateSchema.optional(),
}).and(DatabaseRecordBaseInputSchema);
export type CommentRecordMission = z.infer<typeof CommentRecordMissionSchema>;

export const ReviewRecordMissionSchema = BaseMissionSchema.extend({
  type: z.literal('REVIEW_RECORD'),
}).and(DatabaseRecordBaseInputSchema);
export type ReviewRecordMission = z.infer<typeof ReviewRecordMissionSchema>;

export const MissionInviteMemberSchema = BaseMissionSchema.extend({
  type: z.literal('INVITE_MEMBER'),
  roleId: z.string().optional(),
  roleTemplateId: z.string().optional(),
  state: InviteMemberMissionStateSchema.optional(),
});
export type MissionInviteMember = z.infer<typeof MissionInviteMemberSchema>;

// export const MissionSetSpaceNewMemberJoinAnnouncementSchema = BaseMissionSchema.extend({
//   type: z.literal('SET_SPACE_NEW_MEMBER_JOIN_ANNOUNCEMENT'),
// });
// export type MissionSetSpaceNewMemberJoinAnnouncement = z.infer<typeof MissionSetSpaceNewMemberJoinAnnouncementSchema>;

export const MissionSetSpaceNameSchema = BaseMissionSchema.extend({
  type: z.literal('SET_SPACE_NAME'),
});
export type MissionSetSpaceName = z.infer<typeof MissionSetSpaceNameSchema>;

const RedirectActionSchema = z.union([
  z.object({
    type: z.literal('SPACE_NODE').describe('Redirect to a space node'),
    nodeId: z.string().optional(),
    nodeTemplateId: z.string().optional(),
  }),
  z.object({
    type: z.literal('MY_MISSIONS').describe('Redirect to To-do page'),
  }),
  z.object({
    type: z.literal('MY_REPORTS').describe('Redirect to Report page'),
  }),
]);

/**
 * Read a markdown segment
 */
export const MissionReadMarkdownSchema = BaseMissionSchema.extend({
  type: z.literal('READ_MARKDOWN'),
  content: iStringSchema,
  redirect: RedirectActionSchema.optional(),
});
export type MissionReadMarkdown = z.infer<typeof MissionReadMarkdownSchema>;

export const MissionReminderSchema = BaseMissionSchema.extend({
  type: REMINDER_LITERAL,
  // When reminding, dueDate is required
  dueDate: BikaDueDateSchema.describe('Deadline, date range of the mission'),
});

export const InstallTemplateMissionSchema = BaseMissionSchema.extend({
  type: z.literal('INSTALL_TEMPLATE'),
  templateId: z.string(),
});
export type InstallTemplateMission = z.infer<typeof InstallTemplateMissionSchema>;

/**
 * Read template README, pass in template ID
 */
export const MissionReadTemplateSchema = BaseMissionSchema.extend({
  type: z.literal('READ_TEMPLATE_README'),
  templateId: z.string(),
  time: z.number().int().positive().describe('time seconds').optional(),
  beforeText: z
    .object({
      title: iStringSchema.optional(),
      description: iStringSchema.optional(),
    })
    .optional(),
  afterText: z
    .object({
      title: iStringSchema.optional(),
      description: iStringSchema.optional(),
    })
    .optional(),
  // Path to jump to after clicking "Read Complete"
  redirect: RedirectActionSchema.optional(),
  wizardGuideId: z.string().optional(),
});
export type ReadTemplateReadmeMission = z.infer<typeof MissionReadTemplateSchema>;

export const MissionSchema = z.union([
  // record
  AICreateRecordsMissionSchema,
  MissionCreateRecordSchema,
  MissionCreateMultiRecordsSchema,
  CommentRecordMissionSchema,
  ReviewRecordMissionSchema,
  MissionUpdateRecordSchema,
  // template
  InstallTemplateMissionSchema,
  MissionReadTemplateSchema,
  // meet
  GoogleMeetMissionSchema,
  VoovMeetMissionSchema,
  ZoomMeetMissionSchema,

  MissionSetSpaceNameSchema,
  MissionReadMarkdownSchema,
  MissionInviteMemberSchema,
  MissionApprovalSchema,
  MissionSubmitFormSchema,
  MissionSubmitMultipleFormSchema,
  MissionCreateTaskSchema,
  MissionUILauncherSchema,
  MissionRedirectSpaceNodeSchema,
  MissionEnterViewSchema,
  MissionReminderSchema,
  ReminderMissionSchema,

  // Recursive
  QuestMissionSchema,
  SequenceMissionSchema,
]);
export type Mission = z.infer<typeof MissionSchema>;
export type MissionZodInput = z.input<typeof MissionSchema>;
export type MissionZodOutput = z.output<typeof MissionSchema>;

const MissionWithDatabaseSchema = z.intersection(MissionSchema, DatabaseBaseInputSchema);
export type MissionWithDatabase = z.infer<typeof MissionWithDatabaseSchema>;

const MissionWithRecordSchema = z.intersection(MissionSchema, DatabaseRecordBaseInputSchema);
export type MissionWithRecord = z.infer<typeof MissionWithRecordSchema>;

type MissionWithNodeSchema = ExtractTypeWithProperty<
  (typeof MissionSchema)['_def']['options'][number],
  'nodeTemplateId'
>;
export type MissionWithNode = z.infer<MissionWithNodeSchema>;
