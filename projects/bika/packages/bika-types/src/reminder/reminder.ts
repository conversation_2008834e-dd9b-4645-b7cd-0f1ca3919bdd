import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import { SchedulerSchema } from '../system/datetime';
import { ToSchema } from '../unit/bo-to';
// import { TimeZoneSchema } from '../system/scheduler';

export const ReminderSchema = z.object({
  name: iStringSchema.describe('Reminder name'),
  description: iStringSchema.optional(),
  // Specific time, time range, recurrence, and timezone are all configured inside
  scheduler: SchedulerSchema,
  // datetime: z.date().optional(),
  // datetimeEnd: z.date().optional(),
  // timezone: TimeZoneSchema,
  // cron: z.string().regex(/^(\S+) (\S+) (\S+) (\S+) (\S+)$/).describe('cron expression').optional(),
  // after: z.string().optional(),
  to: z.array(ToSchema),
});

export type Reminder = z.infer<typeof ReminderSchema>;
