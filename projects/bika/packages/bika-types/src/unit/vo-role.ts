import { iStringSchema } from 'basenext/i18n';
import { BasePaginationInfoSchema } from 'basenext/pagination';
import { z } from 'zod';
import { UnitBaseSchema, RoleUnitType } from './vo-base';

export const MemberActionPermission = z.literal('member');
export const TeamActionPermission = z.literal('team');
export const RoleActionPermission = z.literal('role');
export const ResourceActionPermission = z.literal('resource');
export const TemplateActionPermission = z.literal('template');
export const IntegrationActionPermission = z.literal('integration');
export const SecurityActionPermission = z.literal('security');
export const PublishActionPermission = z.literal('publish');

export const RolePermissionSchema = z.union([
  MemberActionPermission,
  TeamActionPermission,
  RoleActionPermission,
  ResourceActionPermission,
  IntegrationActionPermission,
  TemplateActionPermission,
  SecurityActionPermission,
  PublishActionPermission,
]);

export type RolePermission = z.infer<typeof RolePermissionSchema>;

export const RoleSettingSchema = z.object({
  permissions: z.array(RolePermissionSchema),
});

export type RoleSettingVO = z.infer<typeof RoleSettingSchema>;

export const RoleVOSchema = UnitBaseSchema.extend({
  type: RoleUnitType,
  sequence: z.number().optional(),
  manageSpace: z.boolean(),
  permissions: z.array(RolePermissionSchema).optional(),
  memberCount: z.number().optional(),
});
export type RoleVO = z.infer<typeof RoleVOSchema>;

export const RolePaginationVOSchema = BasePaginationInfoSchema.extend({
  data: z.array(RoleVOSchema),
});

export type RolePaginationVO = z.infer<typeof RolePaginationVOSchema>;

export const AdminPermissionSchema = z.object({
  id: RolePermissionSchema,
  name: iStringSchema,
  description: iStringSchema,
});

export const AdminGroupPermissionSchema = z.object({
  name: iStringSchema,
  description: iStringSchema,
  permissions: z.array(AdminPermissionSchema),
});

export type AdminGroupPermission = z.infer<typeof AdminGroupPermissionSchema>;
