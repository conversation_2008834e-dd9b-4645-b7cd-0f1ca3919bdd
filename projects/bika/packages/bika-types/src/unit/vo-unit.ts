import { BasePaginationInfoSchema } from 'basenext/pagination';
import { z } from 'zod';
import { MemberVOSchema } from './vo-member';
import { RoleVOSchema } from './vo-role';
import { TeamVOSchema } from './vo-team';
import { RenderOptionSchema } from '../system/render-option';

export const UnitVOSchema = z.union([TeamVOSchema, MemberVOSchema, RoleVOSchema]);
export type UnitVO = z.infer<typeof UnitVOSchema>;

export const UnitPaginationVOSchema = BasePaginationInfoSchema.extend({
  data: z.array(UnitVOSchema),
});

export type UnitPaginationVO = z.infer<typeof UnitPaginationVOSchema>;

export const TeamSubListPageItemVOSchema = z.union([MemberVOSchema, TeamVOSchema]);

export type TeamSubListPageItemVO = z.infer<typeof TeamSubListPageItemVOSchema>;

export const TeamSubListPageVOSchema = BasePaginationInfoSchema.extend({
  data: z.array(TeamSubListPageItemVOSchema),
});

export type TeamSubListPageVO = z.infer<typeof TeamSubListPageVOSchema>;

export const UnitRenderOptsSchema = RenderOptionSchema.extend({
  // Whether to load more details, by default only load basic information
  withDetail: z.boolean().optional(),
});

export type UnitRenderOpts = z.infer<typeof UnitRenderOptsSchema>;
