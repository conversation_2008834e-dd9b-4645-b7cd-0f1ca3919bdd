import { PaginationSchema } from 'basenext/pagination';
import { z } from 'zod';
import { RoleUnitType, TeamUnitType } from './vo-base';

export const UnitSearchSchema = z.object({
  name: z.string().optional(),
  ids: z.array(z.string()).optional(),
  isGuest: z.boolean().optional(),
});

export type UnitSearch = z.infer<typeof UnitSearchSchema>;

const UnitListBaseSchema = UnitSearchSchema.extend({
  spaceId: z.string(),
  sort: z
    .string()
    .refine((value) => ['name', 'createdAt'].includes(value), { message: 'Invalid sort field' })
    .default('createdAt'),
  direction: z
    .string()
    .optional()
    .transform((value) => (value === 'asc' ? value : 'desc')),
});

export const UnitListDTOSchema = UnitListBaseSchema.merge(PaginationSchema);

export type UnitListDTO = z.infer<typeof UnitListDTOSchema>;

export const OutgoingContactListDTOSchema = z.object({
  name: z.string().optional(),
  type: z.enum([TeamUnitType.value, RoleUnitType.value]),
  pageNo: z.number().default(1),
  pageSize: z.number().default(50),
  spaceId: z.string(),
  teamId: z.string().optional(),
});

export type OutgoingContactListDTO = z.infer<typeof OutgoingContactListDTOSchema>;

export const InviteOutgoingContactDTOSchema = z.object({
  spaceId: z.string(),
  teamIds: z.array(z.string()),
  userIds: z.array(z.string()),
  roleIds: z.array(z.string()),
  nodeId: z.string(),
});

export type InviteOutgoingContactDTO = z.infer<typeof InviteOutgoingContactDTOSchema>;
