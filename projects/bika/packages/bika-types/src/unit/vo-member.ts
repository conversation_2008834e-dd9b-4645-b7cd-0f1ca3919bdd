import { AvatarLogoSchema } from 'basenext/avatar';
import { BasePaginationInfoSchema } from 'basenext/pagination';
import { z } from 'zod';
import { UnitMemberRelationTypeSchema } from './bo-unit';
import { MemberUnitType, UnitBaseSchema } from './vo-base';
import { RoleVOSchema } from './vo-role';
import { TeamVOSchema } from './vo-team';

export const MemberVOSchema = UnitBaseSchema.extend({
  type: MemberUnitType,
  userId: z.string(),
  email: z.string().nullable(),
  avatar: AvatarLogoSchema.optional(),
  nickName: z.string().optional(),
  isGuest: z.boolean(),
  isSpaceOwner: z.boolean().optional(),
  teams: z.array(TeamVOSchema).optional(),
  roles: z.array(RoleVOSchema).optional(),
  relationType: UnitMemberRelationTypeSchema,
});
export type MemberVO = z.infer<typeof MemberVOSchema>;

export const MemberPaginationVOSchema = BasePaginationInfoSchema.extend({
  data: z.array(MemberVOSchema),
});

export type MemberPaginationVO = z.infer<typeof MemberPaginationVOSchema>;
