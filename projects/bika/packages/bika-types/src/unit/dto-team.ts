import { PaginationSchema } from 'basenext/pagination';
import { z } from 'zod';

const BaseSchema = z.object({
  spaceId: z.string(),
});

export const TeamRootDTOSchema = BaseSchema.extend({
  isGuest: z.boolean().optional().default(false),
});

export type TeamRootDTO = z.infer<typeof TeamRootDTOSchema>;

export const TeamSubListBaseSchema = BaseSchema.extend({
  teamId: z.string(),
});

export const TeamSubListDTOSchema = TeamSubListBaseSchema.merge(PaginationSchema);

export type TeamSubListDTO = z.infer<typeof TeamSubListDTOSchema>;

export const TeamInfoDTOSchema = BaseSchema.extend({
  id: z.string(),
});

export const TeamInfoListDTOSchema = BaseSchema.extend({
  id: z.string().array(),
});

export type TeamListInfoDTO = z.infer<typeof TeamInfoListDTOSchema>;

export type TeamInfoDTO = z.infer<typeof TeamInfoDTOSchema>;

export const TeamCreateDTOSchema = BaseSchema.extend({
  parentTeamId: z.string().describe('Parent Team Id, for openapi, empty string means to create a root team'),
  name: z.string(),
});

export type TeamCreateDTO = z.infer<typeof TeamCreateDTOSchema>;

export const TeamUpdateDTOSchema = BaseSchema.extend({
  id: z.string(),
  name: z.string(),
});

export type TeamUpdateDTO = z.infer<typeof TeamUpdateDTOSchema>;

export const TeamAddMembersDTOSchema = BaseSchema.extend({
  id: z.string(),
  memberIds: z.array(z.string()),
});

export type TeamAddMembersDTO = z.infer<typeof TeamAddMembersDTOSchema>;

export const TeamRemoveMembersDTOSchema = BaseSchema.extend({
  id: z.string(),
  memberIds: z.array(z.string()),
});

export type TeamRemoveMembersDTO = z.infer<typeof TeamRemoveMembersDTOSchema>;
