import { PaginationSchema } from 'basenext/pagination';
import { z } from 'zod';

const BaseSchema = z.object({
  spaceId: z.string(),
});

const MemberListBaseSchema = z.object({
  name: z.string().optional().describe('Member name'),
  ids: z.string().array().optional(),
  detail: z.boolean().default(false).describe('Whether to load more details'),
  sort: z
    .string()
    .refine((value) => ['name', 'createdAt'].includes(value), { message: 'Invalid sort field' })
    .default('createdAt'),
  direction: z
    .string()
    .optional()
    .transform((value) => (value === 'asc' ? value : 'desc')),
  nodeId: z.string().optional(),
});

export const MemberListDTOSchema = BaseSchema.merge(MemberListBaseSchema).merge(PaginationSchema);

export type MemberListDTO = z.infer<typeof MemberListDTOSchema>;

export const MemberInfoDTOSchema = BaseSchema.extend({
  id: z.string(),
  withDetail: z.boolean().optional().default(false),
});

export type MemberInfoDTO = z.infer<typeof MemberInfoDTOSchema>;

export const MemberUpdateSchema = z.object({
  name: z.string().optional().describe('Member name'),
  teamIds: z.string().array().optional().describe('List of team IDs'),
  roleIds: z.string().array().optional().describe('List of role IDs'),
});

export type MemberUpdate = z.infer<typeof MemberUpdateSchema>;

export const MemberUpdateDTOSchema = BaseSchema.extend({
  id: z.string(),
  data: MemberUpdateSchema,
});

export type MemberUpdateDTO = z.infer<typeof MemberUpdateDTOSchema>;

export const GuestGetNodesSchema = z.object({
  spaceId: z.string(),
});

export type GuestGetNodes = z.infer<typeof GuestGetNodesSchema>;
