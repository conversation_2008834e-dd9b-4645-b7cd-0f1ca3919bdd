import { PaginationSchema } from 'basenext/pagination';
import { z } from 'zod';
import { RolePermissionSchema } from './vo-role';

const BaseSchema = z.object({
  spaceId: z.string(),
});

const RoleListBaseSchema = z.object({
  name: z.string().optional(),
  sort: z
    .string()
    .refine((value) => ['name', 'createdAt'].includes(value), { message: 'Invalid sort field' })
    .default('createdAt'),
  direction: z
    .string()
    .optional()
    .transform((value) => (value === 'asc' ? value : 'desc')),
});

export const RoleListSchema = BaseSchema.merge(RoleListBaseSchema).merge(PaginationSchema);

export type RoleListReq = z.infer<typeof RoleListSchema>;

export const RoleInfoSchema = BaseSchema.extend({
  id: z.string(),
});

export type RoleInfoReq = z.infer<typeof RoleInfoSchema>;

export const RoleCreateSchema = BaseSchema.extend({
  id: z.string().optional(),
  name: z.string(),
  manageSpace: z.boolean().default(false),
  permissions: z.array(RolePermissionSchema).optional(),
});

export type RoleCreateReq = z.infer<typeof RoleCreateSchema>;

export const RoleUpdateSchema = BaseSchema.extend({
  id: z.string(),
  name: z.string().optional(),
  manageSpace: z.boolean().optional(),
  permissions: z.array(RolePermissionSchema).optional(),
});

export type RoleUpdateReq = z.infer<typeof RoleUpdateSchema>;

export const RoleDeleteSchema = BaseSchema.extend({
  id: z.string(),
});

export type RoleDeleteReq = z.infer<typeof RoleDeleteSchema>;

export const RoleMembersSchema = BaseSchema.extend({
  id: z.string(),
});

export type RoleMembersReq = z.infer<typeof RoleMembersSchema>;

export const RoleAddMembersSchema = BaseSchema.extend({
  id: z.string(),
  memberIds: z.array(z.string()),
});

export type RoleAddMembersReq = z.infer<typeof RoleAddMembersSchema>;

export const RoleRemoveMemberSchema = BaseSchema.extend({
  id: z.string(),
  memberId: z.string(),
});

export type RoleRemoveMemberReq = z.infer<typeof RoleRemoveMemberSchema>;
