import { BasePaginationInfoSchema } from 'basenext/pagination';
import { z } from 'zod';
import { TeamUnitType, UnitBaseSchema } from './vo-base';

export const TeamBaseVOSchema = UnitBaseSchema.extend({
  parentId: z.string().optional(),
  memberCount: z.number().optional(),
  path: z.string().optional().describe('Department hierarchy path'),
  isGuest: z.boolean(),
  type: TeamUnitType,
});

export type TeamVO = z.infer<typeof TeamBaseVOSchema> & {
  children?: TeamVO[];
};

export type TeamVOZodInput = z.input<typeof TeamBaseVOSchema> & {
  children?: TeamVOZodInput[];
};

export type TeamVOZodOutput = z.output<typeof TeamBaseVOSchema> & {
  children?: TeamVOZodOutput[];
};

export const TeamVOSchema: z.ZodType<TeamVOZodInput, z.ZodTypeDef, TeamVOZodOutput> = TeamBaseVOSchema.extend({
  children: z.lazy(() => TeamVOSchema.array().optional()),
});

export const TeamPaginationVOSchema = BasePaginationInfoSchema.extend({
  data: TeamBaseVOSchema.array(),
});

export type TeamPaginationVO = z.infer<typeof TeamPaginationVOSchema>;
