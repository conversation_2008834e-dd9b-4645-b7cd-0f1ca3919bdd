import { AvatarLogoSchema } from 'basenext/avatar';
import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import {
  DatabaseFieldTypeSchema,
  Attachment,
  AutoNumber,
  CheckBox,
  CreatedTime,
  Currency,
  DateTime,
  Email,
  Link,
  LongText,
  Lookup,
  Member,
  CreatedBy,
  ModifiedBy,
  ModifiedTime,
  MultiSelect,
  NumberField,
  OneWayLink,
  Percent,
  Phone,
  Rating,
  SingleSelect,
  SingleText,
  URL,
  Formula,
  Cascader,
  Button,
  WorkDoc,
  AIText,
  AIVoice,
  AIPhoto,
  AIVideo,
  CutVideo,
  JSONField,
  Video,
  Voice,
  Photo,
  API,
  DateRange,
} from './bo-field-type';
import { FieldEditPrivilegeSchema } from './bo-privilege';
import { ViewFilterSchema, ViewSortArraySchema } from './bo-view';
import { AIModelSelectBOSchema } from '../ai/bo-ai-model-select';
import { AIModelProviderIntegrationSchema, CustomAIProviderIntegrationSchema } from '../integration/bo-ai-integrations';
import { DateTimeFieldPropertySchema } from '../system/datetime/datetime-bo';

export * from './bo-field-type';

export const SingleTextFieldPropertySchema = z
  .object({
    defaultValue: z.string().optional(),
  })
  .nullish();

export type SingleTextFieldProperty = z.infer<typeof SingleTextFieldPropertySchema>;

export const LongTextFieldPropertySchema = z
  .object({
    // active rich text editor state
    richText: z.boolean(),
  })
  .nullish();

export type LongTextFieldProperty = z.infer<typeof LongTextFieldPropertySchema>;

export const SymbolAlignSchema = z.enum([
  /** default */
  'default',
  /** left */
  'left',
  /** to the right */
  'right',
]);
export type SymbolAlign = z.infer<typeof SymbolAlignSchema>;

// Single/Multi Select color options
const COLOR_OPTIONS = [
  'deepPurple',
  'indigo',
  'blue',
  'teal',
  'green',
  'yellow',
  'orange',
  'tangerine',
  'pink',
  'red',
  'brown',
  'gray',
] as const;
export const SelectOptionColorSchema = z.enum(COLOR_OPTIONS);
export type SelectOptionColor = z.infer<typeof SelectOptionColorSchema>;
export const SelectOptionColors = SelectOptionColorSchema.options;
/**
 * Randomly select a color from the color options
 * @param {string[]} excludeColors - Array of colors to exclude from the selection
 * @param {boolean} allowReset - Whether to allow resetting the color pool when exhausted
 * @returns {string} - A randomly selected color
 */
export const getRandomColor = (excludeColors: string[] = [], allowReset: boolean = true): string => {
  // Convert the exclusion array to a Set to improve lookup performance
  const excludeSet = new Set(excludeColors);

  // Dynamically generate the candidate pool (excluding used colors)
  const candidatePool = SelectOptionColors.filter((color) => !excludeSet.has(color));

  // Handling logic when the candidate pool is exhausted
  if (candidatePool.length === 0) {
    if (allowReset) {
      console.warn('The color pool is exhausted, resetting the candidate pool');
      return SelectOptionColors[Math.floor(Math.random() * SelectOptionColors.length)];
    }
    throw new Error('No available colors and reset is not allowed');
  }

  // Randomly select from the candidate pool
  return candidatePool[Math.floor(Math.random() * candidatePool.length)];
};
export const DatabaseFieldConfigSelectOptionSchema = z.object({
  templateId: z.string().nullish(),
  id: z.string().optional(),
  name: iStringSchema,
  color: z.string().optional(),
});
export type DatabaseFieldConfigSelectOption = z.infer<typeof DatabaseFieldConfigSelectOptionSchema>;

export const BaseSelectFieldPropertySchema = z.object({
  options: z.array(DatabaseFieldConfigSelectOptionSchema),
});
export type BaseSelectFieldProperty = z.infer<typeof BaseSelectFieldPropertySchema>;

export const SingleSelectFieldPropertySchema = BaseSelectFieldPropertySchema.extend({
  defaultValue: z.string().optional(),
});
export type SingleSelectFieldProperty = z.infer<typeof SingleSelectFieldPropertySchema>;

export const MultiSelectFieldPropertySchema = BaseSelectFieldPropertySchema.extend({
  defaultValue: z
    .string()
    .array()
    // For compatibility with old structures, mainly when Type Check encounters exceptions
    .or(z.string())
    .optional(),
});
export type MultiSelectFieldProperty = z.infer<typeof MultiSelectFieldPropertySchema>;
export const NumberFieldPropertySchema = z
  .object({
    // precision
    precision: z.number().optional(),
    // default value
    defaultValue: z.string().optional(),
    // thousands separator
    commaStyle: z.string().optional(),
    // symbol
    symbol: z.string().optional(),
    // symbol alignment
    symbolAlign: SymbolAlignSchema.optional(),
  })
  .default({
    precision: 0,
    commaStyle: 'thousand',
    symbol: '',
    symbolAlign: 'default',
  });

export type NumberFieldProperty = z.infer<typeof NumberFieldPropertySchema>;

export const RatingFieldPropertySchema = z.object({
  icon: AvatarLogoSchema,
  max: z.number(),
});

export type RatingFieldProperty = z.infer<typeof RatingFieldPropertySchema>;

export const MemberFieldPropertySchema = z.object({
  many: z.boolean().optional(),
  notifyMentioned: z.boolean().optional(),
});
export type MemberFieldProperty = z.infer<typeof MemberFieldPropertySchema>;

export const DateRangeFieldPropertySchema = DateTimeFieldPropertySchema.omit({ autofill: true });
export type DateRangeFieldProperty = z.infer<typeof DateRangeFieldPropertySchema>;

export const AutoNumberFieldPropertySchema = z.object({
  nextId: z.number().optional(),
});

export const OneWayLinkFieldPropertySchema = z
  .object({
    foreignDatabaseId: z.string().optional(),
    foreignDatabaseTemplateId: z.string().optional(),
  })
  .refine((data) => 'foreignDatabaseId' in data || 'foreignDatabaseTemplateId' in data, {
    message: 'foreignDatabaseId or foreignDatabaseTemplateId is required',
  });
export type OneWayLinkFieldProperty = z.infer<typeof OneWayLinkFieldPropertySchema>;

export const LinkFieldPropertySchema = z
  .object({
    foreignDatabaseId: z.string().optional(),
    foreignDatabaseTemplateId: z.string().optional(),
    brotherFieldId: z.string().optional(),
    brotherFieldTemplateId: z.string().optional(),
  })
  .refine(
    (data) =>
      data.foreignDatabaseId ||
      data.foreignDatabaseTemplateId ||
      (!data.foreignDatabaseId && !data.foreignDatabaseTemplateId), // Both can be empty
    {
      message: 'foreignDatabaseId or foreignDatabaseTemplateId is required',
    },
  );

export type LinkFieldProperty = z.infer<typeof LinkFieldPropertySchema>;

export const LookupDataTypeSchema = z.union([
  z.literal('STRING'),
  z.literal('NUMBER'),
  z.literal('BOOLEAN'),
  z.literal('DATETIME'),
]);
export type LookupDataType = z.infer<typeof LookupDataTypeSchema>;

// Aggregation calculation methods
export const RollUpFuncTypeSchema = z.enum([
  // Reference original value
  'VALUES',

  // Supported for number types
  'AVERAGE',
  'COUNT',
  'COUNTA',
  'COUNTALL',
  'SUM',
  'MIN',
  'MAX',

  'AND',
  'OR',
  'XOR',

  // Currently processed in lookup
  // // will be converted to string
  'CONCATENATE',
  'ARRAYJOIN',
  // Array processing functions
  'ARRAYUNIQUE',
  'ARRAYCOMPACT',
]);
export type RollUpFuncType = z.infer<typeof RollUpFuncTypeSchema>;

const LookUpLimitTypeSchema = z.enum(['ALL', 'FIRST']);
export type LookUpLimitType = z.infer<typeof LookUpLimitTypeSchema>;

// TODO issue
const LookupFieldFormattingPropertySchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('NUMBER'),
    property: NumberFieldPropertySchema,
  }),
  z.object({
    type: z.literal('CURRENCY'),
    property: NumberFieldPropertySchema,
  }),
  z.object({
    type: z.literal('PERCENT'),
    property: NumberFieldPropertySchema,
  }),
  z.object({
    type: z.literal('DATETIME'),
    property: DateTimeFieldPropertySchema,
  }),
]);

export type LookupFieldFormattingProperty = z.infer<typeof LookupFieldFormattingPropertySchema>;

export const LookupFieldPropertySchema = z.object({
  // The ID of the table where the referenced associated field is located
  databaseId: z.string().optional(), // linked database id redundant field
  databaseTemplateId: z.string().optional(),
  // The associated field of the current table
  relatedLinkFieldId: z.string().optional(),
  relatedLinkFieldTemplateId: z.string().optional(),
  // The field ID of the referenced associated field in the target table
  lookupTargetFieldId: z.string().optional(),
  lookupTargetFieldTemplateId: z.string().optional(),

  // Cache the type of the target field
  lookupTargetFieldType: DatabaseFieldTypeSchema.optional(),
  // Inferred before saving
  // Only valid when the target field is Formula or RollUp calculation is enabled
  dataType: LookupDataTypeSchema.optional(),

  // Filter and sort for lookup
  filterInfo: ViewFilterSchema.optional(),
  sortInfo: ViewSortArraySchema.optional(),

  // Default is ALL
  lookUpLimit: LookUpLimitTypeSchema.optional(),
  // Default is VALUES
  rollUpType: RollUpFuncTypeSchema.optional(),

  formatting: LookupFieldFormattingPropertySchema.optional(),

  // rollUpRecordRule: z.string().optional(),
  // rollupMethod: z.string().optional(),
});
// After the associated field is converted or deleted, relatedLinkFieldId/lookupTargetFieldId will be cleared, schema cannot be parsed
// .refine((data) => 'relatedLinkFieldId' in data || 'relatedLinkFieldTemplateId' in data, {
//   message: 'relatedLinkFieldId or relatedLinkFieldTemplateId is required',
// })
// .refine((data) => 'lookupTargetFieldId' in data || 'lookupTargetFieldTemplateId' in data, {
//   message: 'lookupTargetFieldId or lookupTargetFieldTemplateId is required',
// });

export type LookupFieldProperty = z.infer<typeof LookupFieldPropertySchema>;

export const FormulaFieldPropertySchema = z
  .object({
    // Required before saving
    expression: z.string().optional(),
    expressionTemplate: z.string().optional(),
  })
  .refine((data) => 'expression' in data || 'expressionTemplate' in data, {
    message: 'expression or expressionTemplate is required',
  });

export type FormulaFieldProperty = z.infer<typeof FormulaFieldPropertySchema>;

export const AITextPropertyTypeSchema = z.enum(['CUSTOM', 'INTEGRATION']);
export type AITextPropertyType = z.infer<typeof AITextPropertyTypeSchema>;

const baseAITextPropertySchema = z.object({
  prompt: z.string().optional(),
  autoUpdate: z.boolean().optional(),
});

export const AITextIntegrationPropertySchema = baseAITextPropertySchema.extend({
  type: z.literal(AITextPropertyTypeSchema.Enum.INTEGRATION),
  integrationId: z.string(),
  model: z.string().optional(),
});
export type AITextIntegrationProperty = z.infer<typeof AITextIntegrationPropertySchema>;

export const AITextCustomPropertySchema = baseAITextPropertySchema.extend({
  type: z.literal(AITextPropertyTypeSchema.Enum.CUSTOM),
  aiModel: CustomAIProviderIntegrationSchema,
  model: z.string().optional(),
});
export type AITextCustomProperty = z.infer<typeof AITextCustomPropertySchema>;

// AI Text Field, deprecated, contains 'type'. The new version does not include 'type'.
export const AITextFieldPropertyDeprecatedSchema = z.discriminatedUnion('type', [
  AITextIntegrationPropertySchema,
  AITextCustomPropertySchema,
]);
export type AITextFieldPropertyDeprecated = z.infer<typeof AITextFieldPropertyDeprecatedSchema>;

export const AITextFieldPropertySchema = baseAITextPropertySchema.extend({
  model: AIModelSelectBOSchema.optional(),
  modelId: z.string().optional(),
});

export type AITextFieldProperty = z.infer<typeof AITextFieldPropertySchema>;

export const DatabaseFieldPropertySchema = z.union([
  SingleTextFieldPropertySchema,
  LongTextFieldPropertySchema,
  AutoNumberFieldPropertySchema,
  SingleSelectFieldPropertySchema,
  MultiSelectFieldPropertySchema,
  LinkFieldPropertySchema,
  OneWayLinkFieldPropertySchema,
  FormulaFieldPropertySchema,
  LookupFieldPropertySchema,
  NumberFieldPropertySchema,
  MemberFieldPropertySchema,
  DateTimeFieldPropertySchema,
  DateRangeFieldPropertySchema,
  AITextFieldPropertySchema,
]);
export type DatabaseFieldProperty = z.infer<typeof DatabaseFieldPropertySchema>;

const FieldValidatorBase = z.object({
  timing: z.enum(['ON_SUBMIT', 'ON_BLUR']),
  level: z.enum(['ERROR', 'WARNING']), // "warning can pass, but there will be a prompt (report or notification)"
});

// Execute a TypeScript script to validate, true passes, false fails
export const ScriptValidator = FieldValidatorBase.extend({
  type: z.literal('SCRIPT'),
  script: z.string(),
});

// Used together with contents/config/server/prompts/AIPromptValidator.ts for validation
export const AIPromptValidator = FieldValidatorBase.extend({
  type: z.literal('AI_PROMPT'),
  prompt: z.string(),
  aiModel: z.string().or(AIModelProviderIntegrationSchema).optional(),
});

/**
 * Field Validator
 */
export const ValidatorSchema = z.union([ScriptValidator, AIPromptValidator]);

export type Validator = z.infer<typeof ValidatorSchema>;

export const ValidatorsSchema = z.array(ValidatorSchema);

/**
 * This Field BO is stored in the database
 */
export const BaseDatabaseFieldSchema = z.object({
  // Usually this ID is not copied, only forcibly set in some scenarios such as import
  id: z.string().optional(),

  /**
   * Field type
   */
  type: DatabaseFieldTypeSchema,

  /**
   * Is this a template field? Template fields cannot be deleted, but can be after breaking instance
   */
  templateId: z.string().optional(),

  /**
   * Is it a fixed field? Regardless of whether it is a template, it cannot be deleted
   * Usage scenario: TASK table, has fixed fields Due Date and Assignee
   * Default: FULL_EDIT
   */
  privilege: FieldEditPrivilegeSchema.optional(),

  /**
   * Field name
   */
  name: iStringSchema,

  /**
   * Field description
   */
  description: iStringSchema.optional(),

  /**
   * Default required: false
   */
  required: z.boolean().optional(),

  /**
   * Is the field unique?
   */
  unique: z.boolean().optional(),

  /**
   * Data validator
   */
  validators: ValidatorsSchema.optional(),

  /**
   * Field special property
   */
  property: DatabaseFieldPropertySchema.optional(),

  // is the primary field, first column
  primary: z.boolean().optional(),
});

export type BaseDatabaseField = z.infer<typeof BaseDatabaseFieldSchema>;

export const DatabaseSingleTextFieldSchema = BaseDatabaseFieldSchema.extend({
  type: SingleText,
  property: SingleTextFieldPropertySchema.optional(),
});
export type DatabaseSingleTextField = z.infer<typeof DatabaseSingleTextFieldSchema>;

export const DatabaseLongTextFieldSchema = BaseDatabaseFieldSchema.extend({
  type: LongText,
  property: LongTextFieldPropertySchema.optional(),
});
export type DatabaseLongTextField = z.infer<typeof DatabaseLongTextFieldSchema>;

export const DatabaseURLFieldSchema = BaseDatabaseFieldSchema.extend({
  type: URL,
});
export type DatabaseURLField = z.infer<typeof DatabaseURLFieldSchema>;

export const DatabasePhoneFieldSchema = BaseDatabaseFieldSchema.extend({
  type: Phone,
});
export type DatabasePhoneField = z.infer<typeof DatabasePhoneFieldSchema>;

export const DatabaseEmailFieldSchema = BaseDatabaseFieldSchema.extend({
  type: Email,
});
export type DatabaseEmailField = z.infer<typeof DatabaseEmailFieldSchema>;

export const DatabaseCheckBoxFieldSchema = BaseDatabaseFieldSchema.extend({
  type: CheckBox,
});
export type DatabaseCheckBoxField = z.infer<typeof DatabaseCheckBoxFieldSchema>;

// DateTime Field Property
export const DefaultDateTimeFieldPropertySchema = DateTimeFieldPropertySchema.default({
  dateFormat: 'YYYY-MM-DD',
  includeTime: false,
});
export const DefaultDateTimeFieldProperty = DefaultDateTimeFieldPropertySchema.parse(undefined);
export const DatabaseDateTimeFieldSchema = BaseDatabaseFieldSchema.extend({
  type: DateTime,
  property: DefaultDateTimeFieldPropertySchema,
});
export type DatabaseDateTimeField = z.infer<typeof DatabaseDateTimeFieldSchema>;

// DateRange Field Property
export const DefaultDateRangeFieldPropertySchema = DateRangeFieldPropertySchema.default({
  dateFormat: 'YYYY-MM-DD',
  includeTime: false,
});
export const DefaultDateRangeFieldProperty = DefaultDateRangeFieldPropertySchema.parse(undefined);
export const DatabaseDateRangeFieldSchema = BaseDatabaseFieldSchema.extend({
  type: DateRange,
  property: DefaultDateRangeFieldPropertySchema,
});
export type DatabaseDateRangeField = z.infer<typeof DatabaseDateRangeFieldSchema>;

// CreatedTime/ModifiedTime Field Property
export const DatabaseCreatedTimeFieldSchema = BaseDatabaseFieldSchema.extend({
  type: CreatedTime,
  property: DefaultDateTimeFieldPropertySchema,
});
export type DatabaseCreatedTimeField = z.infer<typeof DatabaseCreatedTimeFieldSchema>;

export const DatabaseModifiedTimeFieldSchema = BaseDatabaseFieldSchema.extend({
  type: ModifiedTime,
  property: DefaultDateTimeFieldPropertySchema,
});
export type DatabaseModifiedTimeField = z.infer<typeof DatabaseModifiedTimeFieldSchema>;

// Number Field Property
export const DefaultNumberFieldPropertySchema = NumberFieldPropertySchema.default({
  precision: 0,
  commaStyle: 'thousand',
  symbol: '',
  symbolAlign: 'right',
});
export const DefaultNumberFieldProperty = DefaultNumberFieldPropertySchema.parse(undefined);
export const DatabaseNumberFieldSchema = BaseDatabaseFieldSchema.extend({
  type: NumberField,
  property: DefaultNumberFieldPropertySchema,
});
export type DatabaseNumberField = z.infer<typeof DatabaseNumberFieldSchema>;

// Currency Field Property
export const DefaultCurrencyFieldPropertySchema = NumberFieldPropertySchema.default({
  precision: 2,
  commaStyle: 'thousand',
  symbol: '$',
  symbolAlign: 'left',
});
export const DefaultCurrencyFieldProperty = DefaultCurrencyFieldPropertySchema.parse(undefined);
export const DatabaseCurrencyFieldSchema = BaseDatabaseFieldSchema.extend({
  type: Currency,
  property: DefaultCurrencyFieldPropertySchema,
});
export type DatabaseCurrencyField = z.infer<typeof DatabaseCurrencyFieldSchema>;

// Percent Field Property
export const DefaultPercentFieldPropertySchema = NumberFieldPropertySchema.default({
  precision: 2,
  commaStyle: 'thousand',
  symbol: '%',
  symbolAlign: 'right',
});
export const DefaultPercentFieldProperty = DefaultPercentFieldPropertySchema.parse(undefined);
export const DatabasePercentFieldSchema = BaseDatabaseFieldSchema.extend({
  type: Percent,
  property: DefaultPercentFieldPropertySchema,
});
export type DatabasePercentField = z.infer<typeof DatabasePercentFieldSchema>;

// Rating Field Property
export const DefaultRatingFieldPropertySchema = RatingFieldPropertySchema.default({
  icon: {
    type: 'EMOJI',
    emoji: '⭐️',
  },
  max: 5,
});
export const DefaultRatingFieldProperty = DefaultRatingFieldPropertySchema.parse(undefined);
export const DatabaseRatingFieldSchema = BaseDatabaseFieldSchema.extend({
  type: Rating,
  property: DefaultRatingFieldPropertySchema,
});
export type DatabaseRatingField = z.infer<typeof DatabaseRatingFieldSchema>;

// AutoNumber Field Property
export const DefaultAutoNumberFieldPropertySchema = AutoNumberFieldPropertySchema.default({
  nextId: 1,
});
export const DefaultAutoNumberFieldProperty = DefaultAutoNumberFieldPropertySchema.parse(undefined);
export const DatabaseAutoNumberFieldSchema = BaseDatabaseFieldSchema.extend({
  type: AutoNumber,
  property: DefaultAutoNumberFieldPropertySchema,
});
export type DatabaseAutoNumberField = z.infer<typeof DatabaseAutoNumberFieldSchema>;

// SingleSelect Field Property
export const DefaultSelectFieldPropertySchema = SingleSelectFieldPropertySchema.default({
  options: [],
  defaultValue: '',
});
export const DefaultSelectFieldProperty = DefaultSelectFieldPropertySchema.parse(undefined);
export const DatabaseSingleSelectFieldSchema = BaseDatabaseFieldSchema.extend({
  type: SingleSelect,
  property: DefaultSelectFieldPropertySchema,
});
export type DatabaseSingleSelectField = z.infer<typeof DatabaseSingleSelectFieldSchema>;

// MultiSelect Field Property
export const DefaultMultiSelectFieldPropertySchema = MultiSelectFieldPropertySchema.default({
  options: [],
  defaultValue: [],
});
export const DefaultMultiSelectFieldProperty = DefaultMultiSelectFieldPropertySchema.parse(undefined);
export const DatabaseMultiSelectFieldSchema = BaseDatabaseFieldSchema.extend({
  type: MultiSelect,
  property: DefaultMultiSelectFieldPropertySchema,
});
export type DatabaseMultiSelectField = z.infer<typeof DatabaseMultiSelectFieldSchema>;

export const DatabaseMemberFieldSchema = BaseDatabaseFieldSchema.extend({
  type: Member,
  property: MemberFieldPropertySchema.default({}),
});
export type DatabaseMemberField = z.infer<typeof DatabaseMemberFieldSchema>;

export const DatabaseCreatedByFieldSchema = BaseDatabaseFieldSchema.extend({
  type: CreatedBy,
});
export type DatabaseCreatedByField = z.infer<typeof DatabaseCreatedByFieldSchema>;

export const DatabaseModifiedByFieldSchema = BaseDatabaseFieldSchema.extend({
  type: ModifiedBy,
});
export type DatabaseModifiedByField = z.infer<typeof DatabaseModifiedByFieldSchema>;

export const DatabaseLinkFieldSchema = BaseDatabaseFieldSchema.extend({
  type: Link,
  property: LinkFieldPropertySchema.default({}),
});
export type DatabaseLinkField = z.infer<typeof DatabaseLinkFieldSchema>;

export const DatabaseLookupFieldSchema = BaseDatabaseFieldSchema.extend({
  type: Lookup,
  property: LookupFieldPropertySchema,
});
export type DatabaseLookupField = z.infer<typeof DatabaseLookupFieldSchema>;

export const DatabaseFormulaFieldSchema = BaseDatabaseFieldSchema.extend({
  type: Formula,
  property: FormulaFieldPropertySchema,
});
export type DatabaseFormulaField = z.infer<typeof DatabaseFormulaFieldSchema>;

export const DatabaseAttachmentFieldSchema = BaseDatabaseFieldSchema.extend({
  type: Attachment,
});
export type DatabaseAttachmentField = z.infer<typeof DatabaseAttachmentFieldSchema>;

export const DatabaseVideoFieldSchema = BaseDatabaseFieldSchema.extend({
  type: Video,
});
export type DatabaseVideoField = z.infer<typeof DatabaseVideoFieldSchema>;

export const DatabaseVoiceFieldSchema = BaseDatabaseFieldSchema.extend({
  type: Voice,
});
export type DatabaseVoiceField = z.infer<typeof DatabaseVoiceFieldSchema>;

export const DatabasePhotoFieldSchema = BaseDatabaseFieldSchema.extend({
  type: Photo,
});
export type DatabasePhotoField = z.infer<typeof DatabasePhotoFieldSchema>;

export const DatabaseAPIFieldSchema = BaseDatabaseFieldSchema.extend({
  type: API,
});
export type DatabaseAPIField = z.infer<typeof DatabaseAPIFieldSchema>;

export const DatabaseAITextFieldSchema = BaseDatabaseFieldSchema.extend({
  type: AIText,
  property: AITextFieldPropertySchema,
});
export type DatabaseAITextField = z.infer<typeof DatabaseAITextFieldSchema>;

export const DatabaseAIVoiceFieldSchema = BaseDatabaseFieldSchema.extend({
  type: AIVoice,
});
export type DatabaseAIVoiceField = z.infer<typeof DatabaseAIVoiceFieldSchema>;

export const DatabaseAIPhotoFieldSchema = BaseDatabaseFieldSchema.extend({
  type: AIPhoto,
});
export type DatabaseAIPhotoField = z.infer<typeof DatabaseAIPhotoFieldSchema>;

export const DatabaseAIVideoFieldSchema = BaseDatabaseFieldSchema.extend({
  type: AIVideo,
});
export type DatabaseAIVideoField = z.infer<typeof DatabaseAIVideoFieldSchema>;

export const DatabaseCutVideoFieldSchema = BaseDatabaseFieldSchema.extend({
  type: CutVideo,
});
export type DatabaseCutVideoField = z.infer<typeof DatabaseCutVideoFieldSchema>;

export const DatabaseJSONFieldSchema = BaseDatabaseFieldSchema.extend({
  type: JSONField,
});
export type DatabaseJSONField = z.infer<typeof DatabaseJSONFieldSchema>;

export const DatabaseCascaderFieldSchema = BaseDatabaseFieldSchema.extend({
  type: Cascader,
});
export type DatabaseCascaderField = z.infer<typeof DatabaseCascaderFieldSchema>;

export const DatabaseOneWayLinkFieldSchema = BaseDatabaseFieldSchema.extend({
  type: OneWayLink,
  property: OneWayLinkFieldPropertySchema,
});
export type DatabaseOneWayLinkField = z.infer<typeof DatabaseOneWayLinkFieldSchema>;

export const DatabaseWorkDocFieldSchema = BaseDatabaseFieldSchema.extend({
  type: WorkDoc,
});
export type DatabaseWorkDocField = z.infer<typeof DatabaseWorkDocFieldSchema>;

export const DatabaseButtonFieldSchema = BaseDatabaseFieldSchema.extend({
  type: Button,
});
export type DatabaseButtonField = z.infer<typeof DatabaseButtonFieldSchema>;

export const DatabaseFieldSchema = z.discriminatedUnion('type', [
  // Text
  DatabaseSingleTextFieldSchema,
  DatabaseLongTextFieldSchema,
  DatabaseURLFieldSchema,
  DatabasePhoneFieldSchema,
  DatabaseEmailFieldSchema,
  // Bool
  DatabaseCheckBoxFieldSchema,
  // Dates
  DatabaseDateTimeFieldSchema,
  DatabaseDateRangeFieldSchema,
  DatabaseCreatedTimeFieldSchema,
  DatabaseModifiedTimeFieldSchema,
  // Number
  DatabaseNumberFieldSchema,
  DatabaseCurrencyFieldSchema,
  DatabasePercentFieldSchema,
  DatabaseRatingFieldSchema,
  DatabaseAutoNumberFieldSchema,
  // Select
  DatabaseSingleSelectFieldSchema,
  DatabaseMultiSelectFieldSchema,
  // Member
  DatabaseMemberFieldSchema,
  // Audit
  DatabaseCreatedByFieldSchema,
  DatabaseModifiedByFieldSchema,
  // Complicated
  DatabaseLinkFieldSchema,
  DatabaseOneWayLinkFieldSchema,
  DatabaseLookupFieldSchema,
  DatabaseFormulaFieldSchema,
  // Files
  DatabaseAttachmentFieldSchema,
  DatabaseVideoFieldSchema,
  DatabaseVoiceFieldSchema,
  DatabasePhotoFieldSchema,
  DatabaseAPIFieldSchema,
  DatabaseAITextFieldSchema,
  DatabaseAIVoiceFieldSchema,
  DatabaseAIPhotoFieldSchema,
  DatabaseAIVideoFieldSchema,
  DatabaseCutVideoFieldSchema,
  DatabaseJSONFieldSchema,
  DatabaseCascaderFieldSchema,
  DatabaseWorkDocFieldSchema,
  DatabaseButtonFieldSchema,
]);
export const DatabaseFieldUnionSchema = z.intersection(DatabaseFieldSchema, BaseDatabaseFieldSchema);
export type DatabaseField = z.infer<typeof DatabaseFieldSchema>;

export type DatabaseFieldWithId<T extends DatabaseField = DatabaseField> = T & {
  id: string;
};

// Use Required
// export type DatabaseFieldWithId = DatabaseField & Required<Pick<DatabaseField, 'id'>>;

// Use Omit
// export type DatabaseFieldWithId<T extends DatabaseField = DatabaseField> = Omit<T, 'id'> & {
//   id: string;
// };

// Basic data types (basic data types after aggregation/formula calculation)
// This enum is not standardized, easy to conflict with variables, needs refactoring
export enum BasicValueType {
  String = 'String',
  Number = 'Number',
  DateTime = 'DateTime',
  Array = 'Array',
  Boolean = 'Boolean',
}
