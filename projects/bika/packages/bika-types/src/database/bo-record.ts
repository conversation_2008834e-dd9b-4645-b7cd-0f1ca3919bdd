import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import { AttachmentVOSchema } from '../attachment/vo-attachment';

export const AttachmentCellDataSchema = AttachmentVOSchema;
export type AttachmentCellData = z.infer<typeof AttachmentCellDataSchema>;

export const DocCellDataSchema = z.object({
  docId: z.string(),
  name: z.string(),
});
export type DocCellData = z.infer<typeof DocCellDataSchema>;

export const CellValueBaseSchema = z.union([
  z.null(),
  // z.undefined(), // can not be undefined cause zod openapi schema does not support undefined
  z.number(),
  z.string(),
  z.string().array(),
  z.boolean(),
  iStringSchema,
  AttachmentCellDataSchema,
  DocCellDataSchema,
]);

export const LookUpCellValueSchema = z.array(CellValueBaseSchema);
export type LookUpCellValue = z.infer<typeof LookUpCellValueSchema>;

export const CellValueSchema = z.union([CellValueBaseSchema, LookUpCellValueSchema]);
export type CellValue = z.infer<typeof CellValueSchema>;

// Why is it structured this way? Do cell values have multiple languages?
// string | iString | (string | iString | null)[]
export const CellValuesSchema = iStringSchema.or(z.array(iStringSchema.or(z.null())));
export type CellValues = z.infer<typeof CellValuesSchema>;

export const RecordDataSchema = z.record(z.string(), CellValueSchema.optional());
export type RecordData = z.infer<typeof RecordDataSchema>;

export const RecordValueSchema = z.record(z.string(), CellValueSchema.optional());
export type RecordValue = z.infer<typeof RecordValueSchema>;
