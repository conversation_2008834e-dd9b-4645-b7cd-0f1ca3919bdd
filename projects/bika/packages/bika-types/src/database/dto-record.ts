import { PaginationSchema } from 'basenext/pagination';
import { z } from 'zod';
import { CellValueSchema, RecordDataSchema } from './bo-record';
import { ViewFilterSchema, ViewGroupArraySchema, ViewSortArraySchema } from './bo-view';

const RecordListBaseSchema = z.object({
  databaseId: z.string(),
  viewId: z.string().optional(),
  mirrorId: z.string().optional(),
  formId: z.string().optional(),

  // Filter
  filter: ViewFilterSchema.optional(),

  sort: ViewSortArraySchema.optional(),

  // Grouping
  group: ViewGroupArraySchema.and(z.object({ fieldId: z.string() }).array()).optional(),
  // Group selection
  groupSelect: z.array(CellValueSchema).optional(),

  // Search
  keyword: z.string().optional(),

  // Whether to include comment count
  withCommentCount: z.boolean().optional(),
});

export const ViewGroupFilterSchema = ViewGroupArraySchema.and(z.object({ fieldId: z.string() }).array()).optional();

export type ViewGroupFilter = z.infer<typeof ViewGroupFilterSchema>;

export const RecordListDTOSchema = RecordListBaseSchema.extend({
  startRow: z.number().min(0),
  endRow: z.number().min(1),
});

export type RecordListDTO = z.infer<typeof RecordListDTOSchema>;

export const InfiniteRecordListDTOSchema = RecordListBaseSchema.extend({
  cursor: z.number().min(0),
  limit: z.number().max(20),
});

export type InfiniteRecordListDTO = z.infer<typeof InfiniteRecordListDTOSchema>;

export const RecordDetailDTOSchema = z.object({
  databaseId: z.string().describe('Table ID'),
  viewId: z.string().optional().describe('Specified view ID'),
  mirrorId: z.string().optional().describe('Mirror ID'),
  recordId: z.string().describe('Row record ID'),
});
export type RecordDetailDTO = z.infer<typeof RecordDetailDTOSchema>;

export const RecordDetailListDTOSchema = z.object({
  databaseId: z.string().describe('Table ID'),
  viewId: z.string().optional().describe('Specified view ID'),
  mirrorId: z.string().optional().describe('Mirror ID'),
  recordIds: z.string().array().describe('Row record List'),
});
export type RecordDetailListDTO = z.infer<typeof RecordDetailListDTOSchema>;

export const RecordCreateSchema = z.object({
  cells: RecordDataSchema,
});
export type RecordCreate = z.infer<typeof RecordCreateSchema>;

export const RecordCreateDTOSchema = RecordCreateSchema.extend({
  databaseId: z.string(),
  mirrorId: z.string().optional(), // Mirror ID
  formId: z.string().optional(), // Form submission
  sharing: z.boolean().optional(), // Whether on sharing page (currently sharing and internal URL are the same, need this flag to distinguish, only sharing entry allows anonymous submission)
});
export type RecordCreateDTO = z.infer<typeof RecordCreateDTOSchema>;

export const RecordsCreateDTOSchema = z.object({
  databaseId: z.string(),
  mirrorId: z.string().optional(), // Mirror ID
  formId: z.string().optional(), // Form submission
  sharing: z.boolean().optional(), // Whether on sharing page (currently sharing and internal URL are the same, need this flag to distinguish, only sharing entry allows anonymous submission)
  cells: z.array(RecordDataSchema).describe('the cells to be created'),
});
export type RecordsCreateDTO = z.infer<typeof RecordsCreateDTOSchema>;

export const RecordUpdateSchema = RecordCreateSchema.extend({
  id: z.string().describe('the unique id of the record'),
});
export type RecordUpdate = z.infer<typeof RecordUpdateSchema>;

export const RecordUpdateDTOSchema = RecordUpdateSchema.extend({
  databaseId: z.string(),
  mirrorId: z.string().optional().describe('Mirror ID'),
});

export type RecordUpdateDTO = z.infer<typeof RecordUpdateDTOSchema>;

export const RecordBulkUpdateSchema = z.object({
  recordId: z.string().describe('the unique id of the record'),
  cells: RecordDataSchema.describe('the cells to be updated'),
});
export type RecordBulkUpdate = z.infer<typeof RecordBulkUpdateSchema>;

export const RecordBulkUpdatesSchema = RecordBulkUpdateSchema.array();
export type RecordBulkUpdates = z.infer<typeof RecordBulkUpdatesSchema>;

export const RecordBulkUpdatesDTOSchema = z.object({
  databaseId: z.string(),
  mirrorId: z.string().optional().describe('Mirror ID'),
  updates: RecordBulkUpdatesSchema,
});
export type RecordBulkUpdatesDTO = z.infer<typeof RecordBulkUpdatesDTOSchema>;

/**
 *
 * Bulk update, filter conditions + fields to update
 */
export const RecordBulkUpdateDTOSchema = z.object({
  // Table ID
  databaseId: z.string(),
  // Mirror ID
  mirrorId: z.string().optional(),

  // Matching records
  match: z.object({
    // Selected records
    recordIds: z.array(z.string()).optional(),
    // Filters
    filters: ViewFilterSchema.optional(),
  }),

  // Cells to be updated in bulk
  cells: RecordDataSchema,
});

export type RecordBulkUpdateDTO = z.infer<typeof RecordBulkUpdateDTOSchema>;

/**
 * Delete data rows, supports single and batch deletion
 */
export const RecordDeleteDTOSchema = z.object({
  databaseId: z.string(),
  mirrorId: z.string().optional().describe('Mirror ID'),
  recordIds: z.array(z.string()).min(1),
});

export type RecordDeleteDTO = z.infer<typeof RecordDeleteDTOSchema>;

export const RecordCommentListSchema = PaginationSchema.extend({
  databaseId: z.string(),
  mirrorId: z.string().optional().describe('Mirror ID'),
  recordId: z.string(),
});

export type RecordCommentListDTO = z.infer<typeof RecordCommentListSchema>;

export const RecordAddCommentDTOSchema = z.object({
  recordId: z.string(),
  databaseId: z.string(),
  mirrorId: z.string().optional().describe('Mirror ID'),
  content: z.string(),
});

export type RecordAddCommentDTO = z.infer<typeof RecordAddCommentDTOSchema>;

export const RecordActivityListSchema = PaginationSchema.extend({
  databaseId: z.string(),
  mirrorId: z.string().optional().describe('Mirror ID'),
  recordId: z.string(),
  type: z.enum(['comment', 'change']).default('change'),
});

export type RecordActivityListDTO = z.infer<typeof RecordActivityListSchema>;
