import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import {
  ViewGroupArraySchema,
  ViewFilterSchema,
  ViewSortArraySchema,
  ViewFieldArraySchema,
  ViewRelationTypeSchema,
  ViewSchema,
  DatabaseViewTypeSchema,
  ViewExtraSchema,
  ViewFieldSchema,
} from './bo-view';

export const ViewInfoDTOSchema = z.object({
  viewId: z.string(),
  databaseId: z.string().optional(),
  mirrorId: z.string().optional(),
});

export type ViewInfoDTO = z.infer<typeof ViewInfoDTOSchema>;

export const DatabaseViewCreateDTOSchema = ViewSchema.extend({
  preViewId: z.string().optional(),
  relationId: z.string().optional(),
  relationType: ViewRelationTypeSchema.optional(),
  databaseId: z.string(),
}).default({
  databaseId: '',
  name: 'all',
  type: 'TABLE',
});

export type DatabaseViewCreateDTO = z.infer<typeof DatabaseViewCreateDTOSchema>;

export const DatabaseViewDTOSchema = ViewSchema.extend({
  id: z.string(),
  databaseId: z.string(),
  fields: z.array(
    ViewFieldSchema.extend({
      fieldId: z.string(),
    }).optional(),
  ),
});

export type DatabaseViewDTO = z.infer<typeof DatabaseViewDTOSchema>;

export const DatabaseViewUpdateDTOSchema = z.object({
  name: iStringSchema.optional(),
  type: DatabaseViewTypeSchema.optional(),
  filters: ViewFilterSchema.optional(),
  sorts: ViewSortArraySchema.optional(),
  groups: ViewGroupArraySchema.optional(),
  fields: ViewFieldArraySchema.optional(),
  extra: ViewExtraSchema.optional(),
  preViewId: z.string().optional().nullable(),
});

export type DatabaseViewUpdateDTO = z.infer<typeof DatabaseViewUpdateDTOSchema>;

export const ViewUpdateDTOSchema = z.object({
  viewId: z.string(),
  databaseId: z.string(),
  data: DatabaseViewUpdateDTOSchema,
});
export type ViewUpdateDTO = z.infer<typeof ViewUpdateDTOSchema>;

export const ViewDeleteDTOSchema = z.object({
  databaseId: z.string(),
  viewId: z.string(),
});

export type ViewDeleteDTO = z.infer<typeof ViewDeleteDTOSchema>;
