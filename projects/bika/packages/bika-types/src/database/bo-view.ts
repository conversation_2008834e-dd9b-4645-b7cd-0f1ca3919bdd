import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import { FilterConditionSchema } from './bo-view-filter';
import { DeprecatedFilterConditionSchema } from './bo-view-filter-deprecated';
import { FilterConjunctionSchema } from '../shared/filters-input';

// Sort
export const SortedFieldSchema = z.object({
  fieldTemplateId: z.string().optional(),
  fieldId: z.string().optional(),
  asc: z.boolean(),
});
export type SortedField = z.infer<typeof SortedFieldSchema>;

export const ViewSortArraySchema = z.array(SortedFieldSchema);
export type ViewSortArray = z.infer<typeof ViewSortArraySchema>;

// Group
export const ViewGroupSchema = SortedFieldSchema;
export type ViewGroup = z.infer<typeof ViewGroupSchema>;

export const ViewGroupArraySchema = z.array(ViewGroupSchema);
export type ViewGroupArray = z.infer<typeof ViewGroupArraySchema>;

export const ViewFilterSchema = z.object({
  conjunction: FilterConjunctionSchema,
  /**
   * @deprecated Use `conds` instead.
   * This field is deprecated and will be removed in future versions.
   * Use `conds` for new implementations.
   */
  conditions: z.array(DeprecatedFilterConditionSchema),
  conds: z.array(FilterConditionSchema).optional(),
});
export type ViewFilter = z.infer<typeof ViewFilterSchema>;
// How the view is contained
// A view is a way to control permissions and presentation.
export const ViewResourceTypeSchema = z.enum([
  // View inside the database
  'DATABASE_VIEW',

  // View on a node resource
  'NODE_RESOURCE_VIEW',
]);

// same as DatabaseViewType in db
export const DatabaseViewTypes = ['TABLE', 'KANBAN', 'GALLERY', 'GANTT', 'FORM'] as const;
export const DatabaseViewTypeSchema = z.enum(DatabaseViewTypes);
export type ViewType = z.infer<typeof DatabaseViewTypeSchema>;

export const ViewNodeRelationType = z.literal('NODE').describe('node');
export const ViewDatabaseRelationType = z.literal('DATABASE').describe('database');
export const ViewRelationTypeSchema = z.union([ViewNodeRelationType, ViewDatabaseRelationType]);
export type ViewRelationType = z.infer<typeof ViewRelationTypeSchema>;

export const ViewFieldSchema = z.object({
  id: z.string().optional(),
  templateId: z.string().optional(),
  hidden: z.boolean().optional(),
  width: z.number().optional(),
});

export const ViewFieldArraySchema = z.array(ViewFieldSchema).optional();

export type ViewField = z.infer<typeof ViewFieldSchema>;
export const ViewExtraSchema = z.object({
  // Single select field grouping
  kanbanGroupingFieldId: z.string().optional(),
  kanbanGroupingFieldTemplateId: z.string().optional(),
  kanbanGroupOptionIds: z.array(z.string()).optional(),

  // Cover options for Kanban/Gallery views
  coverFieldId: z.string().optional(),
  coverFieldTemplateId: z.string().optional(),

  // Cover stretch options for kanban/gallery
  // "Fit" - Image maintains aspect ratio to fit card size
  // "Fill" - Image fills card but may crop content
  // "Original Size" - Uses original image size, may result in inconsistent card sizes
  coverStretch: z.enum(['FILL', 'FIT']).optional(),

  // Whether to display field names on kanban and gallery view cards
  // Not displaying can save space
  displayFieldName: z.boolean().optional(),

  // Number of items per row for Gallery view
  // Default is auto if not specified
  itemsPerRow: z.number().optional(),

  // Whether to hide all items
  isHideAllItems: z.boolean().optional(),
});

export type ViewExtra = z.infer<typeof ViewExtraSchema>;

export const ViewSchema = z.object({
  type: DatabaseViewTypeSchema,

  id: z.string().optional(),
  templateId: z.string().optional(),

  name: iStringSchema,
  description: iStringSchema.optional(),

  // When the view is a node resource, you must select the database it is attached to. This field is required.
  databaseTemplateId: z.string().optional(),
  databaseId: z.string().optional(),

  filters: ViewFilterSchema.optional(),
  sorts: ViewSortArraySchema.optional(),

  fields: ViewFieldArraySchema.optional(),

  groups: ViewGroupArraySchema.optional(),

  extra: ViewExtraSchema.optional(),
});

export type View = z.infer<typeof ViewSchema>;
