import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
import { DatabaseRecordSchema } from './bo-database';
import { DatabaseFieldSchema } from './bo-field';
import { ViewSchema } from './bo-view';
import { BaseCreateNodeResourceBOSchema, DatabaseNodeType, NodeUpdateBOSchema } from '../node/base';

export const DatabaseInfoDTOSchema = z.object({
  databaseId: z.string(),
});

export type DatabaseInfoDTO = z.infer<typeof DatabaseInfoDTOSchema>;

export const DatabaseUpdateBOSchema = NodeUpdateBOSchema.extend({
  resourceType: z.literal('DATABASE'),
  name: iStringSchema.optional(),
  description: iStringSchema.optional(),
  views: z
    .array(
      z.object({
        id: z.string(),
      }),
    )
    .optional(),
});

export type DatabaseUpdateDTO = z.infer<typeof DatabaseUpdateBOSchema>;

export const DatabaseCreateBOSchema = BaseCreateNodeResourceBOSchema.extend({
  resourceType: DatabaseNodeType,
  views: z.array(ViewSchema).optional(),
  fields: z.array(DatabaseFieldSchema).optional(),
  records: z.array(DatabaseRecordSchema).optional(),
});

export type DatabaseCreateDTO = z.infer<typeof DatabaseCreateBOSchema>;

export const LinkDatabaseDTOSchema = z.object({
  databaseId: z.string(),
});

export type LinkDatabaseDTO = z.infer<typeof LinkDatabaseDTOSchema>;
export const LinkDatabaseInfoDTOSchema = z.object({
  databaseId: z.string().describe('Database ID'),
  linkFieldId: z.string().describe('Linked Field ID'),
  formId: z.string().optional().describe('Linked Form ID'),
});

export type LinkDatabaseInfoDTO = z.infer<typeof LinkDatabaseInfoDTOSchema>;
