import { AvatarLogoSchema } from 'basenext/avatar';
import { iStringSchema } from 'basenext/i18n';
import { BasePaginationInfoSchema } from 'basenext/pagination';
import { z } from 'zod';
import {
  AITextFieldPropertySchema,
  AITextFieldPropertyDeprecatedSchema,
  AutoNumberFieldPropertySchema,
  BaseDatabaseFieldSchema,
  DatabaseFieldPropertySchema,
  FormulaFieldPropertySchema,
  LinkFieldPropertySchema,
  LongTextFieldPropertySchema,
  LookupFieldPropertySchema,
  MemberFieldPropertySchema,
  MultiSelectFieldPropertySchema,
  NumberFieldPropertySchema,
  OneWayLinkFieldPropertySchema,
  RatingFieldPropertySchema,
  SingleSelectFieldPropertySchema,
  SingleTextFieldPropertySchema,
} from './bo-field';
import {
  DatabaseFieldTypeSchema,
  Attachment,
  AutoNumber,
  CheckBox,
  CreatedTime,
  Currency,
  DateTime,
  Email,
  Link,
  LongText,
  Lookup,
  Member,
  CreatedBy,
  ModifiedBy,
  ModifiedTime,
  MultiSelect,
  NumberField,
  OneWayLink,
  Percent,
  Phone,
  Rating,
  SingleSelect,
  SingleText,
  URL,
  Formula,
  Cascader,
  Button,
  WorkDoc,
  AIText,
  AIVoice,
  AIPhoto,
  AIVideo,
  CutVideo,
  JSONField,
  Video,
  Voice,
  Photo,
  API,
  DateRange,
} from './bo-field-type';
import { FieldVOSchema } from './vo-field';
import { CellFormatSchema } from '../openapi/dto-bika';
import { DateTimeFieldPropertySchema } from '../system';
import {
  AttachmentCellDataSchema,
  CellValueSchema,
  CellValuesSchema,
  DocCellDataSchema,
  LookUpCellValueSchema,
} from './bo-record';
import { RenderOptionSchema } from '../system/render-option';
import { MemberVOSchema } from '../unit/vo-member';
import { UserVOSchema } from '../user/vo-user';

// string | number | boolean | null | (string | number | boolean | null)[]
export const LookupComputedCellDataBaseSchema = z.union([z.null(), z.number(), z.boolean(), z.string()]);
export const LookupComputedCellDataSchema = z.union([
  LookupComputedCellDataBaseSchema,
  z.array(LookupComputedCellDataBaseSchema),
]);
export type LookupComputedCellData = z.infer<typeof LookupComputedCellDataSchema>;

export const FormulaComputedCellDataTypes = ['STRING', 'NUMBER', 'BOOLEAN'] as const;
export const FormulaComputedCellDataTypeSchema = z.enum(FormulaComputedCellDataTypes);
export type FormulaComputedCellDataType = z.infer<typeof FormulaComputedCellDataTypeSchema>;
// string | number | boolean | (string | number | boolean)[]
export const FormulaComputedCellDataBaseSchema = z.union([z.string(), z.number(), z.boolean()]);
export const FormulaComputedCellDataSchema = z.union([
  FormulaComputedCellDataBaseSchema,
  z.array(FormulaComputedCellDataBaseSchema),
]);
export type FormulaComputedCellData = z.infer<typeof FormulaComputedCellDataSchema>;

const BaseStandardCellValueSchema = z.object({
  type: DatabaseFieldTypeSchema,
  property: DatabaseFieldPropertySchema.optional(),
});

export const SingleTextStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: SingleText,
  property: SingleTextFieldPropertySchema,
  data: z.string().nullish(),
  value: z.string().nullish(),
});

export const LongTextStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: LongText,
  property: LongTextFieldPropertySchema,
  data: z.string().nullish(),
  value: z.string().nullish(),
});

// 3. URL
export const UrlStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: URL,
  data: z.string().nullish(),
  value: z.string().nullish(),
});

export const EmailStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: Email,
  data: z.string().nullish(),
  value: z.string().nullish(),
});

export const PhoneStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: Phone,
  data: z.string().nullish(),
  value: z.string().nullish(),
});

export const NumberStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: NumberField,
  property: NumberFieldPropertySchema,
  data: z.number().nullish(),
  value: z.string().nullish(),
});

export const CurrencyStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: Currency,
  property: NumberFieldPropertySchema,
  data: z.number().nullish(),
  value: z.string().nullish(),
});

export const PercentStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: Percent,
  property: NumberFieldPropertySchema,
  data: z.number().nullish(),
  value: z.string().nullish(),
});

export const RatingStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: Rating,
  property: RatingFieldPropertySchema,
  data: z.number().nullish(),
  value: z.string().nullish(),
});

export const AutoNumberStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: AutoNumber,
  property: AutoNumberFieldPropertySchema,
  data: z.number().nullish(),
  value: z.string().nullish(),
});

export const CheckBoxStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: CheckBox,
  data: z.boolean().nullish(),
  value: z.string().nullish(),
});

export const SingleSelectStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: SingleSelect,
  property: SingleSelectFieldPropertySchema,
  data: z.string().array().nullish(),
  value: iStringSchema.array().nullish(),
});

export const MultiSelectStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: MultiSelect,
  property: MultiSelectFieldPropertySchema,
  data: z.string().array().nullish(),
  value: iStringSchema.array().nullish(),
});

export const DateTimeStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: DateTime,
  property: DateTimeFieldPropertySchema,
  data: z.string().nullish(),
  value: z.string().nullish(),
});

export const DateRangeStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: DateRange,
  property: DateTimeFieldPropertySchema,
  data: z.string().nullish(),
  value: z.string().nullish(),
});

export const FormulaStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: Formula,
  property: FormulaFieldPropertySchema,
  data: CellValueSchema.optional(),
  value: z.union([z.string(), z.string().array()]).nullish(),
});

export const CreatedTimeStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: CreatedTime,
  property: DateTimeFieldPropertySchema,
  data: z.string().nullish(),
  value: z.string().nullish(),
});

export const ModifiedTimeStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: ModifiedTime,
  property: DateTimeFieldPropertySchema,
  data: z.string().nullish(),
  value: z.string().nullish(),
});

export const MemberStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: Member,
  property: MemberFieldPropertySchema,
  data: z.string().array().nullish(),
  value: z.string().array().nullish(),
});

export const CreatedByStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: CreatedBy,
  data: z.string().nullish(),
  value: z.string().nullish(),
});

export const ModifiedByStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: ModifiedBy,
  data: z.string().nullish(),
  value: z.string().nullish(),
});

export const AttachmentStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: Attachment,
  data: AttachmentCellDataSchema.array().nullish(),
  value: z.string().array().nullish(),
});

export const DocumentStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: WorkDoc,
  data: DocCellDataSchema.nullish(),
  value: z.string().nullish(),
});

export const LinkStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: Link,
  property: LinkFieldPropertySchema,
  data: z.string().array().nullish(),
  value: z.string().array().nullish(),
});

// 25. Lookup
export const LookupStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: Lookup,
  property: LookupFieldPropertySchema,
  data: LookUpCellValueSchema.nullish(),
  value: CellValuesSchema.nullish(),
});

// ======== Not implemented ========
const UnimplementedStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  data: CellValueSchema.optional(),
  value: z.string().nullish(),
});
export const VideoStandardCellValueSchema = UnimplementedStandardCellValueSchema.extend({
  type: Video,
});
export const VoiceStandardCellValueSchema = UnimplementedStandardCellValueSchema.extend({
  type: Voice,
});
export const PhotoStandardCellValueSchema = UnimplementedStandardCellValueSchema.extend({
  type: Photo,
});
export const APIStandardCellValueSchema = UnimplementedStandardCellValueSchema.extend({
  type: API,
});
export const AITextStandardCellValueSchema = UnimplementedStandardCellValueSchema.extend({
  type: AIText,
  property: AITextFieldPropertySchema.or(AITextFieldPropertyDeprecatedSchema.optional()),
});
export const AIVoiceStandardCellValueSchema = UnimplementedStandardCellValueSchema.extend({
  type: AIVoice,
});
export const AIPhotoStandardCellValueSchema = UnimplementedStandardCellValueSchema.extend({
  type: AIPhoto,
});
export const AIVideoStandardCellValueSchema = UnimplementedStandardCellValueSchema.extend({
  type: AIVideo,
});
export const CutVideoStandardCellValueSchema = UnimplementedStandardCellValueSchema.extend({
  type: CutVideo,
});
export const JSONStandardCellValueSchema = UnimplementedStandardCellValueSchema.extend({
  type: JSONField,
});
export const CascaderStandardCellValueSchema = UnimplementedStandardCellValueSchema.extend({
  type: Cascader,
});
export const OneWayLinkStandardCellValueSchema = BaseStandardCellValueSchema.extend({
  type: OneWayLink,
  property: OneWayLinkFieldPropertySchema,
  data: z.string().array().nullish(),
  value: z.string().array().nullish(),
});
export const ButtonStandardCellValueSchema = UnimplementedStandardCellValueSchema.extend({
  type: Button,
});

export const StandardCellValueSchema = z.discriminatedUnion('type', [
  SingleTextStandardCellValueSchema,
  LongTextStandardCellValueSchema,
  UrlStandardCellValueSchema,
  EmailStandardCellValueSchema,
  PhoneStandardCellValueSchema,
  NumberStandardCellValueSchema,
  CurrencyStandardCellValueSchema,
  PercentStandardCellValueSchema,
  RatingStandardCellValueSchema,
  AutoNumberStandardCellValueSchema,
  CheckBoxStandardCellValueSchema,
  SingleSelectStandardCellValueSchema,
  MultiSelectStandardCellValueSchema,
  DateTimeStandardCellValueSchema,
  DateRangeStandardCellValueSchema,
  CreatedTimeStandardCellValueSchema,
  ModifiedTimeStandardCellValueSchema,
  FormulaStandardCellValueSchema,
  MemberStandardCellValueSchema,
  CreatedByStandardCellValueSchema,
  ModifiedByStandardCellValueSchema,
  AttachmentStandardCellValueSchema,
  DocumentStandardCellValueSchema,
  LinkStandardCellValueSchema,
  LookupStandardCellValueSchema,
  // Not implemented
  VideoStandardCellValueSchema,
  VoiceStandardCellValueSchema,
  PhotoStandardCellValueSchema,
  APIStandardCellValueSchema,
  AITextStandardCellValueSchema,
  AIVoiceStandardCellValueSchema,
  AIPhotoStandardCellValueSchema,
  AIVideoStandardCellValueSchema,
  CutVideoStandardCellValueSchema,
  JSONStandardCellValueSchema,
  CascaderStandardCellValueSchema,
  OneWayLinkStandardCellValueSchema,
  ButtonStandardCellValueSchema,
]);
// Field cell standard value, provides conversion operations
export type StandardCellValue = z.infer<typeof StandardCellValueSchema>;

// Return value of member field cell
export const MemberCellValueSchema = z.object({
  id: z.string().describe('Member ID'),
  avatar: AvatarLogoSchema.optional(),
  name: z.string().describe('Member name'),
  deleted: z.boolean().optional(),
});
export type MemberCellValue = z.infer<typeof MemberCellValueSchema>;

const ButtonTypeSchema = z.enum(['warning', 'success', 'danger', 'primary']);
// Cell value for member field
export const ButtonCellValueSchema = z.object({
  type: ButtonTypeSchema.describe('Button type'),
  btnText: z.string().describe('Button text'),
  onClick: z.function().describe('Click event'),
});
export type ButtonCellValue = z.infer<typeof ButtonCellValueSchema>;

// Cell value for CreatedBy/ModifiedBy
export const UserCellValueSchema = z.object({
  id: z.string().describe('User ID'),
  avatar: AvatarLogoSchema.optional(),
  name: z.string().describe('Member name or anonymous (i18n)'),
  deleted: z.boolean().optional(),
});
export type UserCellValue = z.infer<typeof UserCellValueSchema>;

export const AttachmentCellValueSchema = z.object({
  thumbnailUrl: z.string().optional().nullable(),
  previewUrl: z.string().optional().nullable(),
  downloadUrl: z.string().optional(),
});

export type AttachmentCellValue = z.infer<typeof AttachmentCellValueSchema>;

export const AttachmentCellVOSchema = AttachmentCellDataSchema.merge(AttachmentCellValueSchema);
export type AttachmentCellVO = z.infer<typeof AttachmentCellVOSchema>;

export const CellValueVOSchema = iStringSchema
  .or(z.number())
  .or(z.array(iStringSchema.or(z.null())))
  .or(MemberCellValueSchema.array())
  .or(UserCellValueSchema)
  .or(AttachmentCellValueSchema.array())
  .nullable();
export type CellValueVO = z.infer<typeof CellValueVOSchema>;

export const CellValueLabelVOSchema = iStringSchema
  .or(z.array(iStringSchema.or(z.null())))
  .nullable()
  .optional();
export type CellValueLabelVO = z.infer<typeof CellValueLabelVOSchema>;

// Internal logic, not exposed externally, mainly distinguishes the return value of value
export const CellVOSchema = z.object({
  key: z.string().optional(),
  id: z.string(),
  name: z.string().optional(),
  fieldType: DatabaseFieldTypeSchema.optional(),
  data: CellValueSchema.optional(),
  value: CellValueLabelVOSchema.optional(),
});
export type CellVO = z.infer<typeof CellVOSchema>;

export const RecordVOSchema = z.object({
  id: z.string(),
  databaseId: z.string(),
  revision: z.number().optional(),
  cells: z.record(CellVOSchema),
  url: z.string().optional().nullable(),
  key: z.string().optional().nullable(),
});
export type RecordVO = z.infer<typeof RecordVOSchema>;

export const CellDataValueVOSchema = z.object({
  data: CellValueSchema.optional(),
  value: CellValueVOSchema.optional(),
});

export type CellDataValueVO = z.infer<typeof CellDataValueVOSchema>;
/**
 * Data structure used in Grid
 */
export const CellRenderVOSchema = CellDataValueVOSchema.extend({
  id: z.string(),
  name: z.string().optional(),
});
export type CellRenderVO = z.infer<typeof CellRenderVOSchema>;

/**
 * RenderVO is used by Grid
 */
export const RecordRenderVOSchema = z.object({
  id: z.string(),
  databaseId: z.string(),
  revision: z.number(),
  cells: z.record(CellRenderVOSchema),
  commentCount: z.number().optional(),
  groupCount: z.number().optional(),
});
export type RecordRenderVO = z.infer<typeof RecordRenderVOSchema>;

/**
 * TODO: To be organized into a unified pagination specification
 */
export const RecordPaginationVOSchema = z.object({
  // When table data exceeds 50,000, total is null
  total: z.number().nullable(),
  nextRow: z.number().optional(),
  rows: z.array(RecordRenderVOSchema),
});
export type RecordPaginationVO = z.infer<typeof RecordPaginationVOSchema>;
/**
 * Record detail with Fields
 * Why include fields?
 * When opening a Record Detail UI, it shows the current Record data
 * The linked Fields become tabs
 * The linked data is loaded only when user clicks on a tab
 */
export const RecordDetailVOSchema = z.object({
  record: RecordRenderVOSchema.optional(),
  revision: z.number().optional(),
  fields: z.array(FieldVOSchema),
});
export type RecordDetailVO = z.infer<typeof RecordDetailVOSchema>;

export const RecordDetailsVOSchema = z.object({
  records: z.array(RecordRenderVOSchema),
  fields: z.array(FieldVOSchema),
});
export type RecordDetailsVO = z.infer<typeof RecordDetailsVOSchema>;

export const RecordCommentVOSchema = z.object({
  id: z.string(),
  // UI style of activity
  style: z.enum(['comment', 'change']).optional(),
  content: z.string(),
  createAt: z.string().datetime(),
  member: MemberVOSchema.nullish(),
  user: UserVOSchema, // Shows "who made the change"
});
export type RecordCommentVO = z.infer<typeof RecordCommentVOSchema>;

export const RecordCommentPaginationVOSchema = BasePaginationInfoSchema.extend({
  data: z.array(RecordCommentVOSchema),
});
export type RecordCommentPaginationVO = z.infer<typeof RecordCommentPaginationVOSchema>;

export const DatabaseRecordChangeContentSchema = z.object({
  field: BaseDatabaseFieldSchema.pick({ id: true, name: true, type: true, property: true }).extend({
    name: z.string(),
  }),
  previousField: BaseDatabaseFieldSchema.pick({ id: true, name: true, type: true, property: true })
    .extend({
      name: z.string(),
    })
    .optional(), // returned only when changeType is FIELD_TYPE_CHANGE
  data: z.object({
    previous: CellValueSchema.optional(),
    current: CellValueSchema.optional(),
  }),
});
export type DatabaseRecordChangeContent = z.infer<typeof DatabaseRecordChangeContentSchema>;
export const DatabaseRecordChangeVOSchema = z.object({
  id: z.string(), // record change log id
  user: UserVOSchema.pick({ id: true, name: true, avatar: true }).optional(),
  changeType: z.enum(['UPDATE', 'CREATE', 'FIELD_TYPE_CHANGE'] as const),
  createdAt: z.string().datetime(),
  content: z.array(DatabaseRecordChangeContentSchema),
});
export type DatabaseRecordChangeVO = z.infer<typeof DatabaseRecordChangeVOSchema>;

export const DatabaseRecordChangePaginationVOSchema = BasePaginationInfoSchema.extend({
  data: z.array(DatabaseRecordChangeVOSchema),
});
export type DatabaseRecordChangePaginationVO = z.infer<typeof DatabaseRecordChangePaginationVOSchema>;

export const RecordActivityPaginationVOSchema = z.union([
  RecordCommentPaginationVOSchema,
  DatabaseRecordChangePaginationVOSchema,
]);
export type RecordActivityPaginationVO = z.infer<typeof RecordActivityPaginationVOSchema>;

export const RecordRenderOptsSchema = RenderOptionSchema.extend({
  withTemplateId: z.boolean().optional(),
  withUrl: z.boolean().optional(),
  viewId: z.string().optional(),
  // Only data for fields whose names or IDs are in this list will be included in the result
  fieldIds: z.string().array().optional(),
  // lets you return field objects where the key is the field name, defaults is false, return key is field id
  returnFieldName: z.boolean().optional(),
});
export type RecordRenderOpts = z.infer<typeof RecordRenderOptsSchema>;

export const CellRenderOptsSchema = RenderOptionSchema;
export type CellRenderOpts = z.infer<typeof CellRenderOptsSchema>;

export const OpenAPIRecordRenderOptsSchema = RecordRenderOptsSchema.extend({
  cellFormat: CellFormatSchema.optional().default('json'),
});
export type OpenAPIRecordRenderOpts = z.infer<typeof OpenAPIRecordRenderOptsSchema>;

export const OpenAPICellRenderOptsSchema = CellRenderOptsSchema.extend({
  cellFormat: CellFormatSchema.optional().default('json'),
});
export type OpenAPICellRenderOpts = z.infer<typeof OpenAPICellRenderOptsSchema>;

export interface ILookupVO {
  value: string;
  name: string;
}
