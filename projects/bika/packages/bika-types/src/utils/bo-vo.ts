/* eslint-disable @typescript-eslint/no-use-before-define */
import assert from 'assert';
import { AvatarLogo } from 'basenext/avatar';
import { iStringParse, Locale } from 'basenext/i18n';
import _ from 'lodash';
import {
  ResourceVO,
  BORenderOpts,
  TemplateFolderVO,
  FolderVO,
  NodeTreeVO,
  MirrorVO,
  FormVO,
  FolderDetailVO,
} from '@bika/types/node/vo';
import { createResourceInstance, createResourceProcessor } from './bo-processor';
import { defaultTemplate } from './default-template';
import { Automation } from '../automation/bo';
import { AutomationActionVO, AutomationTriggerVO, AutomationVO } from '../automation/vo';
import { Dashboard } from '../dashboard/bo';
import { DashboardVO, WidgetVO } from '../dashboard/vo';
import { Database, DatabaseField } from '../database/bo';
import { DatabaseVO, FieldVO, CellVO, RecordRenderVO, ViewVO } from '../database/vo';
import { DocumentBO } from '../document/bo';
import type { DocumentVO } from '../document/vo';
import { FormBO, FormMetadataViewType } from '../form/bo';
import { DatabaseViewMirrorBO, Folder, NodeResource, NodeResourceType, NodeResourceTypeSchema } from '../node/bo';
import { NodeCreateDTO, ResourceCreateDTOSchema } from '../node/dto-node-create';
/**
 * the util to convert bo to vo
 *
 * // todo pass template parameter to transfer all template
 */
export function getResourceBO<T extends NodeResource>(resourceType: NodeResourceType): T {
  if (resourceType === 'FOLDER') {
    return {
      resourceType: 'FOLDER',
      templateId: defaultTemplate.templateId,
      name: defaultTemplate.name,
      children: defaultTemplate.resources,
    } as T;
  }
  return defaultTemplate.resources.find((r) => r.resourceType === resourceType) as T;
}

export function getResourceBOByTemplateId<T extends NodeResource>(templateId: string): T {
  return defaultTemplate.resources.find((r) => r.templateId === templateId) as T;
}

export function toResourceVO(resourceType: NodeResourceType, locale: Locale): ResourceVO | null {
  switch (resourceType) {
    case 'TEMPLATE':
      return toTemplateVO(locale);
    case 'FOLDER':
      return toFolderVO(locale);
    case 'DATABASE':
      return toDatabaseVO(locale);
    case 'MIRROR':
      return toMirrorVO(locale);
    case 'AUTOMATION':
      return toAutomationVO(locale);
    case 'FORM':
      return toFormVO(locale);
    case 'DASHBOARD':
      return toDashboardVO(locale);
    case 'DOCUMENT':
      return toDocumentVO(locale);
    default:
      return null;
  }
}

export function defaultRecordsVO(locale: Locale): RecordRenderVO[] {
  const database = getResourceBO<Database>('DATABASE');
  const records = database?.records;
  const vos: RecordRenderVO[] = [];
  if (!records) {
    return vos;
  }
  for (const record of records) {
    const cells: Record<string, CellVO> = {};
    for (const field of database.fields!) {
      if (field.type)
        cells[field.templateId!] = {
          id: field.templateId!,
          name: iStringParse(field.name, locale),
          data: record.data[field.templateId!] || null,
          value: null, // todo
        };
    }
    vos.push({
      id: record.templateId!,
      databaseId: database.templateId!,
      revision: 1,
      cells,
    });
  }
  return vos;
}

function toFormVO(locale: Locale): FormVO | null {
  const form = getResourceBO<FormBO>(NodeResourceTypeSchema.enum.FORM);
  if (!form) {
    return null;
  }
  const databaseVO = toDatabaseVO(locale);
  if (!databaseVO) {
    return null;
  }
  assert(form.formType === 'DATABASE' || form.formType === undefined);
  const view = databaseVO.views.find((o) => o.templateId === (form.metadata as FormMetadataViewType).viewTemplateId);
  if (!view) {
    return null;
  }
  return {
    id: form.templateId!,
    name: iStringParse(form.name, locale),
    cover: form.cover as AvatarLogo,
    brandLogo: form.brandLogo as AvatarLogo,
    description: iStringParse(form.description, locale),
    metadata: form.metadata!,
    databaseId: form.databaseTemplateId!,
    fields: view.columns,
    database: databaseVO,
  };
}

function toAutomationVO(locale: Locale): AutomationVO | null {
  const automation = getResourceBO<Automation>(NodeResourceTypeSchema.enum.AUTOMATION);
  if (!automation) {
    return null;
  }
  const actionsVO = (): AutomationActionVO[] =>
    automation.actions.reduce<AutomationActionVO[]>((acc, action) => {
      acc.push({
        id: action.templateId!,
        type: action.actionType,
        description: iStringParse(action.description, locale),
        bo: action,
        isVerified: false,
      });
      return acc;
    }, []);

  const triggersVO = (): AutomationTriggerVO[] =>
    automation.triggers.reduce<AutomationTriggerVO[]>((acc, trigger) => {
      acc.push({
        id: trigger.templateId!,
        type: trigger.triggerType,
        description: iStringParse(trigger.description, locale),
        bo: trigger,
        isVerified: false,
      });
      return acc;
    }, []);

  return {
    id: automation.templateId!,
    name: iStringParse(automation.name, locale),
    description: iStringParse(automation.description, locale),
    isActive: false,
    actions: actionsVO(),
    triggers: triggersVO(),
    isVerified: false,
  };
}

function toMirrorVO(locale: Locale): MirrorVO | null {
  const mirror = getResourceBO<DatabaseViewMirrorBO>(NodeResourceTypeSchema.enum.MIRROR);
  if (!mirror) {
    return null;
  }
  const databaseVO = toDatabaseVO(locale);
  if (!databaseVO) {
    return null;
  }
  const view = databaseVO.views.find((o) => o.templateId === mirror.viewTemplateId);
  if (!view) {
    return null;
  }
  return {
    id: mirror.templateId!,
    spaceId: '',
    name: iStringParse(mirror.name, locale),
    templateId: mirror.templateId,
    mirrorType: mirror.mirrorType,
    databaseId: mirror.databaseTemplateId!,
    database: databaseVO,
    viewId: mirror.viewTemplateId!,
    view,
  };
}

function toTemplateVO(locale: Locale): TemplateFolderVO | null {
  return {
    id: defaultTemplate.templateId,
    templateId: defaultTemplate.templateId,
    name: iStringParse(defaultTemplate.name, locale),
    type: 'TEMPLATE',
    description: iStringParse(defaultTemplate.description, locale),
    sharing: false,
    hasShareLock: false,
    hasPermissions: false,
    installation: {
      templateId: defaultTemplate.templateId,
      version: defaultTemplate.version,
    },
    author: {
      name: typeof defaultTemplate.author === 'string' ? defaultTemplate.author : 'Template Author',
      avatar: {
        type: 'COLOR',
        color: 'BLUE',
      },
    },
    children: defaultTemplate.resources
      ?.map((i) => {
        const resourceVO = toResourceVO(i.resourceType, locale);
        if (!resourceVO) {
          return null;
        }
        return {
          ...resourceVO,
          type: i.resourceType,
          name: iStringParse(i.name, locale),
        };
      })
      ?.filter((o) => o !== null) as NodeTreeVO[],
  };
}

function toFolderVO(locale: Locale): FolderVO | null {
  const folder = getResourceBO<Folder>(NodeResourceTypeSchema.enum.FOLDER);
  if (!folder) {
    return null;
  }

  return {
    id: folder.templateId!,
    name: iStringParse(folder.name, locale),
    type: folder.resourceType,
    description: iStringParse(folder.description, locale),
    sharing: false,
    hasShareLock: false,
    hasPermissions: false,
    children: folder.children
      ?.map((i) => {
        const resourceVO = toResourceVO(i.resourceType, locale);
        if (!resourceVO) {
          return null;
        }
        return {
          ...resourceVO,
          type: i.resourceType,
          name: iStringParse(i.name, locale),
        };
      })
      ?.filter((o) => o !== null) as NodeTreeVO[],
  };
}
function toDocumentVO(locale: Locale): DocumentVO | null {
  const document = getResourceBO<DocumentBO>(NodeResourceTypeSchema.enum.DOCUMENT);
  if (!document) {
    return null;
  }

  return {
    id: document.id || document.templateId!,
    resourceType: 'DOCUMENT',
    // markdown to json
    // markdownt to html
  };
}

function toDashboardVO(locale: Locale): DashboardVO | null {
  const dashboard = getResourceBO<Dashboard>(NodeResourceTypeSchema.enum.DASHBOARD);
  if (!dashboard) {
    return null;
  }

  return {
    id: dashboard.templateId!,
    templateId: dashboard.templateId!,
    name: iStringParse(dashboard.name, locale),
    description: iStringParse(dashboard.description, locale),
    widgets: dashboard.widgets as unknown as WidgetVO[],
  };
}

function toDatabaseVO(locale: Locale): DatabaseVO | null {
  const database = getResourceBO<Database>(NodeResourceTypeSchema.enum.DATABASE);
  if (!database) {
    return null;
  }

  const toFieldsVO = (fields: DatabaseField[]): FieldVO[] =>
    fields.reduce<FieldVO[]>((acc, field, curIdx) => {
      acc.push({
        id: field.templateId!,
        name: iStringParse(field.name, locale),
        description: iStringParse(field.description, locale),
        type: field.type,
        property: field.property,
        primary: curIdx === 0,
        required: field.required,
        privilege: field.privilege,
        databaseId: database.templateId!,
        templateId: field.templateId,
      } as FieldVO);
      return acc;
    }, []);

  const toViewsVO = (): ViewVO[] => {
    const views = database.views!;
    const fieldMap = _.keyBy(database.fields!, 'templateId');
    return views.reduce<ViewVO[]>((prev, cur, curIdx) => {
      const columns: DatabaseField[] = cur.fields
        ? cur.fields.map((field) => fieldMap[field.templateId!])
        : database.fields!;
      prev.push({
        id: cur.templateId!,
        name: iStringParse(cur.name, locale),
        description: iStringParse(cur.description, locale),
        type: cur.type,
        templateId: cur.templateId,
        databaseId: database.templateId!,
        preViewId: curIdx === 0 ? undefined : views[curIdx - 1].templateId,
        filters: cur.filters,
        columns: toFieldsVO(columns),
      });
      return prev;
    }, []);
  };

  return {
    id: database.templateId!,
    name: iStringParse(database.name, locale),
    spaceId: '',
    views: toViewsVO(),
  };
}

export function bo2vo(
  resources: NodeResource[],
  opts: BORenderOpts,
  newInstance: boolean = true,
): (ResourceVO | FolderDetailVO)[] {
  // create a getInstance function to get the resource by the id or templateId or instanceId
  const getInstance = createResourceInstance(resources, newInstance);
  const vos: (ResourceVO | FolderDetailVO)[] = [];
  for (const resource of resources) {
    const processor = createResourceProcessor(resource);
    // link all related instances to the resource, this will mutate the resource
    processor.processRelatedInstances(getInstance);
    vos.push(processor.toVO(opts));
  }
  return vos;
}

export function bo2CreateDTO(
  resources: NodeResource[],
  opts: {
    spaceId: string;
    parentId: string;

    newInstance?: boolean;
  },
): NodeCreateDTO[] {
  const { spaceId, parentId, newInstance = true } = opts;
  // create a getInstance function to get the resource by the id or templateId or instanceId
  const getInstance = createResourceInstance(resources, newInstance);
  const dtos: NodeCreateDTO[] = [];
  for (const resource of resources) {
    const processor = createResourceProcessor(resource);
    // link all related instances to the resource, this will mutate the resource
    processor.processRelatedInstances(getInstance);
  }

  const loopResources = (initialResources: NodeResource[], curParentId: string) => {
    for (const resource of initialResources) {
      console.log('resource===', resource);
      const createDTO = ResourceCreateDTOSchema.parse({
        ...resource,
        ..._.get(resource, 'metadata', {}),
      });
      dtos.push({
        spaceId,
        parentId: curParentId,
        data: createDTO,
      });
      if (resource.resourceType === 'FOLDER' && (resource as Folder).children) {
        loopResources((resource as Folder).children!, resource.id!);
      }
    }
  };

  loopResources(resources, parentId);
  return dtos;
}
