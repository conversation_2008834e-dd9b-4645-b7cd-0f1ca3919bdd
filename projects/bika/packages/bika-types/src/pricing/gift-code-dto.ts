import { PaginationSchema } from 'basenext/pagination';
import { z } from 'zod';
import { BillingSpecSchema } from './billing';
import { GiftCodeChannelSchema, GiftCodeStatusSchema } from './gift-code';

const GiftCodeListBaseSchema = z.object({
  channel: GiftCodeChannelSchema.optional(),
  status: GiftCodeStatusSchema.optional(),
  code: z.string().optional(),
  sort: z
    .string()
    .refine((value) => ['name', 'createdAt'].includes(value), { message: 'Invalid sort field' })
    .default('createdAt'),
  direction: z
    .string()
    .optional()
    .transform((value) => (value === 'asc' ? value : 'desc')),
});

export const GiftCodeListDTOSchema = GiftCodeListBaseSchema.merge(PaginationSchema);

export type GiftCodeListDTO = z.infer<typeof GiftCodeListDTOSchema>;

export const GiftCodeGenerateDTOSchema = z.object({
  channel: GiftCodeChannelSchema,
  plan: BillingSpecSchema,
  numbers: z.number().optional(),
  codePrefix: z.string().optional(),
});

export type GiftCodeGenerateDTO = z.infer<typeof GiftCodeGenerateDTOSchema>;

export const GiftCodeRedeemDTOSchema = z.object({
  code: z.string(),
});

export type GiftCodeRedeemDTO = z.infer<typeof GiftCodeRedeemDTOSchema>;

export const GiftCodeRedeemWithEmailDTOSchema = GiftCodeRedeemDTOSchema.extend({
  email: z.string().email(),
  verifyCode: z.string(),
});

export type GiftCodeRedeemWithEmailDTO = z.infer<typeof GiftCodeRedeemWithEmailDTOSchema>;
