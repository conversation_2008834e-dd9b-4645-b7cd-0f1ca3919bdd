import { Locale } from 'basenext/i18n';
import type { DocumentCreateDTO, FileCreateDTO } from './dto';

export function defaultDocumentCreateBO(locale: Locale): DocumentCreateDTO {
  return {
    resourceType: 'DOCUMENT',
    name: 'New Document',
  };
}

export function defaultFileNodeCreateBO(locale: Locale): FileCreateDTO {
  return {
    resourceType: 'FILE',
    name: 'New File',
    file: undefined,
  };
}
