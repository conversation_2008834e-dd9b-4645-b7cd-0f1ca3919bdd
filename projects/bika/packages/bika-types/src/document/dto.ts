import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';
// import { FileNodeBODataSchema } from './bo';
import { FileNodeBODataSchema } from './bo';
import { BaseCreateNodeResourceBOSchema, NodeResourceTypeSchema, NodeUpdateBOSchema } from '../node/base';

// === DOCUMENT

export const DocumentCreateDTOSchema = BaseCreateNodeResourceBOSchema.extend({
  resourceType: z.literal(NodeResourceTypeSchema.enum.DOCUMENT),
  // Initial text, using markdown
  markdown: z.string().optional(),
  json: z.any().optional(),
});

export type DocumentCreateDTO = z.infer<typeof DocumentCreateDTOSchema>;

export const DocumentUpdateDTOSchema = NodeUpdateBOSchema.extend({
  resourceType: z.literal(NodeResourceTypeSchema.enum.DOCUMENT),
  name: iStringSchema.optional(),
  description: iStringSchema.optional(),
  // update allows json, html, summary
  summary: z.string().optional(),
  html: z.string().optional(),
  json: z.any().optional(),
  // data: z.ar
});

export type DocumentUpdateDTO = z.infer<typeof DocumentUpdateDTOSchema>;

//  ==== FILE

export const FileCreateDTOSchema = BaseCreateNodeResourceBOSchema.extend({
  resourceType: z.literal(NodeResourceTypeSchema.enum.FILE),
  file: FileNodeBODataSchema.optional(),
});

export type FileCreateDTO = z.infer<typeof FileCreateDTOSchema>;

export const FileUpdateDTOSchema = NodeUpdateBOSchema.extend({
  resourceType: z.literal(NodeResourceTypeSchema.enum.FILE),
  name: iStringSchema.optional(),
  description: iStringSchema.optional(),
  file: FileNodeBODataSchema.optional(),
});

export type FileUpdateDTO = z.infer<typeof FileUpdateDTOSchema>;
