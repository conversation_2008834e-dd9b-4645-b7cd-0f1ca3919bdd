import { z } from 'zod';
import { AvatarLogoSchema } from 'basenext/avatar';
import { DisplaySchema } from './bo-toolsdk-base';
import { ZapierCreateSchema, ZapierTriggerSchema } from './bo-zapier';

export const ActionDataTypeSchema = z.enum(['JSON', 'NPM']);

export const TriggerTemplateSchema = ZapierTriggerSchema.extend({});
export type TriggerTemplate = z.infer<typeof TriggerTemplateSchema>;

/**
 *
 * Formapp.ai's action = Zapier create and search
 *
 */
export const ActionTemplateSchema = ZapierCreateSchema.extend({
  type: z.enum(['create', 'search']),

  logo: AvatarLogoSchema.nullish(),

  // The difference with Zapier, this is optional
  noun: z.string().optional(),

  display: DisplaySchema,

  output: z
    .discriminatedUnion('type', [
      z.object({
        type: z.literal('json'),
      }),
      z.object({
        type: z.literal('image'),
        url: z.string().url(),
      }),
    ])
    .optional(),
});
export type ActionTemplate = z.infer<typeof ActionTemplateSchema>;

export const ActionInputDataBOSchema = z.record(z.unknown());
export type ActionInputData = z.infer<typeof ActionInputDataBOSchema>;
