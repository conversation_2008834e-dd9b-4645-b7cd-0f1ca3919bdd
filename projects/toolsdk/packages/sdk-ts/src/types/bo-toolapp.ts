import * as z from 'zod';
import { AvatarLogoSchema } from 'basenext/avatar';
import { ToolAppAuthenticationSchema } from './bo-configuration';
import { ActionTemplateSchema, TriggerTemplateSchema } from './bo-tool';
import { DisplaySchema } from './bo-toolsdk-base';

export const ToolAppSchema = z.object({
  key: z.string(),
  version: z.string().optional(),

  logo: AvatarLogoSchema.nullish(),

  display: DisplaySchema,

  authentication: ToolAppAuthenticationSchema.optional(),
  triggers: z.record(TriggerTemplateSchema).optional(),

  // ToolApp supports actions that include searches and creates, and also supports writing them separately
  tools: z.record(ActionTemplateSchema).optional(),

  // zapier compatible
  searches: z.record(ActionTemplateSchema).optional(),
  creates: z.record(ActionTemplateSchema).optional(),
});

export type ToolApp = z.infer<typeof ToolAppSchema>;

export const BikaPluginSchema = z.object({
  node: z
    .object({
      resources: z.array(z.any()),
    })
    .optional(),

  database: z
    .object({
      fields: z.array(z.any()),
    })
    .optional(),

  dashboard: z
    .object({
      widgets: z.array(z.any()),
    })
    .optional(),

  automation: z
    .object({
      triggers: z.array(z.any()),
      actions: z.array(ActionTemplateSchema),
    })
    .optional(),
});
