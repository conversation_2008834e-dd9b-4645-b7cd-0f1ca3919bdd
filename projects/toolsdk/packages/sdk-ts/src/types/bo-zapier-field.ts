import { z } from '@hono/zod-openapi';
import { iStringSchema } from 'basenext/i18n';
import { ZapierFunctionSchema } from './bo-zapier-function';

export const InputFieldTypeSchema = z.enum([
  'string',
  'text',
  'integer',
  'number',
  'boolean',
  'datetime',
  'file',
  'password',
  'copy',
  'code',
  'select',
  'array',

  // 'json',
]);
export type InputFieldTypeBO = z.infer<typeof InputFieldTypeSchema>;

export const RefResourceSchema = z.string();
export type RefResource = z.infer<typeof RefResourceSchema>;

export const FieldChoiceWithLabelSchema = z
  .object({
    value: z
      .string()
      .describe(
        'The actual value that is sent into the Zap. This is displayed as light grey text in the editor. Should match sample exactly.',
      ),
    sample: z
      .string()
      .describe(
        'A legacy field that is no longer used by the editor, but it is still required for now and should match the value.',
      ),
    label: z.string().describe('A human readable label for this value.'),
    // [k: string]: z.unknown()
  })
  .and(z.record(z.unknown()));
export type FieldChoiceWithLabel = z.infer<typeof FieldChoiceWithLabelSchema>;

export const FieldChoicesSchema = z.union([
  z.record(z.unknown()),
  z.array(
    z.union([
      z.string(),
      z
        .lazy(() => FieldChoiceWithLabelSchema)
        .openapi({
          type: 'object',
          description: 'FieldChoiceWithLabel',
        }),
    ]),
  ),
]);
export type FieldChoices = z.infer<typeof FieldChoicesSchema>;

export const InputFieldSchema = z.object({
  key: z.string(),
  label: iStringSchema.optional(),
  helpText: iStringSchema.optional(),
  type: InputFieldTypeSchema.optional(), // default: string
  required: z.boolean().optional(), // default: false
  placeholder: z.string().optional(),
  default: z.string().optional(),
  primary: z.boolean().optional(),
  dynamic: RefResourceSchema.optional(),
  search: RefResourceSchema.optional(),
  choices: FieldChoicesSchema.optional(),
  list: z.boolean().optional(),
  // children: z.array(z.lazy(() => InputFieldSchema)).optional(),
  dict: z.boolean().optional(),
  computed: z.boolean().optional(),
  altersDynamicFields: z.boolean().optional(),
  steadyState: z.boolean().optional(),
  inputFormat: z.string().optional(),
});
export type InputFieldBO = z.infer<typeof InputFieldSchema>;

export const ZapierDynamicFieldSchema = InputFieldSchema.or(ZapierFunctionSchema);
export type ZapierDynamicField = z.infer<typeof ZapierDynamicFieldSchema>;
