import { z } from 'zod';
import { AvatarLogoSchema } from 'basenext/avatar';
import { BasePaginationInfoSchema } from 'basenext/pagination';
import { ComponentDataBOSchema, InputFieldSchema, ZapierAuthenticationTypeEnumSchema } from './bo';

const BaseComponentVOSchema = z.object({
  key: z.string(),
  name: z.string(),
  description: z.string().optional(),
  logo: AvatarLogoSchema.nullish(),
  dependencies: z.array(z.string()).optional(),
  stars: z.number().optional(),
});
export type BaseComponentVO = z.infer<typeof BaseComponentVOSchema>;

// ================== CONFIGURATION Template ==================
// export const ToolAppConfigurationTemplateVOSchema = BaseComponentVOSchema.extend({
export const ToolAppConfigurationTemplateVOSchema = z.object({
  kind: z.literal('CONFIGURATION'),
  type: ZapierAuthenticationTypeEnumSchema,
  inputFields: z.array(InputFieldSchema).optional(),
});
export type ToolAppConfigurationTemplateVO = z.infer<typeof ToolAppConfigurationTemplateVOSchema>;

/**
 * VO containing detailed information, used for displaying to the creator.
 */
export const ToolAppConfigurationTemplateDetailVOSchema = ToolAppConfigurationTemplateVOSchema.extend({
  componentData: ComponentDataBOSchema,
});
export type ToolAppConfigurationTemplateDetailVO = z.infer<typeof ToolAppConfigurationTemplateDetailVOSchema>;

// ================== Action Template ==================
// This VO will also be displayed externally along with the ComponentVO component
export const ActionTemplateVOSchema = BaseComponentVOSchema.extend({
  kind: z.literal('TOOL'),
  // actionKey: z.string(),
  inputFields: z.array(InputFieldSchema),
});
export type ActionTemplateVO = z.infer<typeof ActionTemplateVOSchema>;

// ================== Component ==================
export const ComponentInfoVOSchema = z.discriminatedUnion('kind', [
  ActionTemplateVOSchema,
  // ToolAppConfigurationTemplateVOSchema,
  BaseComponentVOSchema.extend({
    kind: z.literal('TRIGGER'),
  }),
  BaseComponentVOSchema.extend({
    kind: z.literal('WIDGET'),
  }),
  BaseComponentVOSchema.extend({
    kind: z.literal('RESOURCE'),
  }),
]);
// TODO: Should we reuse the TemplateCard UI component here? If so, update it to match the TemplateCardVO.
export type ComponentInfoVO = z.infer<typeof ComponentInfoVOSchema>;

export const ComponentPageVOSchema = BasePaginationInfoSchema.extend({
  data: z.array(BaseComponentVOSchema),
});
export type ComponentPageVO = z.infer<typeof ComponentPageVOSchema>;
