import { iStringSchema } from 'basenext/i18n';
import { z } from 'zod';

// export const PackageClientsTypes = ['TOOLSDK_AI', 'CLAUDE', 'CURSOR', 'BIKA_AI'] as const;
// export const PackageClientTypeSchema = z.enum(PackageClientsTypes);
// export type PackageClientType = z.infer<typeof PackageClientTypeSchema>;

export const PackageClientSchema = z.object({
  key: z.string(),
  name: iStringSchema,
  content: z.string().optional(),
  contentMode: z.enum(['MCP_SERVER', 'CUSTOM']).optional(),
  more: z.boolean().optional(),
});
export type PackageClient = z.infer<typeof PackageClientSchema>;
