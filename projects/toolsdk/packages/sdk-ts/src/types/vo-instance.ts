import { z } from 'zod';
import { BasePaginationInfoSchema } from 'basenext/pagination';
import { ActionInputDataBOSchema } from './bo';
import { PackageDetailVOSchema } from './vo-package';

export const PackageInstanceVOSchema = z.object({
  instanceId: z.string(),
  accountKey: z.string().optional(),
  consumerKey: z.string().optional(),

  package: PackageDetailVOSchema.optional(),
  toolKey: z.string().optional(),

  configurationInstanceId: z.string().optional(),
  // fields input data for component
  inputData: ActionInputDataBOSchema,
});
export type PackageInstanceVO = z.infer<typeof PackageInstanceVOSchema>;

// ================== Configuration Instance ==================
export const ConfigurationInstanceVOSchema = z.object({
  id: z.string(),
  connectionLabel: z.string().optional(),
});
export type ConfigurationInstanceVO = z.infer<typeof ConfigurationInstanceVOSchema>;

export const ConfigurationInstancePageVOSchema = BasePaginationInfoSchema.extend({
  data: z.array(ConfigurationInstanceVOSchema),
});
export type ConfigurationInstancePageVO = z.infer<typeof ConfigurationInstancePageVOSchema>;

export const OAuthAuthorizationVOSchema = z.object({
  authorizationUrl: z.string(),
});
export type OAuthAuthorizationVO = z.infer<typeof OAuthAuthorizationVOSchema>;
