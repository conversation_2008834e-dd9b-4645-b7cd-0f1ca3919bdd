import { z } from 'zod';
import { CommonSearchDTOSchema, PaginationSchema } from 'basenext/pagination';

// Query Packages scope
export const PackageListRequestScopeSchema = z.enum([
  'ALL_VALIDATED',
  'ALL_PUBLIC',
  'ALL_PUBLIC_WITH_DEVELOPER_PRIVATE',
  'DEVELOPER_STAR',
  'DEVELOPER_ALL',
  'DEVELOPER_PUBLIC',
  'DEVELOPER_PRIVATE',
]);
export type PackageListRequestScope = z.infer<typeof PackageListRequestScopeSchema>;

// ======= package =======
export const PackageParamsSchema = z.object({
  packageKey: z.string(),
  packageVersion: z.string().optional(),
});
export type PackageParams = z.infer<typeof PackageParamsSchema>;

export const PackagePaginationQuerySchema = CommonSearchDTOSchema.partial().extend({
  scope: PackageListRequestScopeSchema.optional().describe('Default: ALL_PUBLIC'),
});
export type PackagePaginationQuery = z.infer<typeof PackagePaginationQuerySchema>;

export const InputDataBodySchema = z.object({
  inputData: z.record(z.unknown()).optional(),
});
export type InputDataBody = z.infer<typeof InputDataBodySchema>;

export const PackageToolDynamicFieldsRunBodySchema = InputDataBodySchema.extend({
  toolKey: z.string(),
});
export type PackageToolDynamicFieldsRunBody = z.infer<typeof PackageToolDynamicFieldsRunBodySchema>;

export const PackageToolRunBodyWithConfigSchema = PackageToolDynamicFieldsRunBodySchema.extend({
  configurationInstanceId: z.string().optional(),
});
export const PackageToolRunBodySchema = PackageToolRunBodyWithConfigSchema.or(PackageToolDynamicFieldsRunBodySchema);
export type PackageToolRunBody = z.infer<typeof PackageToolRunBodySchema>;

export const PackageToolRunBodyDTOSchema = PackageToolRunBodySchema.and(
  z.object({
    envs: z.record(z.string()).optional(),
  }),
);
export type PackageToolRunBodyDTO = z.infer<typeof PackageToolRunBodyDTOSchema>;

// ======= package instance =======
export const PackageInstanceRunBodySchema = z.object({
  // User business system, runtime context information passed to toolsdk.ai
  context: z.unknown().optional(),
});
export type PackageInstanceRunBody = z.infer<typeof PackageInstanceRunBodySchema>;

export const PackageInstanceUpdateBodySchema = PackageToolRunBodyWithConfigSchema.partial().extend({
  packageKey: z.string().optional(),
  packageVersion: z.string().optional(),
});
export type PackageInstanceUpdateBody = z.infer<typeof PackageInstanceUpdateBodySchema>;

export const PackageInstanceCreateBodySchema = PackageToolRunBodyWithConfigSchema.partial().extend({
  consumerKey: z.string().optional(),
});
export type PackageInstanceCreateBody = z.infer<typeof PackageInstanceCreateBodySchema>;

export const AccountPackageConsumerPutBodySchema = PackageInstanceUpdateBodySchema;
export type AccountPackageConsumerPutBody = z.infer<typeof AccountPackageConsumerPutBodySchema>;

// ======= configuration instance =======
export const ConfigurationInstancePaginationQuerySchema = PaginationSchema.partial();
export type ConfigurationInstancePaginationQuery = z.infer<typeof ConfigurationInstancePaginationQuerySchema>;

export const ConfigurationAuthorizeUrlCreateBodySchema = InputDataBodySchema;
export type ConfigurationAuthorizeUrlCreateBody = z.infer<typeof ConfigurationAuthorizeUrlCreateBodySchema>;

export const ConfigurationInstanceCreateBodySchema = ConfigurationAuthorizeUrlCreateBodySchema;
export type ConfigurationInstanceCreateBody = z.infer<typeof ConfigurationInstanceCreateBodySchema>;

export const ConfigurationInstanceUpdateBodySchema = z.object({
  connectionLabel: z.string(),
});
export type ConfigurationInstanceUpdateBody = z.infer<typeof ConfigurationInstanceUpdateBodySchema>;
