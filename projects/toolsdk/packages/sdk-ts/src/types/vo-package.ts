import { z } from 'zod';
import { AvatarLogoSchema } from 'basenext/avatar';
import { BasePaginationInfoSchema } from 'basenext/pagination';
import { PackageClientSchema } from './bo-package-client';
import { PackageDataTypesSchema } from './bo-package-data';
import { ActionTemplateVOSchema, ToolAppConfigurationTemplateVOSchema } from './vo-template';

export const PackageSimpleVOSchema = z.object({
  // fullSlug = key + version
  packageType: PackageDataTypesSchema,
  key: z.string(),
  version: z.string(),

  url: z.string().optional(),
  name: z.string(),
  description: z.string(),
  // overview: z.string(),
  logo: AvatarLogoSchema.nullish(),
  validated: z.boolean().optional(),

  stars: z.number(),
  updatedAt: z.string().datetime(),
});
export type PackageSimpleVO = z.infer<typeof PackageSimpleVOSchema>;

export const PackagePageVOSchema = BasePaginationInfoSchema.extend({
  data: z.array(PackageSimpleVOSchema),
});
export type PackagePageVO = z.infer<typeof PackagePageVOSchema>;

export const PackageClientVOSchema = PackageClientSchema.extend({
  name: z.string(),
});
export type PackageClientVO = z.infer<typeof PackageClientVOSchema>;

export const PackageDetailVOSchema = PackageSimpleVOSchema.extend({
  configuration: ToolAppConfigurationTemplateVOSchema.optional(),
  tools: z.array(ActionTemplateVOSchema),
  clients: z.array(PackageClientVOSchema),
  isStarred: z.boolean(),
  creator: z.object({ avatar: AvatarLogoSchema, name: z.string() }),
  readme: z.string().optional(),
});
export type PackageDetailVO = z.infer<typeof PackageDetailVOSchema>;
