import type React from 'react';
import { z } from 'zod';
import { AvatarLogoSchema } from 'basenext/avatar';
import { DisplaySchema } from './bo-toolsdk-base';
import { ZapierAuthenticationSchema } from './bo-zapier-auth';

export const ToolAppAuthenticationSchema = ZapierAuthenticationSchema;
export type IntegrationTemplate = z.infer<typeof ToolAppAuthenticationSchema>;

export const WidgetTemplateSchema = z.object({
  key: z.string(),

  logo: AvatarLogoSchema.nullish(),

  display: DisplaySchema,

  code: z.string().or(z.custom<(props: { value: unknown }) => React.JSX.Element>()),
});
export type WidgetTemplate = z.infer<typeof WidgetTemplateSchema>;
