:root[data-theme='light'] {
  --workdocSelectionBg: rgba(123, 103, 238, 0.2);
}

:root[data-theme='dark'] {
  --workdocSelectionBg: rgba(144, 127, 240, 0.16);
}

@keyframes imageLoadingRotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.document-editor .ProseMirror {
  min-width: 400px;
  min-height: 100%;
  color: var(--text-primary);
  font-size: 14px;
  outline: none;
  ::selection {
    background-color: var(--workdocSelectionBg);
  }
}

.document-editor .ProseMirror .is-empty::before {
  color: var(--text-disabled);
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

.document-editor .ProseMirror a {
  color: var(--textLinkDefault);
  cursor: pointer;
  &:hover {
    color: var(--textLinkHover);
  }
  &:active {
    color: var(--textLinkActive);
  }
  & * {
    pointer-events: none;
  }
}
.document-editor .ProseMirror p,
.document-editor .ProseMirror h1,
.document-editor .ProseMirror h2,
.document-editor .ProseMirror h3,
.document-editor .ProseMirror h4,
.document-editor .ProseMirror h5,
.document-editor .ProseMirror h6 {
  min-height: 24px;
  line-height: 24px;
  padding: 4px;
  margin: 0;
  transition: background-color 0.5s ease-in-out;
}
.document-editor .ProseMirror h1,
.document-editor .ProseMirror h2,
.document-editor .ProseMirror h3,
.document-editor .ProseMirror h4,
.document-editor .ProseMirror h5,
.document-editor .ProseMirror h6 {
  font-weight: 600;
}
.document-editor .ProseMirror h1 {
  margin-top: 20px;
  font-size: 28px;
  line-height: 42px;
}
.document-editor .ProseMirror h2 {
  margin-top: 16px;
  font-size: 24px;
  line-height: 36px;
}
.document-editor .ProseMirror h3 {
  margin-top: 12px;
  font-size: 20px;
  line-height: 30px;
}
.document-editor .ProseMirror h4 {
  margin-top: 8px;
  font-size: 18px;
  line-height: 28px;
}
.document-editor .ProseMirror h5,
.document-editor .ProseMirror h6 {
  font-size: 16px;
  line-height: 24px;
}
.document-editor .ProseMirror ol {
  margin-bottom: 0;
  padding-left: 16px;
  list-style-type: none;
  counter-reset: num;
  li {
    min-height: 24px;
    margin: 0 4px;
    position: relative;
    counter-increment: num;
    &::before {
      color: var(--brand);
      content: counter(num) '.';
      position: absolute;
      padding: 4px 0;
      height: 32px;
      right: 100%;
    }
    & > ol {
      list-style-type: none;
      counter-reset: count;
      & > li {
        counter-increment: count;
        &::before {
          content: counter(count, lower-alpha) '.';
        }
        & > ol {
          list-style-type: none;
          counter-reset: listCounter;
          & > li {
            counter-increment: listCounter;
            &::before {
              content: counter(listCounter, upper-roman) '.';
            }
          }
        }
      }
    }
  }
}
.document-editor .ProseMirror ul {
  margin: 0;
  padding-left: 16px;
  list-style-position: inside;
  list-style-type: none;
  li {
    min-height: 24px;
    margin: 0 4px;
    position: relative;
  }
}

.document-editor .ProseMirror ul:not([data-type='taskList']) {
  & > li {
    &::before {
      content: '';
      position: absolute;
      top: 14px;
      left: -14px;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: var(--brand);
    }
    ul li::before {
      background-color: transparent;
      border: 1px solid var(--brand);
    }
    ul ul li::before {
      border-radius: 1px;
      background-color: var(--brand);
      top: 15px;
      width: 5px;
      height: 5px;
    }
  }
}
.document-editor .ProseMirror ul[data-type='taskList'] {
  padding-left: 0;
  li {
    display: flex;
    padding: 0;
    align-items: center;
    label {
      margin-right: 8px;
      user-select: none;
      align-self: flex-start;
      padding-top: 8px;
      position: relative;
    }
    &[data-checked='true'] {
      text-decoration: line-through;
    }
    input {
      display: block;
      width: 16px;
      height: 16px;
      &[type='checkbox'] {
        opacity: 0;
        position: absolute;
        & + span {
          position: relative;
          top: 0;
          left: 0;
          cursor: pointer;
          display: block;
          width: 16px;
          height: 16px;
          background-color: var(--bg-surface);
          border: 1.5px solid var(--text-secondary);
          border-radius: 4px;
          transition: all 0.36s;
        }
        &:checked + span {
          background-color: var(--brand);
          border-color: var(--brand);
          &:after {
            position: absolute;
            top: 45%;
            left: 20%;
            width: 5px;
            height: 10px;
            display: table;
            border: 2px solid var(--white);
            border-top: 0;
            border-left: 0;
            transform: rotate(45deg) scale(1) translate(-50%, -50%);
            opacity: 1;
            transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s;
            content: ' ';
          }
        }
      }
    }
    & > div {
      min-width: 100px;
    }
  }
}
.document-editor .ProseMirror code {
  background-color: var(--bg-controls);
  color: var(--text-secondary);
  padding: 2px 4px;
  border-radius: 2px;
}
.document-editor .ProseMirror mark {
  padding: 0.15em 0;
}
.document-editor .ProseMirror pre {
  margin: 8px 4px;
  padding: 16px;
  border-radius: 4px;
  background-color: var(--bg-controls);
  overflow-y: auto;
  display: block;
  &.is-empty::before {
    padding-left: 8px;
  }
  code {
    color: var(--text-secondary);
    background: none;
    white-space: pre-wrap;
    padding: 0;
  }
}
.document-editor .ProseMirror hr {
  border-top: 1px solid var(--border-default);
  border-bottom: none;
  border-left: none;
  border-right: none;
}
.document-editor .ProseMirror blockquote {
  margin: 4px 0;
  border-left: 2px solid var(--rainbow-gray2);
  padding-left: 14px;
  padding-right: 4px;
  min-height: 24px;
  color: var(--text-secondary);
}
.document-editor .ProseMirror s {
  text-decoration: line-through;
  span {
    text-decoration: line-through;
  }
}
.document-editor .ProseMirror u {
  text-decoration: underline;
  span {
    text-decoration: underline;
  }
}

.document-editor .ProseMirror .img-placeholder {
  position: relative;
  display: inline-block;
  .preview-img {
    opacity: 0.4;
    max-width: 100%;
  }
  .image-loading-container {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    top: 16px;
    z-index: 1;
    right: 16px;
    border-radius: 4px;
    padding: 6px 6px 6px 12px;
    background-color: var(--bg-surface);
    border: 1px solid var(--border-default);
  }
  .image-loading-icon {
    top: 24px;
    z-index: 1;
    right: 84px;
    width: 16px;
    height: 16px;
    border: 2px solid var(--brand);
    border-bottom-color: transparent;
    border-radius: 50%;
    display: inline-block;
    box-sizing: border-box;
    animation: imageLoadingRotation 1s linear infinite;
  }
  .image-loading-content {
    font-size: 13px;
    color: var(--text-primary);
  }
}

.document-editor .ProseMirror .video-placeholder {
  position: relative;
  display: inline-block;
  .preview-video {
    opacity: 0.4;
    max-width: 100%;
  }
  .video-loading-container {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    top: 16px;
    z-index: 1;
    right: 16px;
    border-radius: 4px;
    padding: 6px 6px 6px 12px;
    background-color: var(--bg-surface);
    border: 1px solid var(--border-default);
  }
  .video-loading-icon {
    top: 24px;
    z-index: 1;
    right: 84px;
    width: 16px;
    height: 16px;
    border: 2px solid var(--brand);
    border-bottom-color: transparent;
    border-radius: 50%;
    display: inline-block;
    box-sizing: border-box;
    animation: imageLoadingRotation 1s linear infinite;
  }
  .video-loading-content {
    font-size: 13px;
    color: var(--text-primary);
  }
}

.document-editor .ProseMirror .video-wrapper {
  position: relative;
  pointer-events: none;
  .video-actions {
    display: none;
    position: absolute;
    padding: 4px;
    top: -40px;
    left: calc(50% - 20px);
    border-radius: 4px;
    border: 1px solid var(--border-default);
    background: var(--bg-popup);
  }
}

.document-editor .ProseMirror .video-selected {
  outline: var(--brand) solid 2px;
  line-height: 0;
  video {
    pointer-events: auto;
  }
  .video-actions {
    pointer-events: auto;
    display: flex;
  }
  a {
    display: flex;
    width: 24px;
    height: 24px;
    align-items: center;
    justify-content: center;
    &:hover {
      border-radius: 4px;
      background-color: var(--selected);
    }
    svg {
      fill: var(--text-primary);
    }
  }
}

.document-editor .ProseMirror .collaboration-cursor__caret {
  border-left: 1px solid var(--brand);
  border-right: 1px solid var(--brand);
  margin-left: 1px;
  margin-right: 1px;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    left: -4px;
    top: -6px;
    border: 4px solid;
    border-color: inherit;
  }
  &:hover {
    .collaboration-cursor__label {
      opacity: 1;
    }
  }
}

.document-editor .ProseMirror .node-image {
  display: flex;
  margin-bottom: 4px;
  margin-top: 4px;
}

.document-editor .ProseMirror .image-resizer {
  display: inline-flex;
  position: relative;
  flex-grow: 0;
  pointer-events: none;
  .image-align {
    display: flex;
    position: absolute;
    padding: 4px;
    top: -40px;
    left: calc(50% - 60px);
    border-radius: 4px;
    border: 1px solid var(--border-default);
    background: var(--bg-popup);
    & > div {
      padding: 4px;
      margin-right: 4px;
      display: flex;
      align-items: center;
      &:last-child {
        margin-right: 0;
      }
    }
    svg {
      fill: var(--text-primary);
    }
  }
  .resize-trigger,
  .image-align {
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  @media screen and (max-width: 768px) {
    img {
      width: 100%;
      height: auto;
    }
  }
}

.document-editor .ProseMirror .image-selected {
  &.image-resizer {
    border: 2px solid var(--brand);
    pointer-events: auto;
    cursor: pointer;
    .resize-trigger {
      opacity: 1;
      pointer-events: auto;
      .resize-trigger-top-left,
      .resize-trigger-top-right,
      .resize-trigger-bottom-left,
      .resize-trigger-bottom-right {
        width: 12px;
        height: 12px;
        background-color: var(--bg-popup);
        border: 2px solid var(--brand);
        border-radius: 8px;
        position: absolute;
      }
      .resize-trigger-top-left {
        cursor: nwse-resize;
        top: -6px;
        left: -6px;
      }
      .resize-trigger-top-right {
        cursor: nesw-resize;
        top: -6px;
        right: -6px;
      }
      .resize-trigger-bottom-left {
        cursor: nesw-resize;
        bottom: -6px;
        left: -6px;
      }
      .resize-trigger-bottom-right {
        cursor: nwse-resize;
        bottom: -6px;
        right: -6px;
      }
    }
    .image-align {
      opacity: 1;
      cursor: pointer;
      pointer-events: auto;
      & > div {
        &.active {
          svg {
            fill: var(--brand);
          }
        }
        &.active {
          border-radius: 4px;
          background-color: var(--selected);
        }
        &:hover {
          border-radius: 4px;
          background-color: var(--hover);
        }
      }
    }
  }
}

.document-editor .ProseMirror .collaboration-cursor__label {
  opacity: 0;
  animation: fade-in 0.2s ease-in-out;
  border-radius: 2px 2px 2px 0;
  font-size: 12px;
  font-weight: 600;
  left: -1px;
  line-height: normal;
  padding: 2px 6px;
  position: absolute;
  top: -20px;
  white-space: nowrap;
  pointer-events: none;
  color: var(--static);
}

.document-editor .ProseMirror .heading-flash {
  background-color: var(--selected);
  border-radius: 4px;
}

.document-editor .ProseMirror .mermaid-editor {
  margin: 8px 0;
  border-radius: 4px;
  overflow: hidden;
}

.document-editor .ProseMirror .mermaid-editor p {
  padding: 0;
}

.document-editor .ProseMirror .mermaid-preview {
  padding: 8px;
  display: flex;
  justify-content: center;
}

.document-editor .ProseMirror .mermaid-code {
  width: 100%;
  border-bottom: 1px solid var(--border-default);
  padding: 4px;
  font-family: monospace;
  resize: vertical;
}

.document-editor .ProseMirror .mermaid-error {
  color: var(--status-danger);
  padding: 8px;
}

.document-editor .ProseMirror div[data-youtube-video] {
  border-radius: 8px;
  overflow: hidden;
}

.document-editor .ProseMirror div[data-youtube-video].ProseMirror-selectednode {
  border: 2px solid var(--brand);
}