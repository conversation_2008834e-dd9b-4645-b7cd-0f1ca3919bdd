import type { Hocus<PERSON>cusProvider } from '@hocuspocus/provider';
import type { Editor, EditorEvents } from '@tiptap/core';
import Collaboration from '@tiptap/extension-collaboration';
import CollaborationCursor from '@tiptap/extension-collaboration-cursor';
import { EditorContent, useEditor } from '@tiptap/react';
import isHotkey from 'is-hotkey';
import _ from 'lodash';
import React, { useRef } from 'react';
import type * as Y from 'yjs';
import type { ILocaleContext } from '@bika/contents/i18n';
import type { AttachmentVO } from '@bika/types/attachment/vo';
import type { UserVO } from '@bika/types/user/vo';
import { BubbleMenu } from './bubble-menu';
import { defaultExtensions } from './default-extensions';
import { DocumentEditorExtentions } from './doc-extensions';
import { FloatingMenu } from './floating-menu';
import { TableOfContents } from './table-of-contents';
import { uploadImage } from './utils/upload-image';
import { uploadVideo } from './utils/upload-video';
import { getAvatarRandomColor } from '../../components/avatar/avatar-hoc';
import { Box } from '../../layout-components';
import './styles.css';

const { debounce } = _;
const DRAG_THRESHOLD = 10;
const PREVENT_KEYS = ['mod+b', 'mod+i'];

// Importing the provider and useEffect

export type DocumentEditorOnChangeHandler = (yDoc: Y.Doc, editor: Editor) => void;

interface Props {
  value: Y.Doc;
  onChange: DocumentEditorOnChangeHandler;
  // 上传媒体文件后的公开访问端点地址, 如果你不需要改组件支持上传媒体文件，设置空字符串
  filePublicUrl: string;
  onUpload: (file: File) => Promise<AttachmentVO>;
  id: string;
  provider: HocuspocusProvider | null;
  user?: UserVO;
  editable?: boolean;
  locale: ILocaleContext;
}

/**
 * 协同文档的UI组件
 *
 * 传入Y.js doc对象作为编辑源的“文档”，外边可以灵活的使用TiptapCloud、自定义组件、BikaServer协同等
 *
 * @param props
 * @returns
 */
export function DocumentEditor(props: Props) {
  const { value: doc, id, provider, user, editable = true, locale, filePublicUrl, onUpload } = props;
  const ref = useRef<HTMLDivElement>(null);
  const mouseDownRef = useRef<{ x: number; y: number }>();
  const isDragRef = useRef(false);
  const [contentStyle, setContentStyle] = React.useState<React.CSSProperties>({});

  const debouncedUpdate = React.useCallback(
    debounce(({ editor }: EditorEvents['update']) => {
      props.onChange(doc, editor);
    }, 500),
    [],
  );

  // 监听 resize 事件, document-editor 的宽度变化时，重新计算 table of contents
  React.useEffect(() => {
    const docContent = document.querySelector('#document-content');
    // 创建一个 ResizeObserver 实例
    const resizeObserver = new ResizeObserver((entries) => {
      // biome-ignore lint/complexity/noForEach: <explanation>
      entries.forEach((entry) => {
        // 获取元素的新宽度
        const contentWidth = entry.contentRect.width;
        if (contentWidth < 1300 && contentWidth > 1020) {
          setContentStyle({
            marginLeft: '240px',
          });
        } else if (contentWidth && contentWidth < 1020) {
          setContentStyle({
            marginRight: '0',
            width: 'calc(100% - 232px)',
          });
        } else {
          setContentStyle({});
        }
      });
    });

    // 监听每个元素
    if (docContent) {
      resizeObserver.observe(docContent);
    }
  }, []);

  const editor = useEditor({
    extensions: [
      ...defaultExtensions(locale),
      ...DocumentEditorExtentions,
      Collaboration.configure({
        document: doc,
      }),
      CollaborationCursor.configure({
        provider,
        user: {
          name: user?.name || '',
          id: user?.id || '',
          color: getAvatarRandomColor(user?.id || ''),
        },
      }),
    ],
    onUpdate: (e) => {
      debouncedUpdate(e);
    },
    editable,
  });

  // 确保在 editable 属性变化时更新编辑器实例
  React.useEffect(() => {
    if (editor) {
      editor.setEditable(editable);
    }
  }, [editable, editor]);

  const handlePaste = (e: React.ClipboardEvent) => {
    if (!editor || !editable) return;

    const { items } = e.clipboardData;
    for (const item of items) {
      const file = item.getAsFile();
      if (file instanceof File) {
        if (file.type.startsWith('image')) {
          uploadImage(editor, file, filePublicUrl, onUpload);
          e.preventDefault();
        } else if (file.type.startsWith('video')) {
          uploadVideo(editor, file, filePublicUrl, onUpload);
          e.preventDefault();
        }
      }
    }
  };

  return (
    <Box
      sx={{
        borderRadius: '4px',
        backgroundColor: 'var(--bg-surface)',
        px: 1,
        position: 'relative',
      }}
      className="document-editor"
    >
      <Box
        sx={{
          height: 'calc(100vh - 64px)',
          overflowY: 'auto',
        }}
        id="document-content"
      >
        <Box
          ref={ref}
          sx={{
            width: '840px',
            margin: '12px auto',
            minHeight: 'calc(100% - 24px)',
            ...contentStyle,
          }}
          onKeyDown={(e) => {
            const isPrevent = PREVENT_KEYS.some((key) => isHotkey(key, e));
            if (isPrevent) {
              e.preventDefault();
              e.stopPropagation();
            }
            if (e.key === 'Tab') {
              e.preventDefault();
              e.stopPropagation();
              const isListSection =
                editor?.isActive('taskList') || editor?.isActive('bulletList') || editor?.isActive('orderedList');
              if (!isListSection) {
                editor?.chain().insertContent('    ').run();
              }
            }
            if (e.key === 'Enter') {
              editor?.chain().focus().unsetTextAlign().run();
            }
          }}
          onMouseDown={(e) => {
            mouseDownRef.current = { x: e.clientX, y: e.clientY };
          }}
          onMouseMove={(e) => {
            const { x, y } = mouseDownRef.current || {};
            if (x && y && (Math.abs(e.clientX - x) > DRAG_THRESHOLD || Math.abs(e.clientY - y) > DRAG_THRESHOLD)) {
              isDragRef.current = true;
            }
          }}
          onMouseUp={(e) => {
            mouseDownRef.current = undefined;
            if (isDragRef.current) {
              isDragRef.current = false;
              return;
            }
            if (e.target === ref.current) editor?.commands.focus('end');
          }}
          onPaste={handlePaste}
        >
          <EditorContent editor={editor} />
          <Box
            sx={{
              height: '100px',
              width: '100%',
              cursor: 'text',
            }}
            onClick={() => {
              editor?.commands.focus('end');
            }}
          />
          {editor && <TableOfContents editor={editor} />}
          {editor && <BubbleMenu editor={editor} />}
          {editor && <FloatingMenu editor={editor} documentId={id} filePublicUrl={filePublicUrl} onUpload={onUpload} />}
        </Box>
      </Box>
    </Box>
  );
}
