'use client';

import { CircularProgress } from '@mui/joy';
import { <PERSON><PERSON><PERSON>, <PERSON>le } from '@xyflow/react';
import { AvatarLogo } from 'basenext/avatar';
import React from 'react';
import { INodeResourceApi } from '@bika/types/node/context';
import { SkillsetVO } from '@bika/types/skill/vo';
import type { MemberVO, TeamVO } from '@bika/types/unit/vo';
import ChevronDownOutlined from '@bika/ui/icons/components/chevron_down_outlined';
import ChevronUpOutlined from '@bika/ui/icons/components/chevron_up_outlined';
import { Box, Divider, Stack } from '@bika/ui/layouts';
import { MemberPanel } from '@bika/ui/sidebar/member-panel';
import { SkillsetsLogos } from '../../../components/avatar/skillset-avatars';

interface OrgChartUnitExtension {
  parentId?: string;
  isExpanded?: boolean;
  disabled?: boolean;
  getMoreUnits?: (unit: OrgChartUnit) => void;
  onClick?: (unit: OrgChartUnit) => void;

  // API for fetching NodeBO to display hover skillsets info
  api?: INodeResourceApi;
}

interface MemberNode extends MemberVO, OrgChartUnitExtension {
  type: 'Member';
  children?: never;
  skillsets?: SkillsetVO[];
}
interface TeamNode extends Omit<TeamVO, 'children'>, OrgChartUnitExtension {
  type: 'Team';
  avatar?: AvatarLogo;
  children?: OrgChartUnit[];
}

export type OrgChartUnit = MemberNode | TeamNode;

interface Props {
  readonly data: OrgChartUnit;
}

export function UnitNode(props: Props) {
  const { data } = props;
  const [loading, setLoading] = React.useState(false);

  const disabledExpandButton = data.type === 'Team' && data.children && data.children.length === 0;

  const handleClick = () => {
    data?.onClick?.(data);
  };

  const Skills = () => {
    if (data.type === 'Member' && data.skillsets) {
      return <SkillsetsLogos skillsets={data.skillsets} />;
    }
    return null;
  };

  return (
    <Stack sx={{ display: data.disabled ? 'none' : 'flex' }}>
      <Box onClick={handleClick} sx={{ cursor: data?.onClick ? 'pointer' : 'default' }}>
        <MemberPanel unit={data} type="Organization" skills={<Skills />} />
      </Box>
      {data.type === 'Team' && !disabledExpandButton && (
        <>
          <Divider
            orientation="vertical"
            sx={{ height: '20px', background: 'var(--borderBrandDefault)', margin: '0 auto' }}
          />
          <Box
            sx={{
              width: '32px',
              height: '24px',
              borderRadius: '12px',
              border: 'var(--borderBrandDefault) 1px solid ',
              bgcolor: 'var(--bg-blur)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto',
              boxShadow: 'var(--shadow-default)',
            }}
            onClick={async () => {
              setLoading(true);
              await data?.getMoreUnits?.(data);
              setLoading(false);
            }}
          >
            {loading && (
              <CircularProgress
                sx={{
                  '--CircularProgress-size': '22px',
                  '--CircularProgress-progressThickness': '3px',
                  '--CircularProgress-trackThickness': '3px',
                }}
              />
            )}
            {!loading &&
              (data.isExpanded ? (
                <ChevronUpOutlined size="12px" color={'var(--borderBrandDefault)'} />
              ) : (
                <ChevronDownOutlined size="12px" color={'var(--borderBrandDefault)'} />
              ))}
          </Box>
        </>
      )}
      <Handle
        type="target"
        position={Position.Top}
        style={{ opacity: 0, pointerEvents: 'none', transform: 'translate(-50%,0)' }}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        style={{ opacity: 0, pointerEvents: 'none', transform: 'translate(-50%,0)' }}
      />
    </Stack>
  );
}
