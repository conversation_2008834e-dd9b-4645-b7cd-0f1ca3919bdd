import Tooltip from '@mui/joy/Tooltip';
import Typography from '@mui/joy/Typography';
import { AvatarLogo } from 'basenext/avatar';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import React from 'react';
import { useLocale } from '@bika/contents/i18n/context';
// import { DeleteIcon } from '@bika/ui/icons';
import { useCssColor } from '../../colors';
import { AvatarImg } from '../../components/avatar';
import { Stack } from '../../layout-components';

dayjs.extend(relativeTime);

interface IProps {
  name: string;
  createAt: string;
  avatar: AvatarLogo;
}

export const ActivityUser = ({ name, avatar, createAt }: IProps) => {
  const colors = useCssColor();
  const { t, lang } = useLocale();
  return (
    <Stack direction={'row'} spacing={'8px'} alignItems={'center'}>
      <AvatarImg
        name={name}
        variant="solid"
        avatar={avatar}
        size={'sm'}
        sx={{
          height: '20px',
          width: '20px',
        }}
      />

      <Stack direction="column">
        <Typography level="b2" textColor={'var(--text-secondary)'}>
          {name}
        </Typography>
        <Tooltip title={dayjs(createAt).format('YYYY-MM-DD HH:mm:ss')} placement="top">
          <Typography level="b4" textColor={'var(--text-secondary)'}>
            {dayjs(createAt).locale(lang).fromNow()}
          </Typography>
        </Tooltip>
      </Stack>
    </Stack>
  );
};
