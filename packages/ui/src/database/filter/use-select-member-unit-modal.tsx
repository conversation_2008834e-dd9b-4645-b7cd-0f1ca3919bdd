import _ from 'lodash';
import { useState, useMemo } from 'react';
import { useTRPCQuery, keepPreviousData } from '@bika/api-caller';
import { useSpaceContextForce } from '@bika/types/space/context';
import type { AvatarLogo } from 'basenext/avatar';
import type { MemberVO, UnitVO } from '@bika/types/unit/vo';

type MemberUnitTypeWithDescription = UnitVO & {
  label: string;
  description: string;
  avatar?: AvatarLogo;
};

export const useSelectMemberUnitModal = (spaceId: string, selectedUnitIds: string[], keepPrevious = true) => {
  const trpcQuery = useTRPCQuery();
  const [isShowModal, setIsShowModal] = useState(false);
  const spaceContext = useSpaceContextForce();

  const [searchText, setSearchText] = useState('');

  // 已选
  const { data: unitPagination, isLoading: isLoadingSelectedUnit } = trpcQuery.unit.list.useQuery(
    {
      spaceId,
      ids: selectedUnitIds.filter((id) => id),
      pageNo: 1,
      pageSize: selectedUnitIds.length,
    },
    { enabled: selectedUnitIds.length > 0, placeholderData: keepPrevious ? keepPreviousData : undefined },
  );

  const isLoadingSelected = useMemo(
    () => isLoadingSelectedUnit && selectedUnitIds.length > 0,
    [isLoadingSelectedUnit, selectedUnitIds],
  );

  // 是否根部门被选中
  const isRootSelected = selectedUnitIds.includes(spaceContext.rootTeam?.id);
  // 是否访客根部门被选中
  const isGuestRootSelected = selectedUnitIds.includes(spaceContext.guestRootTeam?.id);

  // 已选的列表
  const selectedUnits: UnitVO[] = useMemo(() => {
    const list = unitPagination?.data ?? [];

    if (isRootSelected) {
      list.push(spaceContext.rootTeam);
    }
    if (isGuestRootSelected) {
      list.push(spaceContext.guestRootTeam);
    }
    return _.uniqBy(list, 'id');
  }, [unitPagination, isRootSelected, isGuestRootSelected]);

  // 搜索通讯录
  const { data: searchResultOfUnits, isLoading: isLoadingSearch } = trpcQuery.unit.list.useQuery(
    {
      spaceId,
      name: searchText.length > 0 ? searchText : undefined,
      pageSize: 5, // 一次最多搜索5个
    },
    { enabled: searchText.length > 0 },
  );
  // 是否正在搜索
  const isSearching = useMemo(() => isLoadingSearch && searchText.length > 0, [isLoadingSearch, searchText]);

  // 构造下拉框选项
  const dropdownOptions: MemberUnitTypeWithDescription[] = useMemo(() => {
    const unitList =
      searchResultOfUnits?.data.filter((unit) => !(unit.type === 'Member' && unit.relationType === 'AI')) || [];

    // 搜索结果中排除已选的成员
    const displaySelectedUnitIds = selectedUnits.map((unit) => unit.id);
    const duplicateUnitList = unitList.filter((member) => !displaySelectedUnitIds.includes(member.id));

    // 搜索时选中成员数据不会丢失
    return [...duplicateUnitList, ...selectedUnits].map((unit) => ({
      ...unit,
      label: unit.name,
      description: (unit as MemberVO)?.teams?.map((team) => team.path).join(', ') ?? '',
    }));
  }, [searchResultOfUnits, selectedUnits]);

  const checkedOptions: UnitVO[] = useMemo(() => selectedUnits, [selectedUnits]);

  return {
    isShowModal,
    isLoading: isLoadingSelected || isSearching,
    setIsShowModal,
    searchText,
    setSearchText,
    dropdownOptions,
    checkedOptions,
  };
};
