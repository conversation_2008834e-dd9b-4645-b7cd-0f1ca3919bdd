import { iStringParse } from 'basenext/i18n';
import { produce } from 'immer';
import { CONST_DISABLED_FILTER } from '@bika/contents/config/client/database/filter';
import {
  type DatabaseFieldType,
  type DatabaseSingleSelectField,
  type FilterCondition,
  type ViewFilter,
  type DatabaseField,
  type DatabaseRating<PERSON>ield,
  getFieldOperators,
  FieldFilterOperator,
  FilterConditionClauseValue,
  ensureFilterConditionClause,
  FilterDateTime,
} from '@bika/types/database/bo';
import type { AutoFormInputComponentProps, IFormCustomOptions } from '../../form/form.types';
import { FieldTypeIconComponent } from '../record-detail/field-type-icon-component';

export type FormType =
  | 'textarea'
  | 'rating'
  | 'number'
  | 'percent'
  | 'member'
  | 'datetime_filter'
  | 'select'
  | 'checkbox'
  | 'modified_by'
  | 'created_by'
  | 'switch';

interface FormInputComponentProps<E extends FormType> {
  formType: E;
  inputProps: AutoFormInputComponentProps;
}

export interface IFilterFormConditionState {
  fieldId: FormInputComponentProps<'select'>;
  operator: FormInputComponentProps<'select'>;
  filterValue?: {
    formType: FormType | null;
    options: IFormCustomOptions;
    inputProps: AutoFormInputComponentProps;
  };
}

interface IFilterFormState {
  conjunction: FormInputComponentProps<'select'>;
  conditions: IFilterFormConditionState[];
}

// 包括引用列类型，推到得出
export type DatabaseFieldWithCalculated = DatabaseField & {
  calculatedType?: DatabaseFieldType;
};

export class FilterStateManager {
  private _formTypeMap: Record<DatabaseFieldType, FormType> = {
    AUTO_NUMBER: 'number',
    NUMBER: 'number',
    SINGLE_TEXT: 'textarea',
    LONG_TEXT: 'textarea',
    URL: 'textarea',
    EMAIL: 'textarea',
    PHONE: 'textarea',
    CASCADER: 'textarea', // FIXME
    DATETIME: 'datetime_filter',
    SINGLE_SELECT: 'select',
    MULTI_SELECT: 'select',
    CURRENCY: 'number',
    PERCENT: 'percent',
    RATING: 'rating',
    CHECKBOX: 'checkbox',
    CREATED_TIME: 'datetime_filter',
    MODIFIED_TIME: 'datetime_filter',
    MEMBER: 'member',
    CREATED_BY: 'modified_by',
    MODIFIED_BY: 'modified_by',
    LINK: 'textarea',
    ONE_WAY_LINK: 'textarea',
    ATTACHMENT: 'textarea', // FIXME
    FORMULA: 'textarea', // FIXME
    LOOKUP: 'textarea',
    WORK_DOC: 'textarea',
    BUTTON: 'textarea',
    API: 'textarea',
    AI_TEXT: 'textarea',
    AI_VOICE: 'textarea',
    AI_PHOTO: 'textarea',
    AI_VIDEO: 'textarea',
    CUT_VIDEO: 'textarea',
    JSON: 'textarea',
    VIDEO: 'textarea',
    VOICE: 'textarea',
    PHOTO: 'textarea',
    DATERANGE: 'textarea',
  };

  private _state: ViewFilter = {
    // 已废弃, 勿用
    conditions: [],
    conds: [],
    conjunction: 'And',
  };

  private readonly _fieldList: DatabaseFieldWithCalculated[];

  private readonly _descriptionMap: (type: DatabaseFieldType) => Record<string, string>;

  constructor(
    initial: ViewFilter,
    fieldList: DatabaseFieldWithCalculated[],
    descriptionMap: (type: DatabaseFieldType) => Record<string, string>,
  ) {
    this._fieldList = fieldList;
    this._state = initial;
    this._descriptionMap = descriptionMap;
  }

  static getDefaultValue(): ViewFilter {
    return {
      conditions: [],
      conds: [],
      conjunction: 'And',
    };
  }

  updateConjunction(conjunction: 'And' | 'Or') {
    return produce(this._state!, (draft) => {
      draft.conjunction = conjunction;
    });
  }

  deleteByIndex(index: number): ViewFilter {
    return produce(this._state!, (draft) => {
      draft.conds?.splice(index, 1);
    });
  }

  /**
   * 更新输入的过滤值
   */
  updateFilterValue(index: number, value: string[] | string | FilterDateTime | null): ViewFilter {
    console.log('updateFilterValue', index, value);
    return produce(this._state, (draft) => {
      if (!draft.conds) {
        console.warn('[update filter value] Conditions not found');
        return;
      }
      const condition: FilterCondition | undefined = draft.conds[index];
      if (!condition) {
        console.warn('[update filter value] Condition not found');
        return;
      }
      // 找到列BO
      const field = this._fieldList.find((item) => {
        const itemId = item.id;
        return itemId === condition.fieldId;
      });
      if (!field) {
        console.warn('[update filter value] Field not found');
        return;
      }
      const clause = ensureFilterConditionClause(field.type, condition.clause.operator, value);
      if (!clause) {
        console.warn('[update filter value] Invalid clause value for field type', value);
        return;
      }
      draft.conds[index].clause = clause;
    });
  }

  /**
   * 切换过滤操作符
   */
  updateOperator(index: number, operator: FieldFilterOperator) {
    return produce(this._state!, (draft) => {
      if (!draft.conds) {
        console.warn('[update operator] Conditions not found');
        return;
      }
      const condition: FilterCondition | undefined = draft.conds[index];
      if (!condition) {
        console.warn('[update operator] Condition not found');
        return;
      }
      const field = this._fieldList.find((item) => {
        const itemId = item.id;
        return itemId === condition.fieldId;
      });
      if (!field) {
        console.warn('[update operator] Field not found');
        return;
      }
      const value = this.defaultFieldOperatorValue(field, operator);
      draft.conds[index].clause.operator = operator;
      draft.conds[index].clause.value = value;
      // condition.clause.operator = operator;
      // condition.clause.value = this.defaultFieldOperatorValue(field, operator);
    });
  }

  /**
   * 切换字段
   */
  updateField(index: number, fieldId: string) {
    const field = this._fieldList.find((item) => item.id === fieldId);
    if (!field) {
      console.warn('Field not found');
      return this._state;
    }
    // console.log('updateField', index, fieldId, field.type);
    return produce(this._state, (draft) => {
      if (!draft.conds) {
        return;
      }
      const condition = draft.conds[index];
      // console.log('source condition', !condition);
      if (!condition) {
        return;
      }
      draft.conds[index] = this.defaultFieldCondition(field);
    });
  }

  /**
   * 字段选中某个操作符后的默认过滤值
   */
  private defaultFieldOperatorValue(field: DatabaseField, operator: FieldFilterOperator): FilterConditionClauseValue {
    switch (field.type) {
      case 'CHECKBOX':
        return false; // 默认值为 false
      case 'DATETIME':
      case 'CREATED_TIME':
      case 'MODIFIED_TIME': {
        // 字段对应支持的操作符
        const operators = getFieldOperators(field.type);
        if (operators.includes(operator)) {
          if (operator === 'IsEmpty' || operator === 'IsNotEmpty') {
            return null;
          }
          // 对于日期时间类型，默认值为 [FilterDuration.ExactDate, null]
          return ['ExactDate', null];
        }
        return null;
      }
      default:
        // 一般情况下, 都是null空值
        return null;
    }
  }

  /**
   * 字段的默认过滤条件
   */
  private defaultFieldCondition(field: DatabaseField): FilterCondition {
    const base = { fieldId: field.id, fieldTemplateId: field.templateId };
    switch (field.type) {
      case 'SINGLE_TEXT':
      case 'LONG_TEXT':
      case 'URL':
      case 'EMAIL':
      case 'PHONE':
      case 'FORMULA':
        return { ...base, fieldType: field.type, clause: { operator: 'Is', value: null } };
      case 'NUMBER':
      case 'PERCENT':
      case 'CURRENCY':
      case 'RATING':
      case 'AUTO_NUMBER': {
        return { ...base, fieldType: field.type, clause: { operator: 'Is', value: null } };
      }
      case 'CHECKBOX': {
        return { ...base, fieldType: field.type, clause: { operator: 'Is', value: false } };
      }
      case 'SINGLE_SELECT':
      case 'CREATED_BY':
      case 'MODIFIED_BY': {
        return { ...base, fieldType: field.type, clause: { operator: 'Is', value: null } };
      }
      case 'MULTI_SELECT':
      case 'MEMBER':
      case 'LINK': {
        return { ...base, fieldType: field.type, clause: { operator: 'Is', value: null } };
      }
      case 'DATETIME':
      case 'CREATED_TIME':
      case 'MODIFIED_TIME': {
        return { ...base, fieldType: field.type, clause: { operator: 'Is', value: ['ExactDate', null] } };
      }
      default: {
        return { ...base, fieldType: field.type, clause: { operator: 'IsEmpty' } };
      }
    }
  }

  getFieldType = (fieldType: DatabaseFieldType): FormType => this._formTypeMap[fieldType as DatabaseFieldType];

  getFieldOptions = (fieldType: DatabaseFieldType, field: DatabaseField | undefined, operator: FieldFilterOperator) => {
    if (!field) {
      return {
        formType: null,
        multiple: false,
        options: null,
      };
    }

    let options: any = null;

    switch (fieldType) {
      case 'RATING': {
        const ratingField = field as DatabaseRatingField;
        options = { max: ratingField.property?.max, icon: ratingField.property?.icon };
        break;
      }
      default: {
        options = (field as DatabaseSingleSelectField)?.property?.options?.map((item) => ({
          label: iStringParse(item.name),
          value: item.id ?? iStringParse(item.name),
          color: item.color,
        }));
      }
    }
    return {
      formType: ['IsEmpty', 'IsNotEmpty', 'IsRepeat'].includes(operator)
        ? null
        : this._formTypeMap[fieldType as DatabaseFieldType],
      multiple: !!(fieldType === 'MULTI_SELECT' || ['Contains', 'DoesNotContain'].includes(operator)),
      options,
    };
  };

  conditionMap2Form(st: FilterCondition, fields: DatabaseFieldWithCalculated[]): IFilterFormConditionState {
    const conditionFieldId = st.fieldId;
    const field = fields.find((item) => item.id === conditionFieldId);
    if (!field) {
      console.log(`Could not find field with id ${conditionFieldId}`);
    }
    // 如果 fieldType 发生变化，则需要更新 fieldType
    const isFieldTypeUpdated = field?.type !== st.fieldType;
    // 获取真正的fieldType 可能转换后
    const fieldType = field?.calculatedType ?? field?.type ?? st.fieldType;
    const operators = getFieldOperators(fieldType).map((operator) => ({
      label: this._descriptionMap(fieldType)[operator],
      value: operator,
    }));
    const { options, multiple, formType } = this.getFieldOptions(fieldType, field, st.clause.operator);

    let filterValueOption = null;
    if (formType === 'rating') {
      filterValueOption = options;
    } else if (formType === 'select') {
      filterValueOption = options;
    } else {
      // timeZone: 'UTC',
      filterValueOption = {
        fOperator: st.clause.operator,
      };
    }

    const checkedValue = isFieldTypeUpdated ? null : st.clause.value;

    return {
      filterValue: {
        formType,
        options: filterValueOption,
        inputProps: {
          isRequired: true,
          fieldProps: {
            value: checkedValue,
            multiple,
            defaultValue: checkedValue,
            showLabel: false,
            options,
          },
          zodInputProps: {},
          fieldConfigItem: {},
        } as AutoFormInputComponentProps,
      },
      operator: {
        formType: 'select',
        inputProps: {
          isRequired: true,
          fieldProps: {
            value: st.clause.operator,
            showLabel: false,
            options: operators,
          },
          zodInputProps: {},
          fieldConfigItem: {},
        } as AutoFormInputComponentProps,
      },
      fieldId: {
        formType: 'select',
        inputProps: {
          isRequired: true,
          fieldProps: {
            value: st.fieldId,
            showLabel: false,
            options: fields.map((field) => ({
              value: field.id,
              label: field.name,
              disabled: CONST_DISABLED_FILTER.includes(field.type) === true,
              prefixIcon: <FieldTypeIconComponent type={field.type} />,
              icon: <FieldTypeIconComponent type={field.type} />,
            })),
          },
          zodInputProps: {},
          fieldConfigItem: {},
        } as AutoFormInputComponentProps,
      },
    };
  }

  addConditions(): ViewFilter {
    const headField = this._fieldList[0];
    if (!headField) {
      return this._state;
    }

    // 检查字段是否支持操作符
    const operators = getFieldOperators(headField.type);
    if (operators.length === 0) {
      console.warn(`No operators found for field type ${headField.type}`);
      return this._state;
    }
    // 加了一个新的字段过滤之后, 显示的默认展示过滤条件
    return produce<ViewFilter>(this._state!, (draft) => {
      if (!draft.conds) {
        draft.conds = [];
      }
      draft.conds.push(this.defaultFieldCondition(headField));
    });
  }

  toForm(): IFilterFormState {
    if (!this._state) {
      return {
        conjunction: {
          formType: 'select',
          inputProps: {} as AutoFormInputComponentProps,
        },
        conditions: [],
      };
    }
    return {
      conjunction: {
        formType: 'select',
        inputProps: {} as AutoFormInputComponentProps,
      },
      conditions: this._state.conds?.map((condition) => this.conditionMap2Form(condition, this._fieldList)) || [],
    };
  }
}
