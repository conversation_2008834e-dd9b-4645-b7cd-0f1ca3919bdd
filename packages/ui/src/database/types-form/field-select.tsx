'use client';

import { Stack, Switch, Typography } from '@mui/joy';
import type { SxProps } from '@mui/joy/styles/types';
import { useEffect, useMemo, useState } from 'react';
import { getWidgetOptionsConfig } from '@bika/contents/config/client/dashboard/widgets';
import type { ILocaleContext } from '@bika/contents/i18n';
import { WidgetDateFormat, widgetDateFormatSchema } from '@bika/types/dashboard/bo';
import type { DatabaseFieldType, LookupFieldProperty } from '@bika/types/database/bo';
import type { ViewFieldVO } from '@bika/types/database/vo';
import type { INodeResourceApi } from '@bika/types/node/context';
import { FormHelperText, FormControl } from '../../form-components';
import { SelectInput } from '../../shared/types-form/select-input';
import { FieldSkeleton } from '../../skeleton';
import { FieldTypeIconComponent } from '../record-detail';

interface FieldSelectProps {
  className?: string;
  databaseId?: string;
  fieldId?: string;
  viewId?: string;
  setFieldId: (id: string, field?: ViewFieldVO) => void;
  setFieldType?: (type?: DatabaseFieldType) => void;
  disabled?: boolean;
  locale: ILocaleContext;
  api: INodeResourceApi;
  required?: boolean;
  label?: string;
  setErrors?: (errors?: Record<string, string>) => void;
  filterFieldTypes?: string[];
  filterFields?: string[];
  disabledFields?: string[];
  filterDataType?: string[];
  isSepareted?: boolean;
  setIsSepareted?: (isSepareted: boolean) => void;
  sx?: SxProps;
  id?: string;
  showSepareted?: boolean;
  isWidget?: boolean;
  showWidgetDateFormat?: boolean;
  onShowDateFormatChange?: (showFormat: boolean) => void;
  widgetDateFormat?: WidgetDateFormat | null;
  onDateFormatChange?: (format: WidgetDateFormat | null) => void;
  placeholder?: string;
}

const SEPARETD_FIELD_TYPES = ['MULTI_SELECT', 'LINK', 'MEMBER'];
const DATE_FORMATTABLE_FIELD_TYPES = ['DATETIME', 'CREATED_TIME', 'MODIFIED_TIME'];

export const FieldSelect = (props: FieldSelectProps) => {
  const { t, i } = props.locale;
  const {
    setFieldId,
    setFieldType,
    disabledFields,
    fieldId = '',
    databaseId = '',
    disabled,
    required,
    label,
    setErrors,
    filterFieldTypes,
    filterFields,
    id,
    viewId,
    filterDataType,
    isSepareted,
    showSepareted = false,
    isWidget,
    showWidgetDateFormat = false,
    placeholder,
  } = props;
  const { data, isLoading } = props.api.database.getDatabaseVO(databaseId);
  const columns = viewId ? data?.views.find((item) => item.id === viewId)?.columns ?? [] : data?.views[0].columns ?? [];
  const [showSeparetedSwitch, setShowSeparetedSwitch] = useState(false);

  const widgetOptions = getWidgetOptionsConfig(props.locale);
  const currentField = columns.find((item) => item.id === fieldId);

  const isDateField =
    DATE_FORMATTABLE_FIELD_TYPES.includes(currentField?.type || '') ||
    (currentField?.type === 'LOOKUP' && currentField.property.dataType === 'DATETIME');

  useEffect(() => {
    const errorKey = id || 'viewId';
    if (required && setErrors) {
      setErrors({ [errorKey]: fieldId ? '' : t.integration.general.err_msg });

      return () => {
        setErrors({ [errorKey]: '' });
      };
    }
  }, [fieldId, setErrors, required]);

  useEffect(() => {
    if (setFieldType) {
      setFieldType(currentField?.type);
    }
    if (showSepareted) {
      const isSeparetedField = SEPARETD_FIELD_TYPES.includes(currentField?.type ?? '');
      setShowSeparetedSwitch(isSeparetedField);
    }
  }, [currentField, showSepareted]);

  const filteredColumns = useMemo(() => {
    let _filteredColumns = columns;
    if (columns.length === 0) return [];
    if (filterFieldTypes) {
      _filteredColumns = columns.filter((item) => {
        const result = filterFieldTypes.includes(item.type);
        if (item.type === 'LOOKUP' && filterDataType) {
          return result && filterDataType.includes((item.property as LookupFieldProperty)?.dataType ?? '');
        }
        return result;
      });
    }
    if (filterFields) {
      _filteredColumns = columns.filter((item) => {
        if (item.templateId && filterFields?.includes(item.templateId)) {
          return false;
        }
        return !filterFields.includes(item.id);
      });
    }
    return _filteredColumns;
  }, [columns, filterFieldTypes, filterFields]);

  const options = filteredColumns.map((item) => ({
    label: i(item.name),
    value: item.id,
    disabled: (disabledFields ?? []).includes(item.id),
    icon: <FieldTypeIconComponent type={item.type} />,
  }));

  const invalidField = !filteredColumns.find((item) => item.id === fieldId);

  if (databaseId && isLoading) {
    return <FieldSkeleton />;
  }

  return (
    <FormControl className={props.className}>
      <SelectInput
        required={required}
        disabled={disabled}
        label={label}
        iconSize={16}
        options={options}
        placeholder={
          options.length > 0 ? t.resource.placeholder_select_field : placeholder || t.resource.placeholder_no_field
        }
        value={fieldId}
        onChange={(value) => {
          const currentField = filteredColumns?.find((c) => c.id === value);
          setFieldId(value as string, currentField);
        }}
        sx={props.sx}
      />
      {required && !fieldId && (
        <FormHelperText sx={{ color: 'var(--status-danger)' }}>{t.integration.general.err_msg}</FormHelperText>
      )}
      {fieldId && invalidField && (
        <FormHelperText sx={{ color: 'var(--status-danger)' }}>
          {t.widget_settings.chart_option_field_had_been_deleted}
        </FormHelperText>
      )}
      {showSeparetedSwitch && (
        <Stack direction="row" spacing={2} alignItems="center" sx={{ mt: 1 }}>
          <Switch checked={isSepareted} onChange={(e) => props.setIsSepareted?.(e.target.checked)} />
          <Typography>{t.widget_settings.separeted_multi_value}</Typography>
        </Stack>
      )}
      {isWidget && isDateField && (
        <>
          <Stack direction="row" spacing={2} alignItems="center" sx={{ mt: 1 }}>
            <Switch
              checked={showWidgetDateFormat}
              onChange={(e) => props.onShowDateFormatChange?.(e.target.checked)}
              endDecorator={<Typography textColor="var(--text-primary)">{t.widget_settings.format_date}</Typography>}
            />
          </Stack>

          {showWidgetDateFormat && (
            <SelectInput
              iconSize={16}
              options={widgetOptions.dateFormat}
              value={props.widgetDateFormat || widgetDateFormatSchema.enum.YEAR_MONTH_DAY}
              onChange={(value) => props.onDateFormatChange?.(value)}
            />
          )}
        </>
      )}
    </FormControl>
  );
};
