import { useCompletion } from '@ai-sdk/react';
import Option from '@mui/joy/Option';
import Select from '@mui/joy/Select';
import type { LocaleType } from 'basenext/i18n';
import { i18n } from 'basenext/i18n';
import React from 'react';
import type { z } from 'zod';
import { aiWriterConfigs } from '@bika/contents/config/client/ai/ai-writer/ai-writer';
import { LocaleStringConfig } from '@bika/contents/config/client/locale';
import { useLocale } from '@bika/contents/i18n';
import type { AIWriter, AIWriterType } from '@bika/types/ai/bo';
import { AIWriterResponse } from '@bika/types/ai/bo';
import { useNodeResourceApiContext } from '@bika/types/node/context';
import { useSpaceContextForce } from '@bika/types/space/context';
import { Button, IconButton } from '@bika/ui/button-component';
import { ButtonGroup } from '@bika/ui/forms';
import Star2Outlined from '@bika/ui/icons/components/star_2_outlined';
import TriangleDownFilled from '@bika/ui/icons/components/triangle_down_filled';
import GenerateAiFilled from '@bika/ui/icons/doc_hide_components/generate_ai_filled';
import TranslateOutlined from '@bika/ui/icons/doc_hide_components/translation_ai_outlined';
import WriteAiOutlined from '@bika/ui/icons/doc_hide_components/write_ai_outlined';
import { Stack, Box } from '@bika/ui/layouts';
import { Menu, MenuItem } from '@bika/ui/menu';
import { StringInput } from '@bika/ui/shared/types-form/string-input';
import { useSnackBar } from '@bika/ui/snackbar/snackbar-component';
import { Typography } from '@bika/ui/texts';

export interface AIWriterViewProps {
  /**
   * 定义好业务类型 列表
   */
  writers: AIWriter[];
  // 是否不让修改模式
  immutableWriter?: boolean;
  defaultUserPrompt: string;
  defaultWriterType?: AIWriterType | null;
  zodSchema?: z.ZodAny;
  // 提交后，返回的数据，看是“追加”还是“覆盖”
  onSubmit: (type: 'INSERT' | 'REPLACE', result: { data: any; value: string | undefined }) => void;
  // 当前单元格内的语言 可不写 翻译用
  currentLocale?: LocaleType;
}

export function AIWriterView(props: AIWriterViewProps) {
  const { i, t, lang } = useLocale();
  const { toast } = useSnackBar();
  const [lastWriterResponse, setLastWriterResponse] = React.useState<AIWriterResponse | null>(() => null);
  const {
    completion,
    input,
    isLoading,
    setInput,
    // handleInputChange,
    complete,
  } = useCompletion({
    api: '/api/ai/writer',
    initialInput: props.defaultUserPrompt,
    onFinish: (_prompt, _completion: string) => {
      console.log('finish Data', _completion);
      setLastWriterResponse({
        success: true,
        data: completion,
        value: completion,
      });
    },
    onError: (error) => {
      console.error('error====', error);
      // todo handle credit error
    },
  });
  const [currentWriter, setCurrentWriter] = React.useState<AIWriter>(
    (props.defaultWriterType && props.writers.find((w) => w.type === props.defaultWriterType)) || props.writers[0],
  );

  const currentWriterType = currentWriter.type;

  // 额外的参数
  const [extraParams, setExtraParams] = React.useState<any | null>(() => null);

  const api = useNodeResourceApiContext();
  const spaceContext = useSpaceContextForce();
  const spaceId = spaceContext?.data?.id;
  const [open, setOpen] = React.useState(false);
  const anchorRef = React.useRef<HTMLDivElement>(null);
  const [selectedIndex, setSelectedIndex] = React.useState(1);
  const actionTexts: string[] = [t.ai.insert, t.ai.overwrite];
  const [writerNextAction, setWriterNextAction] = React.useState<'INSERT' | 'REPLACE'>('REPLACE');

  const handleActionMenuItemClick = (_event: React.MouseEvent<HTMLElement, MouseEvent>, index: number) => {
    setSelectedIndex(index);
    setOpen(false);

    setWriterNextAction(index === 0 ? 'INSERT' : 'REPLACE');
  };

  const onClickCallAIWriter = React.useCallback(async () => {
    complete(input, {
      body: {
        writer: Object.assign(currentWriter, extraParams),
        spaceId,
      },
    });

    // const writerResult = await api.ai.callAIWriter(Object.assign(currentWriter, extraParams), input, {
    //   createdAt: new Date().toISOString(),
    // });
    // setResultData(writerResult.data || null);
    // // setResultValue(writerResult.value || '');
    // if (!writerResult.success) {
    //   toast(writerResult.message || '', { variant: 'warning' });
    // }
    // } finally {
    //   // setLoading(false);
    // }
  }, [api.ai, input, currentWriterType]);

  const _configs = aiWriterConfigs;
  const writerOptions = props.writers.map((writer) => {
    const cfg = _configs[writer.type];
    if (!cfg) {
      return {
        label: writer.type,
        value: writer.type,
      };
    }
    return {
      label: i(cfg.label),
      value: writer.type,
    };
  });
  const currentConfig = _configs[currentWriterType];

  if (!currentConfig) {
    return null;
  }

  // resultData 实际数据
  // resultValue 显示用的
  return (
    <Stack direction="column">
      <Box sx={{ mb: 1, display: 'flex' }}>
        <Select<AIWriterType>
          startDecorator={<WriteAiOutlined color="var(--text-secondary)" />}
          disabled={props.immutableWriter === true}
          value={currentWriterType}
          variant="soft"
          onChange={(_event, newVal) => {
            setCurrentWriter(props.writers.find((w) => w.type === newVal)!);

            // 清空状态
            setLastWriterResponse(null);
            // setResultData(null);
            // setResultValue(null);
            // setUserPrompt('');
            setExtraParams(null);
            if (newVal === 'I18N_STRING') {
              setExtraParams({ langs: i18n.locales.filter((l) => l === props.currentLocale) });
            }
          }}
          size="sm"
          sx={{
            borderRadius: 16,
            minHeight: 32,
            px: '1em',
            alignSelf: 'baseline',
          }}
          slotProps={{
            // the left-edge of the listbox will align with button.
            listbox: {
              placement: 'bottom-start',
              sx: {
                '--List-radius': '4px',
                '--ListItem-radius': 0,
              },
            },
          }}
        >
          {writerOptions.map((opt) => (
            <Option key={opt.value} value={opt.value}>
              <Stack spacing={1} direction="row">
                <GenerateAiFilled color="var(--text-secondary)" />
                <Typography sx={{ ml: 1 }}>{opt.label}</Typography>
              </Stack>
            </Option>
          ))}
        </Select>
      </Box>
      <Stack mb={2}>
        {currentConfig.showUserPrompt && (
          <StringInput
            textarea={{ minRows: 5, maxRows: 15 }}
            helpText={i(currentConfig.description)}
            value={input}
            onChange={(newInput) => {
              setInput(newInput);
            }}
          />
        )}

        {currentWriter?.type === 'TRANSLATE' && (
          <Select<AIWriterType>
            disabled={props.immutableWriter === true}
            value={extraParams?.lang || i18n.locales.find((l) => l !== lang)}
            variant="soft"
            onChange={(_e, newVal) => {
              setExtraParams({ lang: newVal });
            }}
            size="sm"
            sx={{
              width: 200,
              borderRadius: 10,
              minHeight: 32,
              mt: 2,
            }}
          >
            {i18n.locales.map((opt) => (
              <Option key={opt} value={opt}>
                <Stack spacing={1} direction="row">
                  {/* @ts-expect-error ignore */}
                  <TranslateOutlined color="var(--text-secondary)" /> {LocaleStringConfig[lang][opt]}
                </Stack>
              </Option>
            ))}
          </Select>
        )}
      </Stack>
      {!lastWriterResponse && (
        <Stack
          // 居中
          justifyContent="center"
          alignItems="center"
          sx={{
            width: '100%',
          }}
          display="flex"
        >
          <Button
            loading={isLoading}
            startDecorator={<GenerateAiFilled />}
            // 渐变色 6681E5 - 9852D7
            sx={{
              background: 'linear-gradient(90deg, #6681E5 0%, #9852D7 100%)',
              color: 'white',
              ':hover': {
                background: 'var(--joy-palette-primary-solidHoverBg)',
              },
              ':active': {
                background: 'var(--joy-palette-primary-solidActiveBg)',
              },
              '&.Mui-disabled': {
                opacity: 0.5,
                color: 'white',
              },
            }}
            onClick={() => {
              onClickCallAIWriter();
            }}
            size="lg"
          >
            {isLoading ? t.ai.generating : t.ai.generate}
          </Button>
        </Stack>
      )}

      {completion && (
        <Stack>
          <Typography level="b3">{t.ai.generated_result}</Typography>
          <Stack
            sx={{
              mt: 0.5,
              borderRadius: 8,
              padding: 1,
              backgroundColor: 'var(--selected)',
              color: 'var(--brand)',
              mb: 1,
            }}
          >
            <Typography
              level="b3"
              component="pre"
              sx={{
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
                fontFamily: 'inherit',
              }}
            >
              {completion}
            </Typography>
          </Stack>
          {/* <StringInput label={'生成结果'} value={resultValue} onChange={setResultValue} /> */}
          <Stack direction="row" spacing={1} justifyContent="center" alignItems="center" display="flex" sx={{ mt: 2 }}>
            <ButtonGroup ref={anchorRef} size="md" color="primary">
              {!isLoading && (
                <>
                  <Button
                    color="primary"
                    size="md"
                    onClick={() => {
                      props.onSubmit(writerNextAction, { data: lastWriterResponse?.data, value: completion || '' });
                    }}
                  >
                    {actionTexts[selectedIndex]}
                  </Button>
                  <IconButton
                    color="primary"
                    variant="solid"
                    size="md"
                    aria-controls={open ? 'split-button-menu' : undefined}
                    aria-expanded={open ? 'true' : undefined}
                    aria-label="select merge strategy"
                    aria-haspopup="menu"
                    onClick={() => {
                      setOpen((prevOpen) => !prevOpen);
                    }}
                  >
                    <TriangleDownFilled color="var(--text-secondary)" />
                  </IconButton>
                </>
              )}
            </ButtonGroup>
            <Menu open={open} onClose={() => setOpen(false)} anchorEl={anchorRef.current}>
              {actionTexts.map((option, index) => (
                <MenuItem
                  key={option}
                  disabled={index === 2}
                  selected={index === selectedIndex}
                  onClick={(event) => handleActionMenuItemClick(event, index)}
                >
                  {option}
                </MenuItem>
              ))}
            </Menu>
            <Button
              size="md"
              loading={isLoading}
              startDecorator={<Star2Outlined color="var(--text-secondary)" />}
              variant="outlined"
              onClick={() => {
                onClickCallAIWriter();
              }}
            >
              {t.ai.regenerate}
            </Button>
          </Stack>
        </Stack>
      )}
    </Stack>
  );
}
