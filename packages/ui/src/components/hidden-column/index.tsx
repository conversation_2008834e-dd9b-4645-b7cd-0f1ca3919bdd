import { DropResult } from '@hello-pangea/dnd';
import { useSelections, useUpdateEffect } from 'ahooks';
import { iString } from 'basenext/i18n';
import React, { ReactElement, useCallback, useState } from 'react';
import { useLocale } from '@bika/contents/i18n';
import { EmptyResult } from './empty-result';
import { Button, IconButton } from '../../button-component';
import { useCssColor } from '../../colors';
import { DragDropList } from '../../drag-drop-list';
import { Highlighter } from '../../highlighter';
import DragOutlined from '../../icons/components/drag_outlined';
import EyeCloseOutlined from '../../icons/components/eye_close_outlined';
import EyeOpenOutlined from '../../icons/components/eye_open_outlined';
import LockOutlined from '../../icons/components/lock_outlined';
import { Container } from '../../layout';
import { Box, Stack } from '../../layout-components';
import { SearchInputComponent } from '../../search-input-component';
import { Typography } from '../../text-components';

interface HiddenColumnProps<
  T extends {
    name: iString;
  },
> {
  value: T[];
  itemRenderer?: (
    item: T,
    options?: {
      searchWords?: string;
    },
  ) => ReactElement | string;
  onChange: (newDataList: T[]) => void;
  keyMapper?: (item: T) => string;
}

// a little function to help us with reordering the result
const reorder = <TList extends unknown[]>(list: TList, startIndex: number, endIndex: number): TList => {
  const result = Array.from(list) as TList;
  const [removed] = result.splice(startIndex, 1);
  result.splice(endIndex, 0, removed);
  return result;
};

const defaultKeyMapper = String;

export function HiddenColumnComponent<
  T extends {
    id: string;
    hidden: boolean;
    name: iString;
    primary?: boolean;
  },
>({ value = [], onChange, itemRenderer: originalItemRenderer, keyMapper = defaultKeyMapper }: HiddenColumnProps<T>) {
  const valueIds = value.map((r) => r.id);

  // selected 是所有选中的 id，意味着需要显示这些字段
  const { isSelected, selected, toggle, selectAll, setSelected } = useSelections(
    valueIds,
    value.filter((r) => r.hidden !== true).map((r) => r.id),
  );

  useUpdateEffect(() => {
    onChange(value.map((item) => ({ ...item, hidden: !selected.includes(item.id) })).filter(Boolean));
  }, [selected]);

  const onDragEnd = useCallback(
    (result: DropResult) => {
      // 如果拖放到列表外，直接返回
      if (!result.destination) {
        return;
      }

      // 如果拖放到相同位置，不进行任何操作
      if (result.destination.index === result.source.index) {
        return;
      }

      const newItems = [...value];
      const [movedItem] = newItems.splice(result.source.index, 1);
      newItems.splice(result.destination.index, 0, movedItem);

      // 更新字段顺序并保持隐藏状态
      onChange(
        newItems.map((item) => ({
          ...item,
          hidden: !selected.includes(item.id),
        })),
      );
    },
    [value, selected, onChange],
  );

  const { i, t } = useLocale();

  const colors = useCssColor();
  const [text, setText] = useState('');
  const defaultRenderer = (
    col: T,
    options: {
      searchWords?: string;
    },
  ) => {
    const searchWords = options?.searchWords ? [options?.searchWords] : [];
    return (
      <Typography level={'b4'} textColor={'var(--text-primary)'}>
        <Highlighter searchWords={searchWords} textToHighlight={i(col.name)}></Highlighter>
      </Typography>
    );
  };
  const itemRenderer = originalItemRenderer ?? defaultRenderer;

  const filteredList = value.filter((item) => {
    if (text.length === 0) {
      return true;
    }
    return i(item.name)?.includes(text);
  });

  const draggable = text.length === 0;

  return (
    <>
      <Box
        className={'flex flex-col'}
        sx={{
          height: '324px',
          width: '240px',
          overflowX: 'hidden',
          overflowY: 'hidden',
        }}
      >
        <SearchInputComponent
          value={text}
          autoFocus={true}
          onChange={(_newValue) => {
            setText(_newValue);
          }}
        />
        <Box className={'flex-auto h-full overflow-y-auto'}>
          {filteredList.length === 0 ? (
            <Stack width={'100%'} alignItems="center" height="100%" justifyContent={'center'}>
              <EmptyResult></EmptyResult>
            </Stack>
          ) : (
            <DragDropList
              droppableId={'id-drag-list'}
              onDragEnd={onDragEnd}
              list={filteredList}
              itemSx={{
                height: '40px',
                paddingX: '8px',
              }}
              renderItem={(item, dragProvided, index) => (
                <Container
                  width={'100%'}
                  key={keyMapper(item)}
                  justifyContent={'center'}
                  className={'hover:rounded-[4px]'}
                  sx={{
                    boxSizing: 'border-box',
                    display: 'inline-flex',
                    alignItems: 'center',
                    cursor: 'pointer',
                    '&:hover': {
                      backgroundColor: 'transparent',
                    },
                    height: '32px',
                  }}
                >
                  <Stack
                    alignItems={'center'}
                    direction={'row'}
                    className={'overflow-x-hidden w-full items-center'}
                    sx={{
                      width: '100%',
                    }}
                  >
                    {draggable && (
                      <div className={'cursor-pointer mr-2'} {...dragProvided?.dragHandleProps}>
                        {/* 看板未分组选项id==='none' */}
                        {item.primary || item.id === 'none' ? (
                          <LockOutlined color={'var(--text-secondary)'}></LockOutlined>
                        ) : (
                          <DragOutlined color={'var(--text-secondary)'} />
                        )}
                      </div>
                    )}

                    <Box
                      className={'flex-auto w-full flex items-center overflow-x-hidden'}
                      sx={{
                        width: '100%',
                      }}
                    >
                      {itemRenderer(item, {
                        searchWords: text,
                      })}
                    </Box>

                    {!item.primary && (
                      <IconButton
                        sx={{ display: 'show' }}
                        // disabled={firstColumn?.id === item.id || firstColumn.templateId === item.id}
                        color="neutral"
                        onClick={(e) => {
                          toggle(item.id);
                          e.stopPropagation();
                        }}
                      >
                        {isSelected(item.id) ? <EyeOpenOutlined /> : <EyeCloseOutlined />}
                      </IconButton>
                    )}
                  </Stack>
                </Container>
              )}
            />
          )}
        </Box>

        <Stack
          className={'flex-none justify-between'}
          direction={'row'}
          justifyContent={'space-around'}
          sx={{
            marginTop: '8px',
            gap: '12px',
          }}
        >
          <Button
            variant={'soft'}
            color={'neutral'}
            fullWidth
            onClick={() => {
              setSelected(value.filter((item) => item.primary).map((value) => value.id));
            }}
          >
            {t.toolbar.hide_all}
          </Button>

          <Button
            color={'neutral'}
            variant={'soft'}
            fullWidth
            onClick={() => {
              selectAll();
            }}
          >
            {t.toolbar.show_all}
          </Button>
        </Stack>
      </Box>
    </>
  );
}
