import Avatar from '@mui/joy/Avatar';
import Avatars from '@mui/joy/AvatarGroup';
import { AvatarLogo } from 'basenext/avatar';
import * as React from 'react';
import { type MemberVO } from '@bika/types/unit/vo';
import { Tooltip } from '@bika/ui/tooltip';
import { AvatarImg } from './avatar-component';
import { AvatarSize } from './avatar.enum';
import { Box } from '../../layout-components';
import { UserPanelComponent } from '../../sidebar/user-panel/index';

export type AvatarGroupItem = { name: string; avatar: AvatarLogo; id?: string };

interface BaseAvatarGroupProps {
  avatars?: AvatarGroupItem[];
  max?: number;
  style?: React.CSSProperties;
  size?: AvatarSize;
  groupGap?: number;
  customAvatar?: (avatar: AvatarGroupItem) => React.ReactNode; // custom avatar component
}

interface MemberAvatarGroupProps {
  members: MemberVO[];
  max?: number;
  style?: React.CSSProperties;
  size?: AvatarSize;
  groupGap?: number;
}

export const BaseAvatarGroup = (props: BaseAvatarGroupProps) => {
  const { avatars, max = 5, style, size = AvatarSize.Size32, groupGap, customAvatar } = props;

  if (!avatars) return null;

  const avatarsCount = avatars.length;
  const effectiveMax = Math.max(2, Math.min(max, avatarsCount + 1));
  const displayedMembersCount = Math.min(effectiveMax - 1, avatarsCount);
  const surplusMembers = avatarsCount - displayedMembersCount;

  const displayedAvatars = avatars.slice(-displayedMembersCount).map((avatar) =>
    customAvatar ? (
      customAvatar(avatar)
    ) : (
      <Avatar key={avatar.name} sx={{ height: size, width: size, ...style }}>
        <AvatarImg
          name={avatar.name}
          alt={avatar.name}
          avatar={avatar.avatar}
          customSize={size}
          shape="SQUARE"
          style={{ ...style }}
        />
      </Avatar>
    ),
  );

  return (
    <Avatars
      sx={{
        '--AvatarGroup-gap': groupGap ? `${groupGap}px` : `-${size / 5}px`,
        '--Avatar-ringSize': '0px',
      }}
    >
      {displayedAvatars}
      {!!surplusMembers && <Avatar sx={{ height: size, width: size, ...style }}>+{surplusMembers}</Avatar>}
    </Avatars>
  );
};

const MemberAvatarGroup = (props: MemberAvatarGroupProps) => {
  const { members, max = 5, style, size = AvatarSize.Size32, groupGap } = props;
  const [openUserPanel, setOpenUserPanel] = React.useState(false);

  const avatars = members.map((member) => ({
    name: member.name,
    avatar: member.avatar!,
    id: member.id,
  }));

  const memberPanel = (id: string) => {
    const member = members.find((item) => item.id === id);
    if (!member) return null;
    return openUserPanel ? (
      <UserPanelComponent member={member} nickName={member.name} memberName={member.name} />
    ) : (
      member.name
    );
  };

  const memberAvatar = (avatar: AvatarGroupItem) => (
    <Tooltip
      sx={openUserPanel ? { p: 0, bgcolor: 'transparent', boxShadow: 'none', borderRadius: '8px' } : {}}
      onClose={() => setOpenUserPanel(false)}
      title={memberPanel(avatar.id!)}
      placement="top"
    >
      <Box onClick={() => setOpenUserPanel(true)}>
        <Avatar sx={{ height: size, width: size, ...style }}>
          <AvatarImg
            name={avatar.name}
            alt={avatar.name}
            avatar={avatar.avatar}
            shape="SQUARE"
            customSize={size}
            sx={{ ...style }}
          />
        </Avatar>
      </Box>
    </Tooltip>
  );

  return (
    <BaseAvatarGroup
      avatars={avatars}
      max={max}
      style={style}
      size={size}
      groupGap={groupGap}
      customAvatar={memberAvatar}
    />
  );
};

export const AvatarGroup = MemberAvatarGroup;
