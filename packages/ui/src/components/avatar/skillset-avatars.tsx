import { Avatar, Box, Tooltip } from '@mui/joy';
import { AvatarLogo } from 'basenext/avatar';
import { CSSProperties, ReactNode, useMemo, useState } from 'react';
import { SkillsetVO } from '@bika/types/skill/vo';
import { AvatarGroupItem, AvatarImg, AvatarSize, BaseAvatarGroup } from '@bika/ui/components/avatar/index';

interface Props {
  skillsets: SkillsetVO[];
  size?: AvatarSize;
  style?: CSSProperties;

  tooltipContent?: ReactNode; // Element to show on hover
}
/**
 * Skillsets Logos
 *
 * @param props
 * @returns
 */
export function SkillsetsLogos(props: Props) {
  const { size = AvatarSize.Size16, tooltipContent } = props;
  const [open, setOpen] = useState(false);
  const skillsetsAvatars: { name: string; avatar: AvatarLogo }[] | undefined = useMemo(() => {
    if (props.skillsets && props.skillsets.length > 0) {
      return props.skillsets.map((skillset) => ({
        name: skillset.name,
        avatar: skillset.logo!,
      }));
    }
    return undefined;
  }, []);

  const customAvatar = (avatar: AvatarGroupItem) => (
    <Tooltip
      sx={open ? { p: 0, bgcolor: 'transparent', boxShadow: 'none', borderRadius: '8px' } : {}}
      onClose={() => setOpen(false)}
      title={tooltipContent}
      placement="top"
    >
      <Box onClick={() => setOpen(true)}>
        <Avatar sx={{ height: size, width: size, borderRadius: '4px', ...props.style }}>
          <AvatarImg name={avatar.name} alt={avatar.name} avatar={avatar.avatar} shape="SQUARE" customSize={size} />
        </Avatar>
      </Box>
    </Tooltip>
  );

  return (
    <BaseAvatarGroup
      avatars={skillsetsAvatars}
      size={size}
      groupGap={2}
      style={{ borderRadius: '4px', ...props.style }}
      customAvatar={tooltipContent ? customAvatar : undefined}
    />
  );
}
