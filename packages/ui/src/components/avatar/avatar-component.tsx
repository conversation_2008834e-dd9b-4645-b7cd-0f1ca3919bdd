import MUIAvatar, { type AvatarProps } from '@mui/joy/Avatar';
import type {
  AvatarLogo,
  AttachmentAvatar,
  UrlAvatar,
  UnsplashAvatar,
  PresetAvatar,
  ColorAvatar,
} from 'basenext/avatar';
import clsx from 'classnames';
import type React from 'react';
import { isValidHttpUrl } from '@bika/types/utils';
import { useUIFrameworkContext } from '@bika/ui/framework';
import { AvatarHOC, type IAvatarProps } from './avatar-hoc';
import type { AvatarSize } from './avatar.enum';
import type { AvatarShape } from './types';

export interface BikaAvatarProps extends AvatarProps {
  name?: string;
  className?: string;
  avatar?: AvatarLogo;
  customSize?: AvatarSize | number;
  shape?: AvatarShape;
  svg?: React.ReactNode;
  defaultIcon?: React.ReactNode;
}

const urlDisplay = (url: string): string | undefined => {
  if (isValidHttpUrl(url) || url.startsWith('/assets')) {
    return url;
  }
  return undefined;
};

export const relativePathDisplay = (relativePath: string, attachmentPublicUrl?: string): string | undefined => {
  if (relativePath.startsWith('blob:')) {
    return relativePath;
  }

  if (isValidHttpUrl(relativePath)) {
    return relativePath;
  }

  return `${attachmentPublicUrl}/${relativePath}`;
};
function EmojiComponent({
  color,
  emoji,
  customSize,
  className,
  shape,
}: {
  color: string;
  emoji: string;
  customSize: number;
  className: string | undefined;
  shape: AvatarShape;
}) {
  return (
    <div
      className={clsx('flex justify-center items-center', className)}
      style={{
        backgroundColor: color,
        width: customSize,
        height: customSize,
        // eslint-disable-next-line no-nested-ternary
        borderRadius: shape === 'SQUARE' ? (customSize >= 32 ? '6px' : '4px') : '50%',
        color: 'red',
      }}
    >
      <span
        style={{
          fontSize: customSize! * 0.66,
          lineHeight: 'normal',
          // transform: 'scale(0.5)',
        }}
      >
        {emoji}
      </span>
    </div>
  );
}
export const AvatarImg = (props: BikaAvatarProps) => {
  const { svg, name, alt, avatar, customSize, shape, defaultIcon, className, ...rest } = props;
  const ctx = useUIFrameworkContext();

  if (svg) {
    return (
      <MUIAvatar className={className} {...rest}>
        {svg}
      </MUIAvatar>
    );
  }

  const avatarBaseProps: IAvatarProps = {
    className,
    name: name || '',
    size: customSize,
    shape,
    defaultIcon,
  };

  if (!avatar) {
    if (!defaultIcon) {
      return (
        <MUIAvatar
          className={className}
          sx={{
            width: customSize,
            height: customSize,
          }}
          {...rest}
        >
          {name?.slice(0, 1)}
        </MUIAvatar>
      );
    }

    return <AvatarHOC {...avatarBaseProps} />;
  }

  const avatarType = avatar.type;
  switch (avatarType) {
    case 'URL': {
      const urlAvatar = avatar as UrlAvatar;
      return <AvatarHOC {...avatarBaseProps} src={urlDisplay(urlAvatar.url)} style={props.style} />;
    }
    case 'UNSPLASH': {
      const unsplashAvatar = avatar as UnsplashAvatar;
      return <AvatarHOC {...avatarBaseProps} src={urlDisplay(unsplashAvatar.url)} style={props.style} />;
    }
    case 'ATTACHMENT': {
      const attachment = avatar as AttachmentAvatar;
      return (
        <AvatarHOC
          {...avatarBaseProps}
          src={relativePathDisplay(attachment.relativePath, ctx.storageHostname)}
          style={props.style}
        />
      );
    }
    case 'COLOR': {
      const colorAvatar = avatar as ColorAvatar;

      return <AvatarHOC {...avatarBaseProps} avatarColor={colorAvatar.color} style={props.style} />;
    }
    case 'EMOJI':
      return (
        <EmojiComponent
          color={avatar.backgroundColor || ''}
          emoji={avatar.emoji}
          customSize={customSize || 40}
          className={className}
          shape={shape || 'CIRCLE'}
        />
      );
    case 'PRESET': {
      const presetAvatar = avatar as PresetAvatar;
      return <AvatarHOC {...avatarBaseProps} src={presetAvatar.url} style={props.style} />;
    }
    case 'AI': {
      return (
        <AvatarHOC
          {...avatarBaseProps}
          src={relativePathDisplay(avatar.relativePath, ctx.storageHostname)}
          style={props.style}
        />
      );
    }
    default:
      throw new Error(`unknown avatar type: ${avatarType}`);
  }
};
