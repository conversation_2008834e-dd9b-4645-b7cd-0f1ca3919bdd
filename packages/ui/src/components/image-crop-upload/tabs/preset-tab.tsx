import { useState } from 'react';
import type { <PERSON><PERSON><PERSON>ogo } from 'basenext/avatar';
import * as utils from '../../../utils';

export interface Props {
  // for preset tab
  presetPhotos?: { url: string }[];
  onApply: (logo: <PERSON>tar<PERSON>ogo | undefined) => void;
}

export function PresetTab({ presetPhotos, onApply }: Props) {
  const [presetPhotoUrl, setPresetPhotoUrl] = useState<string>('reset');

  const clearState = () => {
    setPresetPhotoUrl('');
  };

  const handlePresetPhotoSelect = (url: string) => {
    clearState();
    setPresetPhotoUrl(url);
    onApply({
      type: 'PRESET',
      url,
    });
  };

  return (
    <>
      <div className={'flex flex-wrap'}>
        {presetPhotos?.map((photo, index) => {
          if (photo.url === 'reset') {
            return (
              <div
                className={utils.cn(
                  'w-[160px] h-[120px] rounded-lg mb-6 bg-[--bg-page] cursor-pointer',
                  presetPhotoUrl === 'reset' && 'shadow-[0_0_0_2px_#A494FF]',
                )}
                onClick={() => handlePresetPhotoSelect('reset')}
              ></div>
            );
          }
          return (
            <img
              src={photo.url}
              key={photo.url}
              onClick={() => handlePresetPhotoSelect(photo.url)}
              alt="preset"
              width={160}
              height={120}
              className={utils.cn(
                'cursor-pointer w-content rounded-lg mb-6 bg-[--bg-page]',
                presetPhotoUrl === photo.url && 'shadow-[0_0_0_2px_#A494FF]',
                index % 3 !== 0 && 'ml-4',
              )}
            />
          );
        })}
      </div>
    </>
  );
}
