import type { <PERSON><PERSON><PERSON><PERSON> } from 'basenext/avatar';
import React, { type ChangeEvent, useCallback, useRef, useState } from 'react';
// @ts-expect-error ts-migrate(7006) FIXME: Parameter 'props' implicitly has an 'any' type.
import ReactCrop from 'react-image-crop';
import { useLocale } from '@bika/contents/i18n/context';
import { Button } from '@bika/ui/button';
import { CropShape } from '../interface';
import styles from '../style.module.css';

const initCropConfigMap = new Map([
  [CropShape.Square, { unit: '%', width: 100, aspect: 1 }],
  [CropShape.Rectangle, { unit: '%', width: 100, aspect: 3 }],
  [CropShape.AnyShape, { unit: '%', width: 100, height: 100 }],
]);
interface Props {
  cropShape?: CropShape;
  fileLimit?: number; // Upload the image size limit in MB, e.g. to limit the image size limit to 2MB after cropping, pass 2 for fileLimit
  // cropShape = CropShape.Square,
  onApply: (avatar: AvatarLogo | undefined) => void;
  upImgFile: File | null;
  setUpImgFile: (file: File | null) => void;
}
export function AttachmentTab({ onApply, cropShape, fileLimit, upImgFile, setUpImgFile }: Props) {
  const { t } = useLocale();
  const inputRef = useRef<HTMLInputElement>(null);
  const [upImg, setUpImg] = useState<string>(''); // Latest images uploaded - url
  const imgRef = useRef<HTMLImageElement | null>(null);
  // const [previewUrl, setPreviewUrl] = useState<string>(''); // Preview images
  const initCropConfig = initCropConfigMap.get(cropShape || CropShape.Square);
  const [crop, setCrop] = useState(initCropConfig); // Image cropping information
  const [isGif, setIsGif] = useState(false);
  const [disabled, setDisabled] = useState(false);
  // const avatarColorList = createAvatarRainbowColorsArr();

  // const getEmojis = () => {
  //   const emojis = (emojiData as EmojiMartData).emojis;
  //   const categories = (emojiData as EmojiMartData).categories;

  //   return categories.reduce<string[]>((collect, cur) => {
  //     cur.emojis.map((key: string) => collect.push(emojis[key].skins[0].native));
  //     return collect;
  //   }, []);
  // };

  // const onUploadFile = () => {
  //   inputRef.current?.click();
  // };
  const onComplete = (crop: { width: number; height: number }, percentCrop: { width: number; height: number }) => {
    const image = imgRef.current;
    // console.log('onComplete', crop, percentCrop);
    if (!image) {
      return;
    }

    // 如果是 GIF 文件，跳过裁剪处理，保持原始文件
    if (isGif) {
      // 对于 GIF 文件，直接使用原始文件，不进行 Canvas 裁剪
      onApply({
        type: 'ATTACHMENT',
        attachmentId: '',
        relativePath: window.URL.createObjectURL(upImgFile!),
      });
      return;
    }

    if (crop.width && crop.height && cropShape === CropShape.AnyShape) {
      // console.log('cropAnyView');
      cropAnyView(image, crop);
    }
    if (percentCrop.width && percentCrop.height && cropShape === CropShape.Rectangle) {
      // console.log('cropRectangleView');
      cropRectangleView(image, percentCrop);
    }
    if (percentCrop.width && percentCrop.height && cropShape === CropShape.Square) {
      // console.log('cropSquareView');
      cropSquareView(image, percentCrop);
    }
  };

  // Drawing a square cut-out area
  const cropSquareView = (image: any, percentCrop: any) => {
    const canvas = document.createElement('canvas');
    const computedSize = Math.floor((image.naturalHeight * percentCrop.height) / 100);
    canvas.width = computedSize;
    canvas.height = computedSize;
    const ctx = canvas.getContext('2d');
    ctx!.drawImage(
      image,
      Math.floor((image.naturalWidth * percentCrop.x) / 100),
      Math.floor((image.naturalHeight * percentCrop.y) / 100),
      computedSize,
      computedSize,
      0,
      0,
      computedSize,
      computedSize,
    );

    canvasToFile(canvas);
  };

  // Drawing rectangular cut-out areas
  const cropRectangleView = (image: any, percentCrop: any) => {
    const canvas = document.createElement('canvas');
    const computedSize = Math.floor((image.naturalWidth * percentCrop.width) / 100);
    canvas.width = computedSize;
    canvas.height = computedSize / 3;
    const ctx = canvas.getContext('2d');
    ctx!.drawImage(
      image,
      Math.floor((image.naturalWidth * percentCrop.x) / 100),
      Math.floor((image.naturalHeight * percentCrop.y) / 100),
      computedSize,
      computedSize / 3,
      0,
      0,
      computedSize,
      computedSize / 3,
    );

    canvasToFile(canvas);
  };

  // Draw arbitrarily shaped cropped areas
  const cropAnyView = (image: any, crop: any) => {
    const canvas = document.createElement('canvas');
    const scaleX = image.naturalWidth / image.width;
    const scaleY = image.naturalHeight / image.height;
    canvas.width = crop.width * scaleX;
    canvas.height = crop.height * scaleY;
    const ctx = canvas.getContext('2d');
    ctx!.drawImage(
      image,
      crop.x * scaleX,
      crop.y * scaleY,
      crop.width * scaleX,
      crop.height * scaleY,
      0,
      0,
      crop.width * scaleX,
      crop.height * scaleY,
    );

    canvasToFile(canvas);
  };

  const dataURLtoFile = (urlData: string, fileName: string) => {
    const bytes = window.atob(urlData.split(',')[1]); // Remove the url header and convert to byte
    // @ts-expect-error ts-migrate(7006) FIXME: Parameter 'bytes' implicitly has an 'any' type.
    const mime = urlData.split(',')[0].match(/:(.*?);/)[1];
    // Handling exceptions, converting ascii codes less than 0 to greater than 0
    const ab = new ArrayBuffer(bytes.length);
    const ia = new Uint8Array(ab);
    for (let i = 0; i < bytes.length; i += 1) {
      ia[i] = bytes.charCodeAt(i);
    }
    return new File([ab], fileName, { type: mime });
  };
  const canvasToFile = (canvas: HTMLCanvasElement) => {
    canvas.toBlob(
      (blob) => {
        if (!blob) {
          // new Error('Canvas is empty');
          return;
        }
        // Image Preview
        const pUrl = window.URL.createObjectURL(blob!);
        // setPreviewUrl();

        onApply({
          type: 'ATTACHMENT',
          attachmentId: '',
          relativePath: pUrl,
        });
      },
      (upImgFile as File).type,
    );
    const imgBase64 = canvas.toDataURL((upImgFile as File).type, 1);
    const file = dataURLtoFile(imgBase64, (upImgFile as File).name);
    setUpImgFile(file);
  };

  const onLoad = useCallback((img: any) => {
    imgRef.current = img;
  }, []);

  const onUploadFile = () => {
    inputRef.current?.click();
  };

  const beforeUpload = (file: File): Promise<void> =>
    new Promise<void>((res, rej) => {
      const isGifFile = Boolean(file.type === 'image/gif');
      setIsGif(isGifFile);

      const reader = new FileReader();
      // Because it takes time to read a file, the result of the read is used in the callback function
      reader.onload = () => {
        const isLt2M = fileLimit ? (file as File).size / 1024 / 1024 > fileLimit : false;
        if (isLt2M) {
          // Message.error({ content: t(Strings.image_limit, { number: fileLimit }) });
          setDisabled(true);
          rej();
          return;
        }

        // setAvatarColor(null);
        setUpImg(reader.result as string);
        // setOfficialImgToken('');
        setUpImgFile(file);
        // setPreviewUrl(window.URL.createObjectURL(file));
        const pUrl = window.URL.createObjectURL(file);

        // 如果是 GIF 文件，直接应用原始文件，不需要等待裁剪
        if (isGifFile) {
          onApply({
            type: 'ATTACHMENT',
            attachmentId: '',
            relativePath: pUrl,
          });
        } else {
          // 对于非 GIF 文件，先设置预览，等待用户裁剪
          onApply({
            type: 'ATTACHMENT',
            attachmentId: '',
            relativePath: pUrl,
          });
        }
        // setDisabled(false);
        // props.children && setInnerControlModal(true);
        res();
      };

      reader.readAsDataURL(file);
    });

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      beforeUpload(file);
    }
  };

  // 渲染上传文件后的内容
  const renderUploadedContent = () => {
    if (isGif) {
      // 对于 GIF 文件，显示预览而不是裁剪工具
      return (
        <div className={'flex flex-col items-center justify-center h-full space-y-4'}>
          <div className={'text-center'}>
            <div className={'text-sm text-[--text-secondary] mb-2'}>
              {t('avatar.gif_no_crop_tip') || 'GIF files will be uploaded without cropping to preserve animation'}
            </div>
            <img src={upImg} alt="GIF preview" className={'max-w-full max-h-[300px] object-contain rounded'} />
          </div>
        </div>
      );
    }

    // 对于非 GIF 文件，显示裁剪工具
    return (
      <div className={styles.cropWrapper}>
        <ReactCrop
          crop={crop}
          onChange={(c: any) => {
            setCrop(c);
          }}
          onComplete={onComplete}
          src={upImg}
          onImageLoaded={onLoad}
        />
      </div>
    );
  };

  return (
    <>
      <input type="file" hidden ref={inputRef} accept="image/*" onChange={handleFileChange} />
      {upImgFile ? (
        renderUploadedContent()
      ) : (
        <div className={'mb-6 flex flex-col items-center justify-center h-full space-y-6'}>
          <Button
            color="primary"
            onClick={() => {
              onUploadFile();
            }}
          >
            {t('avatar.upload_avatar')}
          </Button>
          <div className={'text-b4 text-[--text-secondary]'}>{t('avatar.file_tip')}</div>
        </div>
      )}
    </>
  );
}
