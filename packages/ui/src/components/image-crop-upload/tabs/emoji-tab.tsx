import emojiData from '@emoji-mart/data';
import type { EmojiMartData } from '@emoji-mart/data';
import type { AvatarLogo } from 'basenext/avatar';
import { ColorTab } from './color-tab';
import { createAvatarRainbowColorsArr } from '../utils/color';

interface Props {
  // avatarColor?: string | null;
  value: AvatarLogo | undefined;
  onApply: (avatar: AvatarLogo | undefined) => void;
}
export function EmojiTab({ value, onApply }: Props) {
  const avatarColorList = createAvatarRainbowColorsArr();

  const getEmojis = () => {
    const emojis = (emojiData as EmojiMartData).emojis;
    const categories = (emojiData as EmojiMartData).categories;

    return categories.reduce<string[]>((collect, cur) => {
      cur.emojis.map((key: string) => collect.push(emojis[key].skins[0].native));
      return collect;
    }, []);
  };

  return (
    <>
      <ColorTab
        avatarColor={value?.type === 'EMOJI' ? value?.backgroundColor : undefined}
        onApply={(newColor) => {
          onApply({
            type: 'EMOJI',
            backgroundColor: newColor,
            emoji: (value?.type === 'EMOJI' && value?.emoji) || getEmojis()[0],
          });
        }}
      />

      <div className={'grid grid-cols-10 gap-4 overflow-auto h-[calc(100%-122px)]'}>
        {getEmojis().map((item) => (
          <span
            key={item}
            className={'w-[40px] h-[40px] flex justify-center items-center text-[32px] cursor-pointer rounded-lg'}
            style={{
              background:
                item === (value?.type === 'EMOJI' && value?.emoji) ? 'var(--textStaticSecondary)' : 'transparent',
            }}
            onClick={() => {
              // handleEmoji(item);
              onApply({
                type: 'EMOJI',
                backgroundColor: (value?.type === 'EMOJI' && value?.backgroundColor) || avatarColorList[0],
                emoji: item,
              });
            }}
          >
            {item}
          </span>
        ))}
        {/* <Input
                          placeholder={t('avatar.paste_image_link')}
                          sx={{
                            background: 'var(--bg-controls)',
                          }}
                          onBlur={handleThirdPartyUrl}
                        />
                        <div className={'text-[--text-disabled] text-b3 text-center'}>{t('avatar.tab_link_tip')}</div> */}
      </div>
    </>
  );
}
