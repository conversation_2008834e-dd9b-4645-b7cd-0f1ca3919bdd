/* eslint-disable max-lines */

'use client';

import React, { useCallback, useMemo, useRef, useState } from 'react';
import { useLocale } from '@bika/contents/i18n/context';
import type { AttachmentVO } from '@bika/types/attachment/vo';
import type { AvatarLogo } from 'basenext/avatar';
import { Button, TextButton } from '@bika/ui/button';
import { AvatarImg, AvatarSize } from '@bika/ui/components/avatar/index';
import { Modal } from '@bika/ui/modal';
import { Tab, TabList, TabPanel, Tabs } from '@bika/ui/tabs';
import { CropShape, type ICustomTip, type IUnsplashData, PreviewShape } from './interface';
import { Unsplash } from './unsplash';
import * as utils from '../../utils';
import type { AIImageGeneratorProps } from './tabs/ai-image-tab';
import { AIImageTab } from './tabs/ai-image-tab';
import { AttachmentTab } from './tabs/attachment-tab';
import { ColorTab } from './tabs/color-tab';
import { EmojiTab } from './tabs/emoji-tab';
import { PresetTab } from './tabs/preset-tab';
import { UrlTab } from './tabs/url-tab';
import 'react-image-crop/dist/ReactCrop.css';

// 预制图片
export type ImageUploadTab = AvatarLogo['type'];

export interface IImageCropUploadProps {
  onClose: () => void;
  // type?: ImageUploadTab;
  confirm: (data: AvatarLogo) => void;

  value: AvatarLogo | undefined;

  avatarName?: string;
  fileLimit?: number; // Upload the image size limit in MB, e.g. to limit the image size limit to 2MB after cropping, pass 2 for fileLimit
  customTips?: ICustomTip; // Customise the preview area and crop area Tip text
  previewShape?: PreviewShape; // The shape of the preview image
  previewShapeStyle?: React.CSSProperties;
  cropShape?: CropShape;
  // avatarColor?: string;
  allowTab?: ImageUploadTab[];

  target?: 'BgImage' | 'Avatar';

  config?: {
    preset?: {
      // for preset tab
      presetPhotos?: { url: string }[];
    };
    ai?: AIImageGeneratorProps;

    unsplash?: {
      // unsplash handler
      unsplashDownload?: (unsplashUrl: string) => Promise<string>;
    };
  };

  // uploader handler
  upload?: (file: File) => Promise<AttachmentVO | undefined>;

  onReset?: () => void;
}

export const ImageCropUpload = ({
  onClose,
  confirm,
  fileLimit,
  value,
  customTips = {},
  previewShape = PreviewShape.Square,
  previewShapeStyle,
  cropShape = CropShape.Square,
  avatarName,
  config,
  allowTab = ['COLOR', 'ATTACHMENT', 'URL', 'EMOJI', 'UNSPLASH', 'AI'],
  target = 'Avatar',
  upload: uploadFile,
  onReset,
}: IImageCropUploadProps) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const type = value?.type || 'COLOR'; // Default type is COLOR if value is not provided
  const [tabKey, setTabKey] = useState<ImageUploadTab>(() => {
    // If value has a type and it's in allowTab, use that type
    if (type && allowTab.includes(type)) {
      return type;
    }
    // Otherwise, use the first allowed tab
    return allowTab?.[0] || type;
  });

  const [emoji, setEmoji] = useState<string | undefined>();
  const [officialImgToken, setOfficialImgToken] = useState(''); // Official picture id
  const [upImg, setUpImg] = useState<string>(''); // Latest images uploaded - url
  const [upImgFile, setUpImgFile] = useState<File | null>(null); // Latest images uploaded -file format
  const [previewUrl, setPreviewUrl] = useState<string>(''); // Preview images
  const isPreviewCircle = previewShape === PreviewShape.Circle;

  const [avatar, setAvatar] = useState<AvatarLogo | undefined>(value);

  const { t } = useLocale();

  const [loading, setLoading] = useState(false);

  const { cropTip, cropDesc } = customTips;

  const previewTip = customTips.previewTip || cropTip;

  const _onClose = () => {
    if (loading) return;
    onClose();
  };

  const confirmBtnClick = async () => {
    if (!avatar) return;

    // 独立处理  File 裁剪后的attachment 上传
    if (avatar.type === 'ATTACHMENT' && uploadFile && upImgFile) {
      // && upImgFile && uploadFile) {
      // 这里要先处理上传的逻辑
      setLoading(true);
      const res = await uploadFile(upImgFile);
      if (!res) {
        setLoading(false);
        return;
      }
      setLoading(false);
      confirm?.({
        ...avatar,
        attachmentId: res.id,
        relativePath: res.path,
      });
      return;
    }

    if (avatar.type === 'UNSPLASH') {
      setLoading(true);

      setLoading(false);

      confirm?.({
        ...avatar,
      });
      return;
    }

    confirm?.(avatar);
  };

  const isAllowSubmit = avatar !== undefined;

  // Deselect
  const clearSelect = () => {
    setUpImg('');
    setUpImgFile(null);
    setOfficialImgToken('');
    setPreviewUrl('');
    setEmoji(undefined);
  };

  const onUploadFile = () => {
    inputRef.current?.click();
  };

  const renderReselect = useMemo(() => {
    if (!(upImg || officialImgToken)) {
      return;
    }
    if (tabKey === 'COLOR' || (tabKey === 'ATTACHMENT' && !upImg)) {
      // eslint-disable-next-line consistent-return
      return (
        <div className={'flex flex-col items-center mt-6'}>
          <TextButton color="primary" onClick={clearSelect}>
            {t('avatar.cancel_select')}
          </TextButton>
        </div>
      );
    }
    if (tabKey === 'ATTACHMENT' && upImg) {
      // eslint-disable-next-line consistent-return
      return (
        <>
          <div className={'flex flex-col items-center space-y-6 mt-6'}>
            <TextButton color="primary" disabled={!upImg} onClick={onUploadFile}>
              {t('avatar.re_select')}
            </TextButton>
            <div className={'w-[172px] text-b4 text-[--text-secondary]'}>
              <div>{previewTip || t('avatar.file_tip')}</div>
              {/* {previewDesc || (fileLimit && '123')} */}
            </div>
          </div>
        </>
      );
    }
  }, [officialImgToken, previewTip, tabKey, upImg, t]);

  const PreviewArea = useCallback(
    () => (
      <div
        style={previewShapeStyle}
        className={utils.cn('w-[120px] h-[120px] overflow-hidden', isPreviewCircle && 'rounded-full')}
      >
        <AvatarImg
          name={avatarName || ''}
          customSize={AvatarSize.Size120}
          style={previewShapeStyle}
          avatar={avatar}
          shape={previewShape === PreviewShape.Circle ? 'CIRCLE' : 'SQUARE'}
        />
      </div>
    ),
    // }

    [avatar, upImg, officialImgToken, emoji, previewUrl],
  );

  return (
    <Modal onClose={_onClose} width={'837px'}>
      {/* <input type="file" hidden ref={inputRef} accept="image/*" onChange={handleFileChange} /> */}
      <div>
        <div className={'mb-[12px]'}>{t('avatar.edit_image')}</div>
        <div className={'flex justify-between pt-[16px] space-x-6'}>
          {/* 上传预览区域 */}
          <div className={'w-[245px] h-full'}>
            <div
              style={{
                marginBottom: target === 'BgImage' ? '24px' : '80px',
              }}
            >
              {t('avatar.preview')}
            </div>
            <div className={'flex justify-center'}>
              <PreviewArea />
            </div>
            {renderReselect}
          </div>
          {/* 上传区域 */}
          <div className={'flex-1'}>
            <Tabs onChange={(event, newValue) => setTabKey(newValue as any)} defaultValue={tabKey}>
              <TabList>
                {allowTab.includes('COLOR') && (
                  <Tab disableIndicator={false} value={'COLOR'}>
                    {t.avatar.tab_color}
                  </Tab>
                )}
                {allowTab.includes('PRESET') && config?.preset?.presetPhotos && (
                  <Tab disableIndicator={false} value={'PRESET'}>
                    {t.settings.space.wallpaper_preset_photos}
                  </Tab>
                )}
                {allowTab.includes('ATTACHMENT') && (
                  <Tab disableIndicator={false} value={'ATTACHMENT'}>
                    {t.avatar.tab_upload}
                  </Tab>
                )}
                {allowTab.includes('URL') && (
                  <Tab disableIndicator={false} value={'URL'}>
                    {t.avatar.tab_link}
                  </Tab>
                )}
                {allowTab.includes('EMOJI') && (
                  <Tab disableIndicator={false} value={'EMOJI'}>
                    Emoji
                  </Tab>
                )}
                {allowTab.includes('AI') && (
                  <Tab disableIndicator={false} value={'AI'}>
                    AI
                  </Tab>
                )}
                {/* 必须有unsplash 下载函数才显示 */}
                {config?.unsplash?.unsplashDownload && allowTab.includes('UNSPLASH') && (
                  <Tab disableIndicator={false} value={'UNSPLASH'}>
                    Unsplash
                  </Tab>
                )}
              </TabList>

              {/*  Start Panels */}
              <TabPanel value={'PRESET'} sx={{ height: '400px' }}>
                <PresetTab
                  presetPhotos={config?.preset?.presetPhotos}
                  onApply={(logo) => {
                    setAvatar(logo);
                  }}
                />
              </TabPanel>
              <TabPanel value={'EMOJI'} sx={{ height: '400px' }}>
                <EmojiTab value={avatar} onApply={setAvatar} />
              </TabPanel>
              <TabPanel value={'COLOR'} sx={{ height: '400px' }}>
                <ColorTab
                  avatarColor={avatar?.type === 'COLOR' ? avatar.color : undefined}
                  onApply={(color) => {
                    setAvatar({
                      type: 'COLOR',
                      color: color || 'red',
                    });
                  }}
                />
              </TabPanel>
              <TabPanel value={'ATTACHMENT'} sx={{ height: '400px' }}>
                <AttachmentTab
                  fileLimit={fileLimit}
                  cropShape={cropShape}
                  onApply={setAvatar}
                  upImgFile={upImgFile}
                  setUpImgFile={setUpImgFile}
                />
              </TabPanel>
              <TabPanel value={'URL'} sx={{ height: '400px' }}>
                <UrlTab
                  onApply={(newUrl) => {
                    setAvatar({
                      type: 'URL',
                      url: newUrl,
                    });
                  }}
                />
              </TabPanel>
              <TabPanel value={'AI'} sx={{ height: '400px' }}>
                <AIImageTab
                  {...config?.ai}
                  onApply={(image, prompt) => {
                    setAvatar({
                      type: 'AI',
                      prompt,
                      attachmentId: image.attachmentId, // FIXME: attachmentId
                      relativePath: image.relativePath,
                    });
                  }}
                />
              </TabPanel>
              <TabPanel value={'UNSPLASH'} sx={{ height: '400px' }}>
                <Unsplash
                  unsplashUrl={
                    avatar?.type === 'UNSPLASH'
                      ? { previewUrl: avatar.url, downloadUrl: avatar.downloadUrl || '' }
                      : null
                  }
                  setUnsplashUrl={(urls: IUnsplashData) => {
                    // clearState();
                    setAvatar({
                      type: 'UNSPLASH',
                      url: urls.previewUrl,
                      downloadUrl: urls.downloadUrl,
                    });
                  }}
                />
              </TabPanel>
            </Tabs>
          </div>
        </div>
        <div className={'flex justify-between	space-x-4'}>
          <div>
            {onReset && (
              <Button color="neutral" onClick={onReset} size="lg" variant="plain">
                Reset
              </Button>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button color="neutral" onClick={_onClose} size="lg" variant="plain" disabled={loading}>
              {t.buttons.cancel}
            </Button>
            <Button onClick={confirmBtnClick} size="lg" disabled={!isAllowSubmit} loading={loading}>
              {t.buttons.confirm}
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
};
