'use client';

import type { iString } from 'basenext/i18n';
import { useEffect, useState } from 'react';
import type { ILocaleContext } from '@bika/contents/i18n';
import type { AIWriter, DatabaseFieldDTO } from '@bika/types/ai/bo';
import type { NodeResource } from '@bika/types/node/bo';
import { truncateText } from '@bika/ui/utils';
import { IStringInput } from './i-string-input';

interface Props {
  labels?: {
    name: string | undefined;
    description: string | undefined;
  };
  value: {
    name: iString;
    description: iString | undefined;
  };
  onChange: (data: { name: iString; description: iString | undefined }) => void;
  locale: ILocaleContext;
  setErrors?: (errors?: Record<string, string>) => void;
  /** 给组件的上下文信息 目前只有AI在用 */
  context?: {
    resource?: NodeResource;
  };
}

export function NameDescriptionBOInput(props: Props) {
  const { locale } = props;
  const { i } = locale;

  const [availableAIWriters, setAvailableAIWriters] = useState<AIWriter[]>([]);

  const resourceName = props.context?.resource ? props.context.resource.name : undefined;
  const resourceDesc = props.context?.resource ? props.context.resource.description : undefined;

  useEffect(() => {
    if (props.context?.resource) {
      // enable AI writer for generating resource description

      const writerArray: AIWriter[] = [];

      if (props.context.resource.resourceType === 'FOLDER') {
        writerArray.push({
          type: 'RESOURCE_DESCRIPTION',
          resource: {
            resourceType: props.context.resource.resourceType,
            id: props.context.resource.id,
            name: i(props.context.resource.name),
            description: truncateText(i(props.context.resource.description), 200) || '',
            children:
              'children' in props.context.resource && Array.isArray(props.context.resource.children)
                ? props.context.resource?.children
                    .filter((child) =>
                      ['FOLDER', 'DATABASE', 'AUTOMATION', 'DASHBOARD', 'FORM', 'DOCUMENT'].includes(
                        child.resourceType,
                      ),
                    )
                    .slice(0, 5)
                    .map((child) => ({
                      name: i(child.name),
                      description: truncateText(i(child.description), 80) || '',
                      resourceType: child.resourceType,
                    }))
                : [],
          },
        });
      }

      if (props.context.resource.resourceType === 'DATABASE') {
        writerArray.push({
          type: 'RESOURCE_DESCRIPTION',
          resource: {
            resourceType: props.context.resource.resourceType,
            name: i(props.context.resource.name),
            description: truncateText(i(props.context.resource.description), 200) || '',
            fields:
              'fields' in props.context.resource && Array.isArray(props.context.resource.fields)
                ? (props.context.resource.fields.slice(0, 10).map((field) => ({
                    name: i(field.name),
                    type: field.type,
                  })) as DatabaseFieldDTO[])
                : [],
          },
        });
      }

      if (props.context.resource.resourceType === 'AUTOMATION') {
        writerArray.push({
          type: 'RESOURCE_DESCRIPTION',
          resource: {
            resourceType: props.context.resource.resourceType,
            name: i(props.context.resource.name),
            description: truncateText(i(props.context.resource.description), 200) || '',
            triggers:
              'triggers' in props.context.resource && Array.isArray(props.context.resource.triggers)
                ? props.context.resource.triggers.slice(0, 5).map((trigger) => ({
                    triggerType: trigger.triggerType,
                    description: truncateText(i(trigger.description), 50) || '',
                  }))
                : [],
            actions:
              'actions' in props.context.resource && Array.isArray(props.context.resource.actions)
                ? props.context.resource.actions.slice(0, 5).map((action) => ({
                    actionType: action.actionType,
                    description: truncateText(i(action.description), 50) || '',
                  }))
                : [],
          },
        });
      }

      if (writerArray.length === 0) {
        // for other types of resources
        writerArray.push({
          type: 'RESOURCE_DESCRIPTION',
          resource: {
            resourceType: props.context.resource.resourceType,
            name: i(props.context.resource.name),
            description: truncateText(i(props.context.resource.description), 200) || '',
          },
        });
      }

      // TODO: implement other AI writers
      // writerArray.push({ type: 'TRANSLATE' });
      // writerArray.push({ type: 'I18N_STRING' });
      setAvailableAIWriters(writerArray);
    }
  }, [resourceName, resourceDesc]);

  return (
    <>
      <IStringInput
        label={props.labels?.name || 'Name'}
        value={props.value.name}
        onChange={(newValue) => {
          props.onChange({ ...props.value, name: newValue });
        }}
        locale={locale}
        required
        setErrors={props.setErrors}
      />

      <IStringInput
        label={props.labels?.description || 'Description'}
        value={props.value.description || ''}
        onChange={(newDesc) => {
          props.onChange({ ...props.value, description: newDesc });
        }}
        textarea={{ minRows: 1 }}
        locale={props.locale}
        aiWriter={availableAIWriters.length > 0 ? availableAIWriters : undefined}
      />
    </>
  );
}
