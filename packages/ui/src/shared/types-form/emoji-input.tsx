import { AvatarLogo } from 'basenext/avatar';
import { AvatarImg } from '../../components/avatar/avatar-component';
import { AvatarSize } from '../../components/avatar/avatar.enum';
import { FormLabel } from '../../form-components';

interface Props {
  label?: string;
  value: AvatarLogo;
  onChange(value: AvatarLogo): void;

  // 点击更新封面
  onClickChangeCover?: () => void;
}
export function EmojiInput(props: Props) {
  // const [showImageUpload, setShowImageUpload] = useState(false);
  return (
    <>
      {props.label && <FormLabel>{props.label}</FormLabel>}
      <div className={'w-[64px] h-[64px] overflow-hidden m-auto'} onClick={props.onClickChangeCover}>
        <AvatarImg name={props.label} avatar={props.value} customSize={AvatarSize.Size64} shape="SQUARE" />
      </div>
    </>
  );
}
