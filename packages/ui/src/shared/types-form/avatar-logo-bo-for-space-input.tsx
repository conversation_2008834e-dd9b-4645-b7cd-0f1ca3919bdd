import React from 'react';
import type { ILocaleContext } from '@bika/contents/i18n';
import { useNodeResourceApiContext } from '@bika/types/node/context';
import type { AvatarLogo } from 'basenext/avatar';
import CopyOutlined from '@bika/ui/icons/components/copy_outlined';
import EditOutlined from '@bika/ui/icons/components/edit_outlined';
import { useSnackBar } from '@bika/ui/snackbar/snackbar-component';
import { copyText } from '@bika/ui/utils';
import { AvatarImg } from '../../components/avatar/avatar-component';
import { AvatarSize } from '../../components/avatar/avatar.enum';
import { useAttachmentUpload } from '../../components/image-crop-upload';
import { ImageCropUpload } from '../../components/image-crop-upload/image-crop-upload';

interface Props {
  name: string;
  spaceId: string;
  createBy: string;
  value: AvatarLogo | undefined;
  onChange(value: AvatarLogo | undefined): void;
  locale: ILocaleContext;
  avatarFilePrefix?: string;

  // 点击更新封面
  onClickChangeCover?: () => void;
}
export function AvatarLogoBOForSpaceInput(props: Props) {
  const editorContext = useNodeResourceApiContext();
  const { unsplashDownload } = useAttachmentUpload();
  const [showImageUpload, setShowImageUpload] = React.useState(false);
  const { t } = props.locale;
  const { toast } = useSnackBar();
  return (
    <div
      className="bg-[--bg-controls] p-7 rounded-lg flex items-center group"
      // onMouseEnter={() => setShowEditOverlay(true)}
      // onMouseLeave={() => setShowEditOverlay(false)}
    >
      <div className={'w-[64px] h-[64px] overflow-hidden m-auto mr-[18px] relative'}>
        <AvatarImg name={props.name} avatar={props.value} customSize={AvatarSize.Size64} shape="SQUARE" />
        {
          <div
            className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center cursor-pointer hidden group-hover:flex"
            onClick={() => setShowImageUpload(true)}
          >
            <EditOutlined className="text-white" />
            <span className="text-white ml-1">{t.action.edit}</span>
          </div>
        }
      </div>
      <div className="flex-1 ">
        <div className="text-h7 text-[--text-primary]">{props.name}</div>
        <div>
          <span className="text-b4 text-[--text-secondary]">{t.space.space_creator}：</span>
          <span className="text-b4 text-[--text-primary]"> {props.createBy}</span>
        </div>
        <div className="flex items-center">
          <span className="text-b4 text-[--text-secondary]">{t.space.space} ID：</span>
          <span className="text-b4 text-[--text-primary]">{props.spaceId}</span>
          <span
            className="cursor-pointer ml-1"
            onClick={async () => {
              await copyText(props.spaceId);
              toast(t.copy.copy_success, { variant: 'success' });
            }}
          >
            <CopyOutlined color="var(--text-secondary)" />
          </span>
        </div>
      </div>
      {showImageUpload && (
        <ImageCropUpload
          upload={async (file: File) =>
            editorContext.folder.uploadFolderLogo({ file, filePrefix: props.avatarFilePrefix ?? 'avatar' })
          }
          config={{
            unsplash: {
              unsplashDownload,
            },
          }}
          onClose={() => {
            setShowImageUpload(false);
          }}
          avatarName={props.name}
          // type={props.value?.type}
          confirm={(folderCover) => {
            setShowImageUpload(false);
            props.onChange(folderCover);
          }}
          value={props.value}
          // initPreview={
          //   <AvatarImg name={props.name} avatar={props.value} customSize={AvatarSize.Size120} shape="SQUARE" />
          // }
        />
      )}
    </div>
  );
}
