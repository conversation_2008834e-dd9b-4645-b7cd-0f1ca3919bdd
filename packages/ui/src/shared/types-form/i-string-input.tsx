'use client';

/* eslint-disable @typescript-eslint/no-explicit-any */
import assert from 'assert';
import FormHelperText from '@mui/joy/FormHelperText';
import { type Locale, iStringRecordSchema, type iString } from 'basenext/i18n';
import React, { useEffect } from 'react';
import type { ILocaleContext } from '@bika/contents/i18n/context';
import type { AIWriter } from '@bika/types/ai/bo';
import { AIWriterModal, AIWriterModalRef } from '@bika/ui/ai/writer';
import { Button, IconButton } from '@bika/ui/button';
import EditOutlined from '@bika/ui/icons/components/edit_outlined';
import GenerateAiFilled from '@bika/ui/icons/doc_hide_components/generate_ai_filled';
import { Stack, Box } from '@bika/ui/layouts';
import { ModalComponent } from '@bika/ui/modal-component';
import { I18NLangSelectInput } from './i18n-lang-select-input';
import { JsonObjectInput } from './json-object-input';
import { StringInput } from './string-input';
import { Tooltip } from '../../tooltip-components';

export interface IStringProps {
  label?: string;
  required?: boolean;
  helpText?: string;
  placeholder?: string;
  value: iString;
  onChange?: (iStr: iString) => void;
  locale: ILocaleContext;
  endDecorator?: React.ReactNode;
  textarea?: {
    minRows?: number;
  };
  setErrors?: (errors?: Record<string, string>) => void;
  aiWriter?: AIWriter[];
  readOnly?: boolean;
}
export function IStringInput(props: IStringProps) {
  const { setErrors, required, value } = props;
  const { lang, t } = props.locale;
  const [editingLang, setEditingLang] = React.useState<Locale>(() => lang as Locale);
  const ref = React.useRef<AIWriterModalRef>(null);
  useEffect(() => {
    if (required && setErrors) {
      setErrors({ nameInput: value ? '' : 'Name cannot be empty' });
    }
    return () => {
      if (required && setErrors) {
        setErrors({ nameInput: '' });
      }
    };
  }, [value, required]);

  const renderValue = React.useMemo(() => {
    if (typeof value === 'string') {
      return value;
    }
    // 如果是对象，但又没有当前语言的值，那么，显示空字符串
    // if (!editingLang) {
    //   console.warn('No editingLang');
    //   return value.en || '';
    // }
    return value[editingLang] || '';
  }, [value, editingLang]);

  const enableLocales: Locale[] | undefined = React.useMemo(() => {
    if (typeof value === 'string') return undefined;
    return Object.keys(value).map((v) => v as Locale);
  }, [value]);

  const MoreInfoComponent = () => {
    const [jsonMode, setJsonMode] = React.useState(false);
    const [currentValue, setCurrentValue] = React.useState<iString>(value);
    // 验证 currentValue,是否符合 iStringRecordSchema
    const isValid = iStringRecordSchema.safeParse(currentValue).success;

    if (jsonMode) {
      return (
        <JsonObjectInput
          validator={iStringRecordSchema}
          locale={props.locale}
          value={currentValue}
          onChange={(newVal) => {
            setCurrentValue(newVal);
            if (isValid) {
              props.onChange?.(newVal);
            }
          }}
        />
      );
    }
    return (
      <>
        <Box justifyContent="flex-end">
          <Button
            variant="plain"
            color="neutral"
            startDecorator={<EditOutlined color="var(--text-secondary)" />}
            onClick={() => {
              setJsonMode(true);
            }}
          >
            {t.buttons.edit}
          </Button>
        </Box>
        {Object.entries(value).map(([zLang, zStr]) => (
          <div key={zLang}>
            {zLang}: {zStr}
          </div>
        ))}
      </>
    );
  };

  return (
    <>
      {/* {JSON.stringify(editingLang)}
      {JSON.stringify(value)} */}
      <StringInput
        label={props.label || 'Name'}
        helpText={props.helpText}
        value={renderValue}
        placeholder={props.placeholder}
        readOnly={props.readOnly}
        onChange={(newVal) => {
          if (typeof props.value === 'string') props.onChange?.(newVal);
          else {
            props.onChange?.({
              ...props.value,
              [editingLang!]: newVal,
            });
          }
        }}
        textarea={props.textarea}
        endDecorator={
          <Stack
            // 靠右 flex-end
            display="flex"
            flexDirection="row"
            justifyContent="flex-end"
            alignItems="center"
            sx={{
              width: '100%',
            }}
            // 间隔
          >
            {props.aiWriter && (
              <Tooltip title={t.ai.name} variant="solid" arrow color="neutral" placement="top">
                <IconButton
                  onClick={() => {
                    ref.current?.open();
                  }}
                >
                  <GenerateAiFilled color="var(--text-secondary)" />
                </IconButton>
              </Tooltip>
            )}

            <I18NLangSelectInput
              locale={props.locale}
              value={typeof value === 'string' ? undefined : editingLang}
              enables={enableLocales}
              // onClickTranslateAll={() => {
              //   ref.current?.open('I18N_STRING');
              // }}
              onClickShowMoreInfo={() => {
                ModalComponent.show({
                  type: 'info',
                  title: t.components.configure_multilingual,
                  content: <MoreInfoComponent />,
                });
              }}
              onChange={(newLang, deleteLang?: Locale) => {
                if (!newLang) {
                  // 取消语言时，拿到当前语言的字符串
                  assert(typeof props.value !== 'string');
                  assert(editingLang);
                  // 删除单个语言
                  if (deleteLang) {
                    const newStr = { ...props.value };
                    delete newStr[deleteLang];
                    props.onChange?.(newStr);
                    return;
                  }

                  const pureStr = props.value[editingLang];
                  props.onChange?.(pureStr || '');

                  setEditingLang(lang); // 恢复用户当前默认，纯string时不影响渲染
                } else {
                  // 设置了lang，如果之前是纯string？那么，要把当前字符串设置到新lang上
                  if (typeof props.value === 'string') {
                    props.onChange?.({
                      [newLang]: props.value,
                    });
                  }
                  // 如果，切换了语言，但是当前语言没有字符串，那么，要把当前语言的字符串设置到新lang上
                  else if (!props.value[newLang]) {
                    props.onChange?.({
                      ...props.value,
                      [newLang]: props.value[editingLang] ?? '',
                    });
                  }
                  setEditingLang(newLang);
                }
              }}
            />
          </Stack>
        }
      />
      {props.aiWriter && (
        <AIWriterModal
          ref={ref}
          currentLocale={editingLang}
          writers={props.aiWriter}
          defaultUserPrompt={renderValue}
          onSubmit={(type, result) => {
            if (!result.value) return;
            if (type === 'REPLACE') {
              if (typeof result.value === 'string') {
                if (typeof value === 'string') {
                  props.onChange?.(result.value);
                } else {
                  props.onChange?.({ ...value, [editingLang!]: result.value });
                }
              } else {
                props.onChange?.(result.value);
              }
            } else if (typeof result.value === 'string') {
              if (typeof value === 'string') {
                props.onChange?.(renderValue + result.value);
              } else {
                props.onChange?.({ ...value, [editingLang!]: renderValue + result.value });
              }
            } else if (typeof result === 'object') {
              const newData: iString = {};
              for (const [k, v] of Object.entries(result.value)) {
                if (typeof value === 'string') {
                  return props.onChange?.(result.value);
                }
                if (k in value) {
                  // @ts-expect-error
                  newData[k] = value[k] + v;
                } else {
                  // @ts-expect-error
                  newData[k] = v;
                }
              }
              props.onChange?.(newData);
            }
          }}
        />
      )}
      {required && !value && (
        <FormHelperText sx={{ color: 'var(--status-danger)' }}>{'Cannot be empty'}</FormHelperText>
      )}
    </>
  );
}
