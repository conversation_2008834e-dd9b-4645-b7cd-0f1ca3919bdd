'use client';

import type { InputFieldBO } from '@toolsdk.ai/sdk-ts/types/bo';
import type React from 'react';
import { iStringParse } from 'basenext/i18n';
import { BooleanInput } from '@bika/ui/shared/types-form/boolean-input';
import { DatetimeInput } from '@bika/ui/shared/types-form/datetime-input';
import { NumberInput } from '@bika/ui/shared/types-form/number-input';
import { SelectInput } from '@bika/ui/shared/types-form/select-input';
import { StringInput } from '@bika/ui/shared/types-form/string-input';

export type CustomField = React.FC<{
  value: string;
  onChange: (value: string) => void;
  label?: string;
  required?: boolean;
  helpText?: string;
  style?: React.CSSProperties;
}>;

function choicesToOptions(choices: InputFieldBO['choices']) {
  if (!choices) return [];

  if (Array.isArray(choices)) {
    return choices.map((choice) => {
      if (typeof choice === 'string') {
        return {
          value: choice,
          label: choice,
        };
      }
      return {
        value: choice.value,
        label: choice.label || choice.value,
      };
    });
  }
  return Object.entries(choices).map(([value, label]) => ({
    value,
    label: String(label),
  }));
}

interface Props {
  // inputData
  value: unknown;
  inputField: InputFieldBO;
  onChange: (value: unknown) => void;
  customField?: CustomField;
}

export function InputField(props: Props) {
  const { inputField, customField } = props;
  if (props.inputField.type === 'string' || props.inputField.type === undefined) {
    const stringProps = {
      label: iStringParse(props.inputField.label) || inputField.key,
      required: props.inputField.required,
      helpText: iStringParse(props.inputField.helpText),
      value: props.value as string,
      onChange: (newVal: string) => {
        props.onChange(newVal);
      },
    };
    if (customField) {
      return customField({
        style: { minHeight: '40px' },
        ...stringProps,
      });
    }
    return <StringInput {...stringProps} />;
  }
  if (props.inputField.type === 'number') {
    return (
      <NumberInput
        label={iStringParse(props.inputField.label) || inputField.key}
        helpText={iStringParse(props.inputField.helpText)}
        required={props.inputField.required}
        value={props.value as number}
        onChange={(newVal) => {
          props.onChange(newVal);
        }}
      />
    );
  }
  if (inputField.type === 'boolean') {
    return (
      <BooleanInput
        label={iStringParse(inputField.label) || inputField.key}
        helpText={iStringParse(inputField.helpText)}
        required={inputField.required}
        value={props.value as boolean}
        onChange={(newVal) => {
          props.onChange(newVal);
        }}
      />
    );
  }
  if (inputField.type === 'datetime') {
    return (
      <DatetimeInput
        options={{
          timeZone: 'Asia/Shanghai',
        }}
        label={iStringParse(inputField.label) || inputField.key}
        helpText={iStringParse(inputField.helpText)}
        required={inputField.required}
        value={props.value as string}
        onChange={(newVal) => {
          props.onChange(newVal);
        }}
      />
    );
  }
  if (inputField.type === 'text') {
    const textProps = {
      label: iStringParse(inputField.label) || inputField.key,
      helpText: iStringParse(inputField.helpText),
      required: inputField.required,
      value: props.value as string,
      onChange: (newVal: string) => {
        props.onChange(newVal);
      },
    };
    if (customField) {
      return customField(textProps);
    }
    return (
      <StringInput
        textarea={{
          minRows: 3,
        }}
        {...textProps}
      />
    );
  }
  if (inputField.type === 'password') {
    return (
      <StringInput
        type="password"
        label={iStringParse(inputField.label) || inputField.key}
        helpText={iStringParse(inputField.helpText)}
        required={inputField.required}
        value={props.value as string}
        onChange={(newVal) => {
          props.onChange(newVal);
        }}
      />
    );
  }
  if (inputField.type === 'integer') {
    return (
      <StringInput
        type="number"
        label={iStringParse(inputField.label) || inputField.key}
        helpText={iStringParse(inputField.helpText)}
        required={inputField.required}
        value={props.value}
        onChange={(newVal) => {
          props.onChange(newVal);
        }}
      />
    );
  }
  if (inputField.type === 'select') {
    const options = choicesToOptions(inputField.choices);

    return (
      <SelectInput
        label={iStringParse(inputField.label) || inputField.key}
        helpText={iStringParse(inputField.helpText)}
        required={inputField.required}
        options={options}
        value={props.value as string}
        onChange={(newVal) => {
          props.onChange(newVal);
        }}
      />
    );
  }
}
