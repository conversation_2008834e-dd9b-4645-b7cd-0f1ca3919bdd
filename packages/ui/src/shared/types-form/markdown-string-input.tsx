'use client';

import assert from 'assert';
import { <PERSON>, Stack, Typography } from '@mui/joy';
import FormLabel from '@mui/joy/FormLabel';
import Link from '@mui/joy/Link';
import type { SxProps } from '@mui/joy/styles/types';
import { type iString, iStringRecordSchema, Locale } from 'basenext/i18n';
import React, { useMemo } from 'react';
import { createPortal } from 'react-dom';
import type { ILocaleContext } from '@bika/contents/i18n';
import type { AIWriter } from '@bika/types/ai/bo';
import { AIWriterModal, type AIWriterModalRef } from '@bika/ui/ai/writer';
import { Button, IconButton } from '@bika/ui/button';
import { Switch } from '@bika/ui/forms';
import EditOutlined from '@bika/ui/icons/components/edit_outlined';
import QuestionCircleOutlined from '@bika/ui/icons/components/question_circle_outlined';
import GenerateAiFilled from '@bika/ui/icons/doc_hide_components/generate_ai_filled';
import { Markdown } from '@bika/ui/markdown-component';
import { ModalComponent } from '@bika/ui/modal-component';
import { I18NLangSelectInput } from './i18n-lang-select-input';
import { JsonObjectInput } from './json-object-input';
import { CodeEditor } from '../../code-editor';
import { useCssColor } from '../../colors';
import ExpandOutlined from '../../icons/components/expand_outlined';
import NarrowOutlined from '../../icons/components/narrow_outlined';
import { Tooltip } from '../../tooltip-components';

interface Props {
  label?: string;
  value: iString;
  onChange: (value: iString) => void;
  helpLink?: {
    text: string | React.ReactNode;
    url: string;
  };
  locale: ILocaleContext;
  setErrors?: (errors?: Record<string, string>) => void;
  isMultiLang?: boolean;
  height?: string;
  aiWriter?: AIWriter[];
}

/**
 * 自动切换Markdown代码编辑和预览
 *
 * 标签页切换
 *
 * @returns
 */
export function MarkdownStringInput(props: Props) {
  const { value } = props;
  const [preview, setPreview] = React.useState(false);
  const [isFullScreen, setIsFullScreen] = React.useState(false);
  const { lang, t } = props.locale;
  const [editingLang, setEditingLang] = React.useState<Locale>(lang);
  const colors = useCssColor();
  const ref = React.useRef<AIWriterModalRef>(null);

  const enableLocales: Locale[] | undefined = React.useMemo(() => {
    if (typeof value === 'string') return undefined;
    return Object.keys(value).map((v) => v as Locale);
  }, [value]);

  const sx: SxProps = isFullScreen
    ? {
        width: '100vw',
        height: '100vh',
        position: 'fixed',
        top: 0,
        left: 0,
        zIndex: 1300,
        backgroundColor: 'var(--bg-page)',
        display: 'flex',
        flexDirection: 'column',
      }
    : {
        minWidth: '300px',
      };
  const code = useMemo(() => {
    if (props.value) {
      if (typeof props.value === 'string') {
        return props.value;
      }
      if (typeof props.value === 'object' && editingLang in props.value) {
        return props.value[editingLang] || '';
      }
    }
    return '';
  }, [props.value, editingLang]);

  const MoreInfoComponent = () => {
    const [jsonMode, setJsonMode] = React.useState(false);
    const [currentValue, setCurrentValue] = React.useState<iString>(value);
    // 验证 currentValue,是否符合 iStringRecordSchema
    const isValid = iStringRecordSchema.safeParse(currentValue).success;

    if (jsonMode) {
      return (
        <JsonObjectInput
          validator={iStringRecordSchema}
          locale={props.locale}
          value={currentValue}
          onChange={(newVal) => {
            setCurrentValue(newVal);
            if (isValid) {
              props.onChange(newVal);
            }
          }}
        />
      );
    }
    return (
      <>
        <Box justifyContent="flex-end">
          <Button
            variant="plain"
            color="neutral"
            startDecorator={<EditOutlined color="var(--text-secondary)" />}
            onClick={() => {
              setJsonMode(true);
            }}
          >
            {t.buttons.edit}
          </Button>
        </Box>
        {Object.entries(value).map(([zLang, zStr]) => (
          <div key={zLang}>
            {zLang}: {zStr}
          </div>
        ))}
      </>
    );
  };

  const renderSelectLang = () => (
    <I18NLangSelectInput
      hideDivider
      locale={props.locale}
      value={typeof value === 'string' ? undefined : editingLang}
      enables={enableLocales}
      // onClickTranslateAll={() => {
      //   ref.current?.open('I18N_STRING');
      // }}
      onClickShowMoreInfo={() => {
        ModalComponent.show({
          type: 'info',
          title: t.components.configure_multilingual,
          content: <MoreInfoComponent />,
        });
      }}
      onChange={(newLang, deleteLang?: Locale) => {
        if (!newLang) {
          // 取消语言时，拿到当前语言的字符串
          assert(typeof props.value !== 'string');
          assert(editingLang);
          // 删除单个语言
          if (deleteLang) {
            const newStr = { ...props.value };
            delete newStr[deleteLang];
            props.onChange(newStr);
            return;
          }

          const pureStr = props.value[editingLang];
          props.onChange(pureStr || '');

          setEditingLang(lang); // 恢复用户当前默认，纯string时不影响渲染
        } else {
          // 设置了lang，如果之前是纯string？那么，要把当前字符串设置到新lang上
          if (typeof props.value === 'string') {
            props.onChange({
              [newLang]: props.value,
            });
          }
          // 如果，切换了语言，但是当前语言没有字符串，那么，要把当前语言的字符串设置到新lang上
          else if (!props.value[newLang]) {
            props.onChange({
              ...props.value,
              [newLang]: props.value[editingLang],
            });
          }
          setEditingLang(newLang);
        }
      }}
    />
  );

  const content = (
    <Box sx={sx}>
      {props.label && (
        <Box display="flex" justifyContent="space-between" my={1} px={isFullScreen ? 2 : 0}>
          <FormLabel
            sx={{
              flexGrow: 1,
            }}
          >
            {props.label}{' '}
            {props.helpLink && (
              <Tooltip title={props.helpLink.text} variant="solid" arrow color="neutral" placement="top">
                <Link
                  href={props.helpLink.url}
                  target="_blank"
                  rel="noreferrer"
                  endDecorator={<QuestionCircleOutlined />}
                />
              </Tooltip>
            )}
          </FormLabel>
          <Stack flexShrink={0} display="flex" direction="row" alignItems="center">
            {preview && renderSelectLang()}

            <Tooltip
              title={isFullScreen ? t.global.action.zoom_out : t.global.action.full_screen}
              arrow
              placement="top"
            >
              <IconButton onClick={() => setIsFullScreen(!isFullScreen)}>
                {isFullScreen ? (
                  <NarrowOutlined color="var(--text-secondary)" />
                ) : (
                  <ExpandOutlined color="var(--text-secondary)" />
                )}
              </IconButton>
            </Tooltip>
            <Switch
              checked={preview}
              onChange={(event: React.ChangeEvent<HTMLInputElement>) => setPreview(event.target.checked)}
              sx={{ m: 1 }}
            />
            <Typography level="body-sm">{t.global.action.preview}</Typography>
          </Stack>
        </Box>
      )}
      <Box
        width={isFullScreen ? '80vw' : '100%'}
        ml={isFullScreen ? '10vw' : 0}
        sx={{
          flex: 1,
          display: 'flex',
          overflowY: 'auto',
          overflowX: 'hidden',
          flexDirection: 'column',
        }}
      >
        {!preview ? (
          <Box
            sx={{
              border: '1px solid var(--border-default)',
              borderRadius: '4px',
              overflow: 'hidden',
              backgroundColor: 'var(--bg-controls)',
              p: 0.5,
              ':focus-within': { borderColor: 'var(--brand)' },
            }}
          >
            <CodeEditor
              // 设置语言或者类型变更 刷新下组件
              key={`${editingLang}_${typeof value}`}
              code={code}
              language="markdown"
              onChange={(newVal) => {
                if (typeof value === 'string') {
                  props.onChange(newVal);
                } else {
                  props.onChange({
                    ...value,
                    [editingLang]: newVal,
                  });
                }
              }}
              width="100%"
              height={isFullScreen ? 'calc(100vh - 120px)' : props.height || '300px'}
              options={{
                lineNumbers: 'off',
              }}
            />
            <Stack direction="row" justifyContent="flex-end" mr={1}>
              {props.aiWriter && (
                <Tooltip title={t.ai.name} variant="solid" arrow color="neutral" placement="top">
                  <IconButton
                    onClick={() => {
                      ref.current?.open();
                    }}
                  >
                    <GenerateAiFilled />
                  </IconButton>
                </Tooltip>
              )}
              {renderSelectLang()}
            </Stack>
          </Box>
        ) : (
          <Box
            sx={{
              border: '1px solid var(--border-default)',
              p: 2,
              borderRadius: '6px',
              backgroundColor: 'var(--bg-controls)',
              position: 'relative',
              overflow: 'auto',
            }}
          >
            <Box
              sx={{
                position: 'absolute',
                top: '0',
                right: '0',
                backgroundColor: 'var(--border-default)',
                color: 'white',
                padding: '3px 6px',
                fontSize: '12px',
                borderBottomLeftRadius: '6px',
                lineHeight: '12px',
              }}
            >
              {t.global.action.preview}
            </Box>
            <div className={isFullScreen ? 'w-[800px] mx-auto' : ''}>
              <Markdown markdown={code} />
            </div>
          </Box>
        )}
      </Box>
      {props.aiWriter && (
        <AIWriterModal
          ref={ref}
          currentLocale={editingLang}
          writers={props.aiWriter}
          defaultUserPrompt={code}
          onSubmit={(type, result) => {
            if (!result.value) return;
            if (type === 'REPLACE') {
              if (typeof result.value === 'string') {
                if (typeof value === 'string') {
                  props.onChange(result.value);
                } else {
                  props.onChange({ ...value, [editingLang!]: result.value });
                }
              } else {
                props.onChange(result.value);
              }
            } else if (typeof result.value === 'string') {
              if (typeof value === 'string') {
                props.onChange(code + result.value);
              } else {
                props.onChange({ ...value, [editingLang!]: code + result.value });
              }
            } else if (typeof result === 'object') {
              const newData: iString = {};
              for (const [k, v] of Object.entries(result.value)) {
                if (typeof value === 'string') {
                  return props.onChange(result.value);
                }
                if (k in (value || {})) {
                  // @ts-expect-error
                  newData[k] = value[k] + v;
                } else {
                  // @ts-expect-error
                  newData[k] = v;
                }
              }
              props.onChange(newData);
            }
          }}
        />
      )}
    </Box>
  );
  if (typeof window !== 'undefined') {
    const body = document.querySelector('body') as HTMLBodyElement;
    return isFullScreen ? createPortal(content, body) : content;
  }
}
