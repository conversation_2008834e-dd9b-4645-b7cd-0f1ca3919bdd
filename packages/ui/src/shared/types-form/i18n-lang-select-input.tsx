/* eslint-disable @typescript-eslint/no-explicit-any */
import Divider from '@mui/joy/Divider';
import { i18n, iStringParse, type Locale } from 'basenext/i18n';
import classNames from 'classnames';
import React from 'react';
import { LocaleStringConfig } from '@bika/contents/config/client/locale';
import type { ILocaleContext } from '@bika/contents/i18n/context';
// import DeleteOutlined from '../../icons/components/delete_outlined';
import { Dropdown, Menu, MenuButton } from '@bika/ui/dropdown';
import { IconButton, MenuItem, Switch } from '@bika/ui/forms';
import CheckOutlined from '@bika/ui/icons/components/check_outlined';
import ChevronDownOutlined from '@bika/ui/icons/components/chevron_down_outlined';
import EyeOpenOutlined from '@bika/ui/icons/components/eye_open_outlined';
import Star2Outlined from '@bika/ui/icons/components/star_2_outlined';
import SubtractCircleFilled from '@bika/ui/icons/components/subtract_circle_filled';
import WebOutlined from '@bika/ui/icons/components/web_outlined';
import { Box, Stack } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/texts';
import { useCssColor } from '../../colors';
import { Modal } from '../../modal-component';

interface Props {
  value: Locale | undefined;
  onChange: (value: Locale | undefined, deleteValue?: Locale) => void;
  defaultLocale?: Locale;
  // 哪些是激活的
  enables?: Locale[];
  onClickShowMoreInfo?: () => void;
  onClickTranslateAll?: () => void;
  locale: ILocaleContext;
  hideDivider?: boolean;
}

export function I18NLangSelectInput(props: Props) {
  const { locale: myLocale, hideDivider } = props;
  const colors = useCssColor();
  const [open, setOpen] = React.useState(false);
  const { t } = myLocale;

  const isMultiple = !!props.enables && props.enables.length >= 1;
  const label = iStringParse(LocaleStringConfig[myLocale.lang], props.value);

  const switchWarning = () => {
    Modal.show({
      type: 'warning',
      title: t.components.confirm_remove_multilingual_configuration,
      onOk: () => {
        // 恢复单语言，取当前字符串
        props.onChange(undefined);
      },
      content: t.components.remove_multilingual_configuration_warning,
    });
  };

  return (
    <>
      {!hideDivider && <Divider orientation="vertical" sx={{ height: '16px', margin: 'auto 0' }} />}
      <Dropdown>
        <MenuButton
          variant="plain"
          sx={{
            minWidth: '38px',
            height: '38px',
            '&:hover': {
              backgroundColor: 'transparent',
            },
            p: '10px 8px',
            '&.MuiMenuButton-root': {
              fontWeight: 'normal',
            },
          }}
          onClick={() => {
            setOpen(!open);
          }}
        >
          {isMultiple ? (
            <Typography
              endDecorator={
                <ChevronDownOutlined
                  color={'var(--text-secondary)'}
                  className={classNames('transition-transform duration-300 ease-in-out transform', {
                    '-rotate-180': open,
                  })}
                />
              }
            >
              {label}
            </Typography>
          ) : (
            <WebOutlined size="16px" color={'var(--text-secondary)'} />
          )}
        </MenuButton>
        <Menu
          open={open}
          sx={{
            bgcolor: 'var(--bg-popup)',
            borderRadius: '4px',
            border: `1px solid ${'var(--border-default)'}`,
            boxShadow: 'var(--shadow-high)',
          }}
        >
          {isMultiple && (
            <>
              <Stack>
                {i18n.locales.map((locale, index) => {
                  const isEnable = props.enables?.includes(locale);

                  return (
                    <MenuItem
                      key={index}
                      onClick={() => {
                        props.onChange(locale);
                        setOpen(false);
                      }}
                    >
                      <Typography
                        flexGrow={1}
                        textColor={'var(--text-primary)'}
                        startDecorator={
                          <CheckOutlined size="16px" color={isEnable ? 'var(--status-success)' : 'transparent'} />
                        }
                      >
                        {iStringParse(LocaleStringConfig[myLocale.lang], locale)}
                      </Typography>
                      {isEnable && (
                        <IconButton
                          sx={{
                            '& > svg': {
                              fill: 'var(--text-secondary)',
                            },
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            e.preventDefault();
                            if ((props.enables ?? []).length > 1) {
                              props.onChange(undefined, locale);
                              return;
                            }
                            switchWarning();
                          }}
                        >
                          <SubtractCircleFilled />
                        </IconButton>
                      )}
                    </MenuItem>
                  );
                })}
              </Stack>

              <Divider sx={{ marginY: '4px' }} />

              <MenuItem
                onClick={() => {
                  setOpen(false);
                  props.onClickShowMoreInfo?.();
                }}
              >
                <Typography
                  textColor={'var(--text-primary)'}
                  startDecorator={<EyeOpenOutlined color={'var(--text-secondary)'} />}
                >
                  {t.components.view_all_languages}
                </Typography>
              </MenuItem>
              {props.onClickTranslateAll && (
                <MenuItem
                  onClick={() => {
                    setOpen(false);
                    props.onClickTranslateAll?.();
                  }}
                >
                  <Typography startDecorator={<Star2Outlined color={'var(--text-secondary)'} />}>
                    {t.ai.ai_translate_all}
                  </Typography>
                </MenuItem>
              )}

              <Divider sx={{ marginY: '4px' }} />
            </>
          )}
          <MenuItem
            sx={{ height: 'auto' }}
            onClick={() => {
              if (props.value) {
                switchWarning();
                setOpen(false);
              }
              props.onChange(props.defaultLocale || 'en');
            }}
          >
            <Stack direction="row" alignItems="center" sx={{ paddingY: '4px' }}>
              <WebOutlined color={'var(--text-secondary)'} />
              <Box width="204px" sx={{ marginX: '8px' }}>
                <Typography textColor={'var(--text-primary)'}>{t.components.configure_multilingual}</Typography>
                <Typography level="b4" textColor={'var(--text-secondary)'}>
                  {open ? t.components.disable_multilingual_warning : t.components.enable_multilingual_warning}
                </Typography>
              </Box>
              <Switch checked={isMultiple} size="lg" />
            </Stack>
          </MenuItem>
        </Menu>
      </Dropdown>
    </>
  );
}
