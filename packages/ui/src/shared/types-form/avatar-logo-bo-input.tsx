'use client';

import assert from 'assert';
import type { AvatarLogo } from 'basenext/avatar';
import React, { useState } from 'react';
import { useApiCaller } from '@bika/api-caller/context';
import type { ILocaleContext } from '@bika/contents/i18n';
import type { AIImageBOType } from '@bika/types/ai/bo';
import type { AttachmentVO } from '@bika/types/attachment/vo';
import type { ISpaceContext } from '@bika/types/space/context';
import { useGlobalState } from '@bika/types/website/context';
import { AvatarImg } from '../../components/avatar/avatar-component';
import { AvatarSize } from '../../components/avatar/avatar.enum';
import { ImageCropUpload } from '../../components/image-crop-upload/image-crop-upload';

interface Props {
  name: string;
  value: AvatarLogo | undefined;
  onChange(value: AvatarLogo | undefined): void;
  locale: ILocaleContext;
  avatarFilePrefix?: string;

  // 传入upload方法，才显示上传的处理
  upload?: (file: File) => Promise<AttachmentVO | undefined>;
  unsplashDownload?: (unsplashUrl: string) => Promise<string>;

  // 点击更新封面
  onClickChangeCover?: () => void;

  // AI配置
  aiConfig?: {
    type: Extract<AIImageBOType, 'node-resource-icon' | 'user-avatar'>;
    placeholder: string;
    defaultPrompt: string;
  };

  // 自定义更改封面文本
  changeCoverText?: string;
}
export function AvatarLogoBOInput(props: Props) {
  const [showImageUpload, setShowImageUpload] = useState(false);
  const { t } = props.locale;
  const { trpcQuery } = useApiCaller();
  const [globalSpaceContext] = useGlobalState<ISpaceContext>('SPACE');

  const generateImages = trpcQuery.ai.generateImages.useMutation();

  return (
    <>
      <div className={'w-[64px] h-[64px] overflow-hidden m-auto'}>
        <AvatarImg name={props.name} avatar={props.value} customSize={AvatarSize.Size64} shape="SQUARE" />
      </div>
      <div className={'text-b3 text-[--textLinkDefault] mt-2 text-center cursor-pointer'}>
        <span
          onClick={() => {
            if (props.onClickChangeCover) props.onClickChangeCover();
            else {
              // 默认的直接上传
              setShowImageUpload(true);
            }
          }}
        >
          {props.changeCoverText || t.resource.change_cover}
        </span>
      </div>
      {showImageUpload && props.upload && (
        <ImageCropUpload
          config={{
            unsplash: {
              unsplashDownload: props.unsplashDownload,
            },
            ai: {
              onAIGenerate: async (userPrompt) => {
                const attaches = await generateImages.mutateAsync({
                  type: props.aiConfig?.type || 'node-resource-icon',
                  prompt: userPrompt,
                  n: 1,
                  spaceId: globalSpaceContext?.data?.id ?? '',
                });
                return attaches.map((attach) => ({ relativePath: attach.path, attachmentId: attach.id }));
              },
              placeholder:
                props.aiConfig?.placeholder ||
                'Please input your requirement to generate an icon for this node resource',
              defaultPrompt: props.aiConfig?.defaultPrompt || `Generate an icon for ${props.name}`,
            },
          }}
          upload={async (file: File) => {
            assert(props.upload);
            return props.upload(file);
          }}
          allowTab={['EMOJI', 'ATTACHMENT', 'AI', 'UNSPLASH', 'PRESET', 'COLOR', 'URL']}
          onClose={() => {
            setShowImageUpload(false);
          }}
          avatarName={props.name}
          // type={props.value?.type}
          confirm={(folderCover) => {
            setShowImageUpload(false);
            props.onChange(folderCover);
          }}
          value={props.value}
          // initPreview={
          //   <AvatarImg name={props.name} avatar={props.value} customSize={AvatarSize.Size120} shape="SQUARE" />
          // }
        />
      )}
    </>
  );
}
