'use client';

import Avatar from '@mui/joy/Avatar';
import Box from '@mui/joy/Box';
import Stack from '@mui/joy/Stack';
import { iStringParse, type Locale } from 'basenext/i18n';
import { useEffect, useState } from 'react';
import { allVoicesOfCustomers } from '@bika/contents/testimonials/voice-of-customers/index';
import StarFilled from '@bika/ui/icons/components/star_filled';
import { Typography } from '@bika/ui/website/typography/index';

interface Props {
  locale: Locale;
  title?: string;
}

export function VoiceOfCustomers(props: Props) {
  const { locale } = props;
  const [columns, setColumns] = useState(3);

  // 响应式列数
  useEffect(() => {
    const updateColumns = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setColumns(1); // 手机端单列
      } else if (width < 1024) {
        setColumns(2); // 平板端双列
      } else {
        setColumns(3); // 桌面端三列
      }
    };

    updateColumns();
    window.addEventListener('resize', updateColumns);
    return () => window.removeEventListener('resize', updateColumns);
  }, []);

  // 将数据分配到不同列
  const distributeToColumns = () => {
    const cols: (typeof allVoicesOfCustomers)[] = Array.from({ length: columns }, () => []);

    allVoicesOfCustomers.forEach((item, index) => {
      const columnIndex = index % columns;
      cols[columnIndex].push(item);
    });

    return cols;
  };

  const title = props.title ? (
    <div
      style={{
        textAlign: 'center',
        marginBottom: '60px',
      }}
    >
      <Typography level={2}>{props.title}</Typography>
    </div>
  ) : null;

  const columnData = distributeToColumns();

  return (
    <div
      style={{
        padding: '80px 20px',
        position: 'relative',
      }}
    >
      {/* 内容层 */}
      <div style={{ position: 'relative', zIndex: 2, maxWidth: '1200px', margin: '0 auto' }}>
        {title}

        {/* 瀑布流布局 */}
        <Box
          sx={{
            display: 'flex',
            gap: { xs: 2, sm: 2.5, md: 2.5 },
            alignItems: 'flex-start',
            justifyContent: 'center',
          }}
        >
          {columnData.map((column, columnIndex) => (
            <Box
              key={columnIndex}
              sx={{
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                gap: { xs: 2, sm: 2.5, md: 2.5 },
                maxWidth: { xs: '100%', sm: '300px', md: '350px' },
              }}
            >
              {column.map((voc, index) => (
                <Stack
                  key={`${columnIndex}-${index}`}
                  sx={{
                    borderRadius: '20px',
                    p: { xs: 2.5, sm: 3 },
                    background: 'var(--bg-surface)',
                    border: '1px solid var(--border-default)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      background: 'var(--bg-controls-hover)',
                      transform: 'translateY(-5px)',
                      boxShadow: '0 10px 30px var(--bg-mask)',
                      borderColor: 'var(--border-on-brand)',
                    },
                  }}
                  direction="column"
                  spacing={2}
                >
                  {/* 星级评分 */}
                  <Stack spacing={0.5} direction="row">
                    {Array.from({ length: voc.stars || 5 }).map((_, i) => (
                      <StarFilled key={i} color="var(--status-warn)" />
                    ))}
                  </Stack>

                  {/* 评价内容 */}
                  <Box
                    sx={{
                      color: 'var(--text-primary)',
                      fontSize: { xs: '14px', sm: '15px' },
                      lineHeight: 1.6,
                      fontStyle: 'italic',
                    }}
                  >
                    "{iStringParse(voc.review, locale)}"
                  </Box>

                  {/* 用户信息 */}
                  <Stack direction="row" alignItems="center" spacing={1.5} mt={1}>
                    <Avatar
                      src={voc.avatarUrl}
                      size="md"
                      sx={{
                        border: '2px solid var(--border-default)',
                      }}
                    />
                    <Stack direction="column" spacing={0.5}>
                      <Box
                        sx={{
                          color: 'var(--text-primary)',
                          fontSize: { xs: '14px', sm: '15px' },
                          fontWeight: 600,
                        }}
                      >
                        {iStringParse(voc.author, locale)}
                      </Box>
                      {voc.role && (
                        <Box
                          sx={{
                            color: 'var(--text-secondary)',
                            fontSize: { xs: '12px', sm: '13px' },
                          }}
                        >
                          {iStringParse(voc.role, locale)}
                        </Box>
                      )}
                    </Stack>
                  </Stack>
                </Stack>
              ))}
            </Box>
          ))}
        </Box>
      </div>
    </div>
  );
}
