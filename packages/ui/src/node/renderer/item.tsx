'use client';

import { <PERSON><PERSON><PERSON>ogo, AvatarLogoSchema } from 'basenext/avatar';
import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import { useLocale } from '@bika/contents/i18n/context';
import type { NodeResourceType } from '@bika/types/node/bo';
import type { NodeMenuVO } from '@bika/types/node/vo';
import { IconButton } from '@bika/ui/button';
import DeleteOutlined from '@bika/ui/icons/components/delete_outlined';
import { Stack } from '@bika/ui/layouts';
import { EllipsisText } from '@bika/ui/text';
import { Typography } from '@bika/ui/texts';
import { Tooltip } from '@bika/ui/tooltip-components';
import style from './index.module.css';
import { NodeIcon } from '../icon';

interface NodeResourceItemProps {
  data?: NodeMenuVO;
  name?: string;
  icon?: Icon;
  type?: NodeResourceType;
  onClickDeleteItem?: (event: React.MouseEvent, itemId: string) => void;
  subIcon?: React.ReactNode;
}

type Icon = string | AvatarLogo | null | React.ReactNode;

export function NodeResourceItem(props: NodeResourceItemProps) {
  const locale = useLocale();
  const { t } = locale;

  const [value, setValue] = useState(props);

  useEffect(() => {
    if (props.data) {
      setValue({
        name: props.data.name,
        icon: props.data.icon,
        type: props.data.type,
        onClickDeleteItem: props.onClickDeleteItem,
        subIcon: props.subIcon,
      });
    }
  }, [props.data]);

  const { name, icon, type, onClickDeleteItem, subIcon } = value;
  const renderIcon = () => {
    if (icon == null && type != null) {
      return (
        <NodeIcon
          value={{ kind: 'node-resource', nodeType: type as NodeResourceType }}
          title={name}
          size={24}
          color="var(--text-secondary)"
        />
      );
    }
    const avatar = AvatarLogoSchema.safeParse(icon);
    if (avatar.success) {
      return (
        <NodeIcon
          value={{ kind: 'node-resource', customIcon: avatar.data, nodeType: type as NodeResourceType }}
          title={name}
          size={24}
          color="var(--text-secondary)"
        />
      );
    }
    if (typeof icon === 'string') {
      return <Image src={icon} width={16} height={16} alt="" style={{ borderRadius: '4px' }} />;
    }
    return React.isValidElement(icon) ? icon : null;
  };

  return (
    <Stack width="100%" height="100%" justifyContent="center" direction="column">
      <Stack
        width="100%"
        height="100%"
        justifyContent="space-between"
        alignItems="center"
        direction="row"
        sx={{
          '&:hover': {
            '.operation': {
              display: 'block',
            },
          },
        }}
      >
        <Stack direction="row" alignItems="center">
          <EllipsisText>
            <Typography startDecorator={renderIcon()} level="b2" textColor="var(--text-primary)">
              <span className={style.name} dangerouslySetInnerHTML={{ __html: name || '' }} />
            </Typography>
          </EllipsisText>
        </Stack>

        {subIcon ||
          (!!onClickDeleteItem && (
            <Tooltip title={t.action.delete} arrow placement="top">
              <IconButton
                color="danger"
                onClick={(e) => {
                  e.stopPropagation();
                  onClickDeleteItem(e, props.data?.id || '');
                }}
              >
                <DeleteOutlined className="operation hidden" color="var(--text-secondary)" />
              </IconButton>
            </Tooltip>
          ))}
      </Stack>
      {props.data?.path && (
        <Stack mt={0.5} direction="row" alignItems="center">
          <Typography level="b4" textColor="var(--text-secondary)">
            {`/${props.data?.path}`}
          </Typography>
        </Stack>
      )}
    </Stack>
  );
}
