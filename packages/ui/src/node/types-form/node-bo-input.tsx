import { type Avatar<PERSON>ogo } from 'basenext/avatar';
import React, { useMemo, useState, useCallback } from 'react';
import type { ILocaleContext } from '@bika/contents/i18n';
import { useResourceEditorContext } from '@bika/types/editor/context';
import type { EditorNodeFolderDTO } from '@bika/types/editor/dto';
import { NodeResourceSchema, type NodeResource, type NodeResourceType } from '@bika/types/node/bo';
import { useSpaceContextForce } from '@bika/types/space/context';
import type { ISpaceContext } from '@bika/types/space/context';
import { useGlobalState } from '@bika/types/website/context';
import { useAttachmentUpload } from '@bika/ui/components/image-crop-upload/index';
import { AvatarLogoBOInput } from '@bika/ui/shared/types-form/avatar-logo-bo-input';
import { getNodeResourceTypesOptions } from './node-constants';
import { NameDescriptionBOInput } from '../../shared/types-form/name-description-bo-input';
import { SelectInput } from '../../shared/types-form/select-input';

interface Props<T> {
  value: T;
  onChange(value: T): void;
  locale: ILocaleContext;
  setErrors?: (errors?: Record<string, string>) => void;
}
export function NodeBOInput<T extends NodeResource>(props: Props<T>) {
  const { setErrors } = props;
  const { t, i } = props.locale;
  const [showImageUpload, setShowImageUpload] = useState(false);
  const resourceType = props.value.resourceType;
  const { unsplashDownload } = useAttachmentUpload();
  const spaceContext = useSpaceContextForce();
  const [globalSpaceContext] = useGlobalState<ISpaceContext>('SPACE');
  const typesOptions = useMemo(
    () => getNodeResourceTypesOptions(props.locale).filter((item) => item.value !== 'FOLDER'),
    [props.locale],
  );
  const editorContext = useResourceEditorContext();
  // 每个form，用于编辑、修改，只有一个出口值。
  const [value, setValue] = React.useState<EditorNodeFolderDTO | undefined>();
  const setCover = useCallback((newCover: AvatarLogo) => {
    setValue((oldValue) => {
      if (!oldValue) return oldValue;
      return {
        ...oldValue,
        data: {
          ...oldValue.data,
          cover: newCover,
        },
      };
    });
  }, []);

  const cover: AvatarLogo = useMemo(() => {
    const icon = props.value.icon;
    if (icon) return icon as AvatarLogo;
    if (value?.nodeFolderType === 'TEMPLATE') {
      if (typeof value.template.cover === 'string') {
        return {
          type: 'URL',
          url: value.template.cover,
        };
      }
    }
    return {
      type: 'COLOR',
      color: 'BLUE',
    };
  }, [value]);

  return (
    <>
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', marginTop: '16px' }}>
        <AvatarLogoBOInput
          name={i(props.value.name)}
          value={props.value.icon}
          onChange={(avatar) => {
            props.onChange({ ...props.value, icon: avatar });
          }}
          locale={props.locale}
          upload={(file: File) => editorContext.api.node.uploadFile({ file, filePrefix: 'node' })}
          unsplashDownload={unsplashDownload}
          avatarFilePrefix="folder"
          aiConfig={{
            type: 'node-resource-icon' as const,
            placeholder: 'Please input your requirement to generate an icon for this node resource',
            defaultPrompt: `Generate an icon for ${i(props.value.name)}`,
          }}
        />
      </div>
      <SelectInput<NodeResourceType>
        label={t.resource.title_resource_type}
        options={typesOptions}
        value={resourceType}
        disabled={true}
        onChange={(newValue) => {
          if (!newValue) {
            return;
          }
          const clone = NodeResourceSchema.parse({
            ...props.value,
            resourceType: newValue,
          });
          props.onChange(clone as T);
        }}
      />
      <NameDescriptionBOInput
        labels={{
          name: t.resource.title_resource_name,
          description: t.resource.title_resource_description,
        }}
        value={{
          name: props.value.name,
          description: props.value.description,
        }}
        onChange={(newData) => {
          props.onChange({ ...props.value, ...newData });
        }}
        locale={props.locale}
        setErrors={setErrors}
        context={{ resource: props.value }}
      />
    </>
  );
}
