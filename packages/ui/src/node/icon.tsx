import assert from 'assert';
import React from 'react';
import type { NodeResourceType, INodeIconValue, EnumIconType } from '@bika/types/node/bo';
import type { TalkExpertKey } from '@bika/types/space/bo';
import type { AvatarLogo } from 'basenext/avatar';
import { AvatarImg } from '@bika/ui/components/avatar/index';
import type { IIconProps } from '@bika/ui/icons';
import AddOutlined from '@bika/ui/icons/components/add_outlined';
import AttachmentOutlined from '@bika/ui/icons/components/attachment_outlined';
import CheckboxOutlined from '@bika/ui/icons/components/checkbox_outlined';
import DeleteOutlined from '@bika/ui/icons/components/delete_outlined';
import FileOutlined from '@bika/ui/icons/components/file_outlined';
import FolderNormalOutlined from '@bika/ui/icons/components/folder_normal_outlined';
import FormOutlined from '@bika/ui/icons/components/form_outlined';
import ImportOutlined from '@bika/ui/icons/components/import_outlined';
import ManageApplicationOutlined from '@bika/ui/icons/components/manage_application_outlined';
import MirrorOutlined from '@bika/ui/icons/components/mirror_outlined';
import PlanetOutlined from '@bika/ui/icons/components/planet_outlined';
import RobotOutlined from '@bika/ui/icons/components/robot_outlined';
import RocketOutlined from '@bika/ui/icons/components/rocket_outlined';
import ArchitectureOutlined from '@bika/ui/icons/doc_hide_components/architecture_outlined';
import BrainOutlined from '@bika/ui/icons/doc_hide_components/brain_outlined';
import BuilderOutlined from '@bika/ui/icons/doc_hide_components/builder_outlined';
import EmbedOutlined from '@bika/ui/icons/doc_hide_components/embed_outlined';
import NodeAgentOutlined from '@bika/ui/icons/doc_hide_components/node_agent_outlined';
import NodeAipageOutlined from '@bika/ui/icons/doc_hide_components/node_aipage_outlined';
import NodeAutomationOutlined from '@bika/ui/icons/doc_hide_components/node_automation_outlined';
import NodeDashboardOutlined from '@bika/ui/icons/doc_hide_components/node_dashboard_outlined';
import NodeDatabaseOutlined from '@bika/ui/icons/doc_hide_components/node_database_outlined';
import { Box } from '@bika/ui/layouts';
import { loadIcon } from '../icons';

export type INodeIconCfg = {
  name: string;
  color: string;
  bg: string;
  Icon?: React.FC<IIconProps>;
  avatar?: AvatarLogo;
};
const talkExpertsIconItems: Record<TalkExpertKey, INodeIconCfg> = {
  mission: {
    name: 'Mission',
    color: 'var(--rainbow-green5)',
    bg: 'var(--rainbow-green1)',
    Icon: CheckboxOutlined,
  },
  report: {
    name: 'Report',
    color: 'var(--rainbow-blue5)',
    bg: 'var(--rainbow-blue1)',
    Icon: FileOutlined,
  },
  'template-center': {
    name: 'Template-center',
    color: 'var(--rainbow-purple5)',
    bg: 'var(--rainbow-purple1)',
    Icon: PlanetOutlined,
  },
  trash: {
    name: 'Trash',
    color: 'var(--rainbow-teal5)',
    bg: 'var(--rainbow-teal1)',
    Icon: DeleteOutlined,
  },
  // 'app-store': {
  //   name: 'App Store',
  //   color: 'var(--static)',
  //   bg: 'var(--bg-controls)',
  //   Icon: RobotOutlined,
  // },
  space: {
    name: 'Space launcher',
    color: 'var(--rainbow-purple5)',
    bg: 'var(--rainbow-purple1)',
    Icon: RocketOutlined,
  },
  supervisor: {
    name: 'Space Assistant',
    color: 'var(--rainbow-orange5)',
    bg: 'var(--rainbow-orange1)',
    Icon: BrainOutlined,
  },
  builder: {
    name: 'Creator',
    color: 'var(--rainbow-indigo5)',
    bg: 'var(--rainbow-indigo1)',
    Icon: BuilderOutlined,
  },
};

export type TalkExpertIconType = keyof typeof talkExpertsIconItems;

// 特殊的业务，自定义的其它的 业务
const enumIconItems: Record<EnumIconType, INodeIconCfg> = {
  DATUM: {
    name: 'Database',
    color: 'var(--rainbow-teal5)',
    bg: 'var(--bg-controls)',
    Icon: NodeDatabaseOutlined,
  },
  release_notes: {
    name: 'Release Notes',
    color: 'var(--rainbow-brown5)',
    bg: 'var(--bg-controls)',
    Icon: FileOutlined,
  },

  workflow: {
    name: 'Workflow',
    color: 'var(--rainbow-green5)',
    bg: 'var(--bg-controls)',
    Icon: ArchitectureOutlined,
  },

  // debugger: {
  //   name: 'Debugger',
  //   color: 'var(--static)',
  // bg: 'var(--bg-controls)',
  //   Icon: RobotOutlined,
  // },
  IMPORT: {
    name: 'Import',
    color: 'var(--rainbow-gray5)',
    bg: 'var(--bg-controls)',
    Icon: ImportOutlined,
  },
  create: {
    name: 'Create',
    color: 'var(--rainbow-purple5)',
    bg: 'var(--bg-controls)',
    Icon: AddOutlined,
  },
};

/**
 *
 */
const nodeResourcesIconItems: Record<NodeResourceType, INodeIconCfg> = {
  AUTOMATION: {
    name: 'Automation',
    color: 'var(--rainbow-red5)',
    bg: 'var(--bg-controls)',
    Icon: NodeAutomationOutlined,
  },
  DOCUMENT: {
    name: 'Document',
    color: 'var(--rainbow-indigo5)',
    bg: 'var(--bg-controls)',
    Icon: FileOutlined,
  },

  DATABASE: {
    name: 'Database',
    color: 'var(--rainbow-teal5)',
    bg: 'var(--bg-controls)',
    Icon: NodeDatabaseOutlined,
  },
  AI: {
    name: 'AI',
    color: 'var(--rainbow-purple5)',
    bg: 'var(--bg-controls)',
    Icon: NodeAgentOutlined,
  },
  PAGE: {
    name: 'Page',
    color: 'var(--rainbow-blue5)',
    bg: 'var(--bg-controls)',
    Icon: NodeAipageOutlined,
  },
  EMBED: {
    name: 'Embed',
    color: 'var(--rainbow-blue5)',
    bg: 'var(--bg-controls)',
    Icon: EmbedOutlined,
  },
  // VIEW: {
  //   name: 'View',
  //   color: 'var(--rainbow-orange5)',
  //   bg: 'var(--bg-controls)',
  //   Icon: GridOutlined,
  // },
  DASHBOARD: {
    name: 'Dashboard',
    color: 'var(--rainbow-indigo5)',
    bg: 'var(--bg-controls)',
    Icon: NodeDashboardOutlined,
  },
  FORM: {
    name: 'Form',
    color: 'var(--rainbow-purple5)',
    bg: 'var(--bg-controls)',
    Icon: FormOutlined,
  },
  MIRROR: {
    name: 'Mirror',
    color: 'var(--rainbow-teal5)',
    bg: 'var(--bg-controls)',
    Icon: MirrorOutlined,
  },
  DATAPAGE: {
    name: 'Integration',
    color: 'var(--rainbow-blue5)',
    bg: 'var(--bg-controls)',
    Icon: ManageApplicationOutlined,
  },

  FOLDER: {
    name: 'Folder',
    color: 'var(--rainbow-orange5)',
    bg: 'var(--bg-controls)',
    Icon: FolderNormalOutlined,
  },
  TEMPLATE: {
    name: 'Template',
    color: 'var(--rainbow-pink5)',
    bg: 'var(--bg-controls)',
    Icon: ManageApplicationOutlined,
  },
  CANVAS: {
    name: 'Workflow',
    color: 'var(--rainbow-green5)',
    bg: 'var(--bg-controls)',
    Icon: ArchitectureOutlined,
  },
  REPORT_TEMPLATE: {
    name: 'Release Notes',
    color: 'var(--rainbow-purple5)',
    bg: 'var(--bg-controls)',
    Icon: FileOutlined,
  },
  ALIAS: {
    name: 'Attachment',
    color: 'var(--rainbow-blue5)',
    bg: 'var(--bg-controls)',
    Icon: AttachmentOutlined,
  },
  FILE: {
    name: 'File',
    color: 'var(--rainbow-brown5)',
    bg: 'var(--bg-controls)',
    Icon: AttachmentOutlined,
  },
  ROOT: {
    name: 'Expert',
    color: 'var(--rainbow-blue5)',
    bg: 'var(--bg-controls)',
    Icon: RobotOutlined,
  },
};

export type NodeResourceIconType = keyof typeof nodeResourcesIconItems;

interface Props {
  value: INodeIconValue;

  // NodeResourceType | TalkExpertKey;
  color?: string;
  size?: number;
  isRound?: boolean;
  // title is required for text on top of simple backgroud of avatar
  title?: string;
}

export const ICON_SIZE_RATIO = 16 / 24;

/**
 * Talk Icon，业务图标通关，内部使用 Avatar Logo
 *
 * 避免到处乱用
 *
 * 层次关系： Talk Icon -> Avatar Logo -> 各种<img 等
 * @param props
 * @returns
 */
function NodeIconBase(props: Props) {
  const { value, size = 32, isRound, title, color } = props;

  if (value === undefined) {
    console.trace();
    assert(value, 'NodeIcon value is required');
  }
  if (value.kind === 'avatar') {
    return (
      <AvatarImg
        avatar={value.avatar}
        name={value.name}
        title={title}
        customSize={size}
        shape={isRound ? 'CIRCLE' : 'SQUARE'}
      />
    );
  }

  let Icon: React.FC<IIconProps> | undefined;
  let avatar: AvatarLogo | undefined;
  let forceIconComponent: React.ReactNode = null;
  let iconItem: INodeIconCfg;
  const kind = value.kind;
  if (value.kind === 'expert') {
    iconItem = talkExpertsIconItems[value.expertKey];
    Icon = iconItem.Icon;
    avatar = iconItem.avatar;
  } else if (value.kind === 'node-resource') {
    iconItem = nodeResourcesIconItems[value.nodeType];
    assert(iconItem, `Unknown node resource type: ${JSON.stringify(value)}`);
    Icon = iconItem.Icon;
    if (value.customIcon) {
      forceIconComponent = (
        <AvatarImg
          avatar={value.customIcon}
          name={title}
          title={title}
          customSize={size}
          shape={isRound ? 'CIRCLE' : 'SQUARE'}
        />
      );
    }
  } else if (value.kind === 'enum') {
    iconItem = enumIconItems[value.enum];
    Icon = iconItem.Icon;
  } else if (value.kind === 'icon') {
    // For IconType icons, we'll handle them separately using loadIcon
    Icon = loadIcon(value.icon) as React.FC<IIconProps>;
    iconItem = {
      name: 'Icon',
      color: color || 'var(--text-primary)',
      bg: color ? 'transparent' : 'var(--bg-controls)',
    };
  } else {
    throw new Error(`Unknown icon type: ${kind}`);
  }

  if (avatar) {
    return <AvatarImg avatar={avatar} customSize={size} shape={isRound ? 'CIRCLE' : 'SQUARE'} />;
  }

  return (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      flexShrink={0}
      sx={{
        width: size,
        height: size,
        // eslint-disable-next-line no-nested-ternary
        borderRadius: isRound ? '50%' : size < 32 ? '4px' : '6px',
        backgroundColor: iconItem.bg,
      }}
    >
      {forceIconComponent || (Icon && <Icon color={`${iconItem.color}!important`} size={size * ICON_SIZE_RATIO} />)}
    </Box>
  );
}

export const NodeIcon = React.memo(NodeIconBase);

export const getResourceIconMap = (nodeType: NodeResourceType, size = 32) => (
  <NodeIcon value={{ kind: 'node-resource', nodeType }} size={size} />
);
