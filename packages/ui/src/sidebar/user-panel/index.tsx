'use client';

import React from 'react';
import { useLocale } from '@bika/contents/i18n/context';
import type { AvatarLogo } from 'basenext/avatar';
import type { MemberVO, TeamVO } from '@bika/types/unit/vo';
import { useCssColor } from '@bika/ui/colors';
import CopyOutlined from '@bika/ui/icons/components/copy_outlined';
import EditOutlined from '@bika/ui/icons/components/edit_outlined';
import GiftOutlined from '@bika/ui/icons/components/gift_outlined';
import { Box, Divider, Stack } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/texts';
import { copyText } from '@bika/ui/utils';
import style from './style.module.css';
import { AvatarImg } from '../../components/avatar/avatar-component';
import { AvatarSize } from '../../components/avatar/avatar.enum';
import { useSnackBar } from '../../snackbar/snackbar-component';
import { EllipsisText } from '../../text';

interface IUserProps {
  memberName?: string;
  member?: MemberVO | null;
  avatar?: AvatarLogo;
  email?: string | null;
  nickName?: string;
  referralCode?: string;
  children?: React.ReactElement<any>[];
  appStore?: React.ReactElement;
  setMemberName?: (name?: string) => void;
  setUserProfile?: () => void;
  handleBindEmail?: () => void;
  handleInviteMember?: () => void;
  isUser?: boolean;
}

export function UserPanelComponent(props: IUserProps) {
  const colors = useCssColor();
  const { member, referralCode, isUser = false } = props;
  const email = props.email || member?.email;
  // const roles = member?.roles;
  const teams = member?.teams;
  const memberName = props.memberName || member?.name;
  const nickName = props.nickName || member?.nickName;
  const { t } = useLocale();
  const { toast } = useSnackBar();

  const InfoRow = ({
    label,
    value,
    endDecorator,
    handleClick,
  }: {
    label: string;
    value: string | TeamVO[] | undefined;
    endDecorator?: React.ReactElement;
    handleClick?: () => void;
  }) => {
    const renderValue = () => {
      if (typeof value === 'string') {
        return (
          <EllipsisText>
            <Typography
              textColor={`${'var(--text-primary)'}!important`}
              level="b3"
              sx={{
                cursor: handleClick ? 'pointer' : 'default',
              }}
              onClick={handleClick}
            >
              {value}
            </Typography>
          </EllipsisText>
        );
      }
      if (Array.isArray(value) && value.length) {
        return (
          <Box width={'calc(100% - 40%)'}>
            {value.map((item) => (
              <EllipsisText key={item.id}>
                <Typography
                  textColor={`${'var(--text-primary)'}!important`}
                  level="b3"
                  sx={{
                    cursor: handleClick ? 'pointer' : 'default',
                  }}
                  onClick={handleClick}
                >
                  {item.path}
                </Typography>
              </EllipsisText>
            ))}
          </Box>
        );
      }
      return null;
    };
    if (!renderValue()) return null;
    return (
      <Stack width={'100%'} direction={'row'} alignItems="center" justifyContent={'flex-start'} gap={0.5}>
        <Typography textColor={`${'var(--text-secondary)'}!important`} width={'35%'} flexShrink={0} level="b4">
          {label}
        </Typography>

        {renderValue()}
        {endDecorator}
      </Stack>
    );
  };

  return (
    <Stack
      direction={'column'}
      width={'320px'}
      overflow={'hidden'}
      borderRadius="8px"
      border={`1px solid ${'var(--border-default)'}`}
      boxShadow={'var(--shadow-default)'}
      bgcolor={'var(--bg-popup)'}
    >
      <Box className={style.userInfoTitle}>
        <Stack direction={'row'} alignItems={'center'}>
          <Box
            sx={{ cursor: 'pointer', mr: '16px', position: 'relative', '&:hover div': { visibility: 'visible' } }}
            onClick={props.setUserProfile}
          >
            <AvatarImg
              alt={memberName || nickName}
              avatar={member?.avatar || props.avatar}
              name={memberName || nickName}
              customSize={AvatarSize.Size40}
            />
            {isUser && (
              <Box
                width={'100%'}
                height={'100%'}
                borderRadius={'50%'}
                position={'absolute'}
                top={0}
                left={0}
                bgcolor={colors.deepMaskColor}
                visibility={'hidden'}
                display={'flex'}
                justifyContent={'center'}
                alignItems={'center'}
              >
                <EditOutlined />
              </Box>
            )}
          </Box>
          <Box width={'calc(100% - 52px)'}>
            <EllipsisText>
              <Typography
                textColor={'var(--static) !important'}
                textOverflow={'ellipsis'}
                whiteSpace={'nowrap'}
                overflow={'hidden'}
                level="h6"
              >
                {memberName}
              </Typography>
            </EllipsisText>
            {/* {!!roles?.length && (
              <EllipsisText>
                <Typography textOverflow={'ellipsis'} overflow={'hidden'}>
                  {roles.map((role) => role.name).join(',')}
                </Typography>
              </EllipsisText>
            )} */}
            {!!teams?.length && (
              <EllipsisText>
                <Typography textColor={'var(--static)'} textOverflow={'ellipsis'} overflow={'hidden'}>
                  {teams.map((team) => team.name).join(',')}
                </Typography>
              </EllipsisText>
            )}
          </Box>
        </Stack>
      </Box>

      <Stack direction={'column'} alignItems={'flex-start'} p={'12px 16px'} spacing={'11px'}>
        {isUser && (
          <InfoRow
            label={t.user.member_name}
            value={memberName}
            endDecorator={
              <EditOutlined
                className="cursor-pointer flex-shrink-0 m-t-[2px]"
                onClick={() => props.setMemberName?.(memberName)}
                color="var(--text-secondary)"
              />
            }
          />
        )}
        <InfoRow label={t.team.team} value={teams} />
        <InfoRow
          label={t.user.nickname}
          value={nickName}
          endDecorator={
            isUser ? (
              <EditOutlined
                className="cursor-pointer flex-shrink-0 m-t-[2px]"
                onClick={props.setUserProfile}
                color="var(--text-secondary)"
              />
            ) : undefined
          }
        />
        <InfoRow
          label={t.user.email}
          value={email || (isUser ? t.user.bind_email : undefined)}
          handleClick={!email ? props.handleBindEmail : undefined}
        />
        <InfoRow
          label={t.referral.referral_code}
          value={referralCode}
          endDecorator={
            <CopyOutlined
              className="cursor-pointer flex-shrink-0 m-t-[2px]"
              onClick={async () => {
                await copyText(referralCode!);
                toast(t.copy.copy_success, { variant: 'success' });
              }}
              color="var(--text-secondary)"
            />
          }
        />
        {isUser && (
          <Typography
            level="b4"
            sx={{ cursor: 'pointer' }}
            alignItems={'flex-start'}
            textColor={'var(--brand)'}
            startDecorator={<GiftOutlined color={'var(--brand)'} />}
            onClick={props.handleInviteMember}
          >
            {t.user.invite_your_friends_to_register_and_get_1000_bk_coins}
          </Typography>
        )}
      </Stack>
      {props.children && (
        <Divider
          sx={{
            mx: '16px',
            bgcolor: 'var(--border-default)',
          }}
        />
      )}
      {/* other settings */}
      <Stack
        direction={'column'}
        sx={{
          px: '8px',
          pt: '8px',
          '&>div': {
            height: '32px',
          },
          pb: '12px',
        }}
      >
        {props.children}
        {props.appStore}
      </Stack>
    </Stack>
  );
}
