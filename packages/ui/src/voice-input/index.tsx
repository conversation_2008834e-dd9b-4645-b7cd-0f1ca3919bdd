import 'regenerator-runtime/runtime'; // for `react-speech-recognition`
import { Box } from '@mui/joy';
import { useUpdateEffect } from 'ahooks';
import React, { CSSProperties, FC, useEffect, forwardRef, useImperativeHandle } from 'react';
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';
import { useLocale } from '@bika/contents/i18n';
import { Locale } from 'basenext/i18n';
import VoiceAiFilled from '@bika/ui/icons/doc_hide_components/voice_ai_filled';
import VoiceAiOutlined from '@bika/ui/icons/doc_hide_components/voice_ai_outlined';
import { IconButton } from '../button-component';
import { useCssColor } from '../colors';
import { Tooltip } from '../tooltip-components';

export const VoiceButton: FC<{
  disabled?: boolean;
  onClick?: () => void;
  sx?: CSSProperties;
}> = ({ onClick, sx, disabled }) => {
  const { t } = useLocale();
  const colors = useCssColor();

  return (
    <Tooltip title={disabled ? t.editor.microphone_disabled : t.editor.use_micro_phone}>
      <span>
        <IconButton
          size="sm"
          onClick={disabled ? undefined : onClick}
          sx={{ width: 32, height: 32, borderRadius: '4px', ...sx }}
          disabled={disabled}
        >
          <VoiceAiFilled color={'var(--text-secondary'} />
        </IconButton>
      </span>
    </Tooltip>
  );
};

export const VoiceListeningButton: FC<{
  onClick?: () => void;
}> = ({ onClick }) => {
  const { t } = useLocale();
  const colors = useCssColor();

  return (
    <Tooltip title={t.editor.stop_microphone}>
      <span className="relative flex" onClick={onClick} style={{ cursor: 'pointer' }}>
        <span
          className="animate-ping animate-ping-voice-input absolute inline-flex h-full w-full rounded-full  "
          style={{
            backgroundColor: 'var(--brand)',
          }}
        ></span>
        <span
          className="relative inline-flex rounded-full h-[32px] w-[32px]  opacity-15 justify-center items-center"
          style={{
            backgroundColor: 'var(--brand)',
          }}
        >
          <VoiceAiOutlined color={'var(--text-primary)'} />
        </span>
      </span>
    </Tooltip>
  );
};

export const VoiceInput2 = () => (
  <button
    type="button"
    className="ant-btn css-var-«rku» ant-btn-text ant-btn-color-primary ant-btn-variant-text ant-btn-icon-only ant-sender-actions-btn"
  >
    <span className="ant-btn-icon">
      <span role="img" aria-label="audio" className="anticon anticon-audio">
        <svg
          viewBox="64 64 896 896"
          focusable="false"
          data-icon="audio"
          width="1em"
          height="1em"
          fill="currentColor"
          aria-hidden="true"
        >
          <path d="M842 454c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 140.3-113.7 254-254 254S258 594.3 258 454c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 168.7 126.6 307.9 290 327.6V884H326.7c-13.7 0-24.7 14.3-24.7 32v36c0 4.4 2.8 8 6.2 8h407.6c3.4 0 6.2-3.6 6.2-8v-36c0-17.7-11-32-24.7-32H548V782.1c165.3-18 294-158 294-328.1zM512 624c93.9 0 170-75.2 170-168V232c0-92.8-76.1-168-170-168s-170 75.2-170 168v224c0 92.8 76.1 168 170 168zm-94-392c0-50.6 41.9-92 94-92s94 41.4 94 92v224c0 50.6-41.9 92-94 92s-94-41.4-94-92V232z"></path>
        </svg>
      </span>
    </span>
  </button>
);

const VoiceInput3 = () => (
  <button
    type="button"
    style={
      {
        '--color-primary': '#fc4189',
      } as CSSProperties
    }
    className="ant-btn css-var-«rku» ant-btn-text ant-btn-color-primary ant-btn-variant-text ant-btn-icon-only ant-sender-actions-btn w-[24px] h-[24px]"
  >
    <span className="ant-btn-icon">
      <svg
        color="var(--color-primary)"
        viewBox="0 0 1000 1000"
        xmlns="http://www.w3.org/2000/svg"
        className="ant-sender-actions-btn-recording-icon"
      >
        <title>Speech Recording</title>
        <rect fill="var(--color-primary)" rx="70" ry="70" height="250" width="140" x="0" y="375">
          <animate
            attributeName="height"
            values="250; 500; 250"
            keyTimes="0; 0.5; 1"
            dur="0.8s"
            begin="0s"
            repeatCount="indefinite"
          ></animate>
          <animate
            attributeName="y"
            values="375; 250; 375"
            keyTimes="0; 0.5; 1"
            dur="0.8s"
            begin="0s"
            repeatCount="indefinite"
          ></animate>
        </rect>
        <rect fill="var(--color-primary)" rx="70" ry="70" height="250" width="140" x="286.66666666666663" y="375">
          <animate
            attributeName="height"
            values="250; 500; 250"
            keyTimes="0; 0.5; 1"
            dur="0.8s"
            begin="0.2s"
            repeatCount="indefinite"
          ></animate>
          <animate
            attributeName="y"
            values="375; 250; 375"
            keyTimes="0; 0.5; 1"
            dur="0.8s"
            begin="0.2s"
            repeatCount="indefinite"
          ></animate>
        </rect>
        <rect fill="var(--color-primary)" rx="70" ry="70" height="250" width="140" x="573.3333333333333" y="375">
          <animate
            attributeName="height"
            values="250; 500; 250"
            keyTimes="0; 0.5; 1"
            dur="0.8s"
            begin="0.4s"
            repeatCount="indefinite"
          ></animate>
          <animate
            attributeName="y"
            values="375; 250; 375"
            keyTimes="0; 0.5; 1"
            dur="0.8s"
            begin="0.4s"
            repeatCount="indefinite"
          ></animate>
        </rect>
        <rect fill="var(--color-primary)" rx="70" ry="70" height="250" width="140" x="859.9999999999999" y="375">
          <animate
            attributeName="height"
            values="250; 500; 250"
            keyTimes="0; 0.5; 1"
            dur="0.8s"
            begin="0.6000000000000001s"
            repeatCount="indefinite"
          ></animate>
          <animate
            attributeName="y"
            values="375; 250; 375"
            keyTimes="0; 0.5; 1"
            dur="0.8s"
            begin="0.6000000000000001s"
            repeatCount="indefinite"
          ></animate>
        </rect>
      </svg>
    </span>
  </button>
);

const getLanuage = (lang: Locale) => {
  switch (lang) {
    case 'zh-CN':
      return 'zh-CN';
    case 'zh-TW':
      return 'zh-TW';
    case 'en':
      return 'en-US';
    case 'ja':
      return 'ja-JP';
    default:
      return 'en-US';
  }
};

export interface VoiceInputHandle {
  stopListening: () => void;
}

export const VoiceInput = forwardRef<
  VoiceInputHandle,
  {
    locale?: Locale;
    onChange?: (transcript: string) => void;
  }
>(({ locale, onChange }, ref) => {
  const {
    transcript,
    listening,
    resetTranscript,
    isMicrophoneAvailable,
    // @ts-ignore
    browserSupportsContinuousListening,
    browserSupportsSpeechRecognition,
  } = useSpeechRecognition();

  useImperativeHandle(
    ref,
    () => ({
      stopListening: SpeechRecognition.stopListening,
    }),
    [],
  );

  useUpdateEffect(() => {
    console.log(transcript);
    onChange?.(transcript);
  }, [transcript]);

  useEffect(() => {}, [browserSupportsSpeechRecognition]);

  if (!browserSupportsSpeechRecognition) {
    return null;
    // <span>Browser doesn't support speech recognition.</span>;
  }

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', width: '32px', height: '32px' }}>
      {!listening && (
        <VoiceButton
          disabled={!isMicrophoneAvailable}
          onClick={() => {
            SpeechRecognition.startListening({
              continuous: browserSupportsContinuousListening !== false,
              interimResults: true,
              language: locale ? getLanuage(locale) : undefined,
            });
          }}
        ></VoiceButton>
      )}
      {listening && <VoiceListeningButton onClick={SpeechRecognition.stopListening} />}
    </Box>
  );
});
