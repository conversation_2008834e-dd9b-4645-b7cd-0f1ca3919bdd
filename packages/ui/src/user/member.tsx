import { Icon<PERSON>utton } from '@mui/joy';
import Tooltip from '@mui/joy/Tooltip';
import type React from 'react';
import type { FC, ReactNode } from 'react';
import { useLocale } from '@bika/contents/i18n';
import type { AvatarLogo } from 'basenext/avatar';
import CloseOutlined from '@bika/ui/icons/components/close_outlined';
import UserGroupOutlined from '@bika/ui/icons/components/user_group_outlined';
import UserRoleOutlined from '@bika/ui/icons/doc_hide_components/user_role_outlined';
import type { AvatarShape } from '@bika/ui/user';
import { useCssColor } from '../colors';
import { AvatarSize } from '../components/avatar';
import { AvatarImg, type BikaAvatarProps } from '../components/avatar/avatar-component';
import { Box } from '../layout-components';
import { Typography } from '../text-components';

export type { AvatarShape } from '../components/avatar/types';

const MemberComponentV1: FC<{
  defaultIcon?: BikaAvatarProps['defaultIcon'];
  shape?: BikaAvatarProps['shape'];
  avatar?: AvatarLogo;
  id?: string;
  name: string;
  onDelete?: (id: string) => void;
  className?: string;
  deleted?: boolean;
  disabled?: boolean;
  avatarSize?: AvatarSize;
}> = ({ name, avatar, defaultIcon, deleted, shape, className, avatarSize, disabled, onDelete, id }) => {
  const colors = useCssColor();

  const { t } = useLocale();
  const truncateName = (text: string) => {
    const isChinese = /[\u4e00-\u9fa5]/;
    if (isChinese.test(text)) {
      return text.length > 10 ? `${text.slice(0, 10)}...` : text;
    }
    return text.length > 20 ? `${text.slice(0, 20)}...` : text;
  };

  // const colors = useCssColor();
  const memberName = truncateName(name || t.global.action.un_named);

  const showTooltip = memberName !== name ? name : null;

  return (
    <Box
      className={className}
      sx={{
        paddingLeft: '2px',
        paddingRight: '8px',
        display: 'flex',
        alignItems: 'center',
        width: 'fit-content',
        height: '24px',
        borderRadius: shape === 'CIRCLE' ? '13px' : '2px',
        backgroundColor: 'var(--bg-controls)',
        opacity: deleted === true ? 0.5 : 1,
      }}
    >
      <AvatarImg
        variant="solid"
        defaultIcon={defaultIcon}
        shape={shape}
        customSize={avatarSize || AvatarSize.Size20}
        avatar={avatar}
        name={name}
      />

      <Tooltip title={showTooltip}>
        <Typography
          level={'b4'}
          textColor={'var(--text-primary)'}
          textOverflow="ellipsis"
          whiteSpace="nowrap"
          overflow="hidden"
          maxWidth="150px"
          sx={{
            marginLeft: '4px',
          }}
        >
          {memberName}
        </Typography>
      </Tooltip>

      {disabled !== true && onDelete && (
        <IconButton
          sx={{
            height: '16px',
            width: '16px',
            marginLeft: '4px',
            maxHeight: '16px',
            minHeight: '16px',
            minWidth: '16px',
            '&:hover': {
              backgroundColor: 'rgba(0,0,0,0.12)',
            },
          }}
          onClick={(e) => {
            if (!id) {
              return;
            }
            onDelete?.(id);
            e.stopPropagation();
          }}
        >
          <CloseOutlined />
        </IconButton>
      )}
    </Box>
  );
};

type PropsWithId = Omit<Parameters<typeof MemberComponentV1>[0], 'defaultIcon' | 'shape'> & {
  id?: string;
};

const withMemberEnhancement =
  <
    P extends PropsWithId & {
      id?: string;
    },
  >(
    WrappedComponent: React.ComponentType<
      P & { shape?: BikaAvatarProps['shape']; defaultIcon?: BikaAvatarProps['defaultIcon'] }
    >,
  ) =>
  (props: P) => {
    const enhancedProps = { ...props };
    if (props.id) {
      let shape: AvatarShape = 'CIRCLE' as const;
      let defaultIcon: ReactNode;
      if (props.id.startsWith('root')) {
        defaultIcon = <UserGroupOutlined />;
        shape = 'SQUARE';
      } else if (props.id.startsWith('tem')) {
        shape = 'CIRCLE';
        defaultIcon = <UserGroupOutlined />;
      } else if (props.id.startsWith('rol')) {
        shape = 'SQUARE';
        defaultIcon = <UserRoleOutlined />;
      } else if (props.id.startsWith('usr')) {
        shape = 'CIRCLE';
        defaultIcon = <UserRoleOutlined />;
      }
      return <WrappedComponent {...enhancedProps} defaultIcon={defaultIcon} shape={shape} />;
    }

    return <WrappedComponent {...enhancedProps} shape="CIRCLE" />;
  };

export const MemberComponent: FC<PropsWithId> = withMemberEnhancement(MemberComponentV1);
