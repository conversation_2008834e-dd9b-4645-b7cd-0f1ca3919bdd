import React, { useMemo, useEffect, useRef } from 'react';
import { MentionsInput, Mention as RCMention } from 'react-mentions';
import { useApiCaller } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import type { AvatarLogo } from 'basenext/avatar';
import type { UnitSearch } from '@bika/types/unit/dto';
import type { MemberVO, UnitVO } from '@bika/types/unit/vo';
import { AvatarImg } from './components/avatar/avatar-component';
import { Box, Stack } from './layout-components';
import { EllipsisText } from './text/ellipsis';
import { Typography } from './text-components';
import { MemberSelectModal } from './unit/types-form/member-select-modal';
import './mention.css';
import type { IUserInfo } from './user';

interface SuggestionProps {
  id: string;
  display: string;
  description?: string;
  avatar?: AvatarLogo;
}

interface MentionProps {
  data: SuggestionProps[];
  value: string;
  trigger?: string;
  transform?: (_id: string, display: string) => string;
  placeholder?: string;
  onInput: () => void;
  onChange: (_value: string) => void;
  onCompositionStart?: (_e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onCompositionUpdate?: (_e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onCompositionEnd?: (_e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onKeyDown?: (_e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onFocus?: (_e: React.FocusEvent<HTMLTextAreaElement>) => void;
  onBlur?: (_e: React.FocusEvent<HTMLTextAreaElement | HTMLInputElement>) => void;
  onAdd?: (id: string | number, display: string) => void;
  spaceId: string;
}

export const Mention = (props: MentionProps) => {
  const locale = useLocale();
  const { t } = locale;
  const {
    data,
    value,
    trigger = '@',
    placeholder = t.record.input_comment_placeholder,
    transform,
    onChange,
    onInput,
    onCompositionStart,
    onCompositionUpdate,
    onCompositionEnd,
    onKeyDown,
    onBlur,
    onAdd,
    onFocus = () => {},
    spaceId,
  } = props;

  const inputRef = useRef<HTMLTextAreaElement>(null);
  const { trpcQuery, trpc } = useApiCaller();

  useEffect(() => {
    if (!inputRef.current) {
      return;
    }
    if (onCompositionStart) {
      // @ts-ignore
      inputRef.current.addEventListener('compositionstart', onCompositionStart);
    }
    if (onCompositionUpdate) {
      // @ts-ignore
      inputRef.current.addEventListener('compositionupdate', onCompositionUpdate);
    }
    if (onCompositionEnd) {
      // @ts-ignore
      inputRef.current.addEventListener('compositionend', onCompositionEnd);
    }

    return () => {
      if (!inputRef.current) {
        return;
      }
      if (onCompositionStart) {
        // @ts-ignore
        inputRef.current.removeEventListener('compositionstart', onCompositionStart);
      }
      if (onCompositionUpdate) {
        // @ts-ignore
        inputRef.current.removeEventListener('compositionupdate', onCompositionUpdate);
      }
      if (onCompositionEnd) {
        // @ts-ignore
        inputRef.current.removeEventListener('compositionend', onCompositionEnd);
      }
    };
  }, [inputRef]);

  const { data: unitPagination } = trpcQuery.unit.list.useQuery(
    {
      spaceId,
      ids: value ? [value] : [],
      pageNo: 1,
      pageSize: 1,
    },
    { enabled: !value },
  );
  const { data: memberPagination } = trpcQuery.member.list.useQuery({
    spaceId,
    detail: true,
    pageSize: 5,
  });

  const options = useMemo(() => {
    const members = memberPagination?.data ?? [];
    const memberArray = members.map((member) => ({
      ...member,
      label: member.name,
      description: member?.teams?.map((team) => team.path).join(', '),
    }));

    const newOptions: UnitVO[] = [...memberArray];
    const selectedItems = unitPagination?.data ?? [];
    for (const selectedItem of selectedItems) {
      if (memberArray.every((item) => item.id !== selectedItem.id)) {
        newOptions.push(selectedItem);
      }
    }
    return newOptions.map((unit) => ({
      ...unit,
      label: unit.name,
      description: (unit as MemberVO)?.teams?.map((team) => team.path).join(', ') ?? '',
    })) as IUserInfo[];
  }, [memberPagination, unitPagination]);

  const [showModal, setShowModal] = React.useState(false);
  const [isDisabled, setIsDisabled] = React.useState(false);
  const handleSuggestionClick = () => {
    setIsDisabled(true);
    setTimeout(() => setIsDisabled(false), 100); // 小延迟以重新启用 onChange
  };

  return (
    <Box>
      <MentionsInput
        value={value}
        onInput={onInput}
        inputRef={inputRef}
        onChange={(_e, newValue) => {
          if (isDisabled) return;
          // Prevent the "@[see_more](more)" text from being added to the input
          const seeMorePattern = new RegExp(`@\\[${t.buttons.see_more}\\]\\(more\\)`);
          if (seeMorePattern.test(newValue)) {
            return;
          }
          onChange(newValue);
        }}
        onKeyDown={onKeyDown}
        onFocus={onFocus}
        forceSuggestionsAboveCursor
        onBlur={onBlur}
        className="mentions"
        style={{
          control: {
            backgroundColor: 'var(--bg-surface)',
            color: 'var(--text-primary)',
            padding: '6px 12px',
            borderRadius: '4px',
          },
          highlighter: {
            overflow: 'hidden',
            minHeight: 70,
            maxHeight: 120,
          },
          input: {
            margin: 0,
            padding: '6px 12px',
            border: 0,
            borderRadius: '4px',
            outline: 0,
            width: '100%',
            color: 'inherit',
            fontSize: 'inherit',
            backgroundColor: 'transparent',
            minHeight: 70,
            maxHeight: 120,
            overflow: 'auto',
          },
        }}
        placeholder={placeholder}
      >
        <RCMention
          displayTransform={transform || ((_id, display) => `@${display} `)}
          markup="@[__display__](__id__)"
          trigger={trigger}
          data={[
            ...data,
            // more
            {
              id: 'more',
              display: t.buttons.see_more,
            },
          ]}
          style={{
            backgroundColor: 'var(--selected)',
            borderRadius: '8px',
          }}
          onAdd={(id, display) => {
            if (id === 'more') {
              setShowModal(true);
            } else {
              onAdd?.(id, display);
            }
          }}
          renderSuggestion={(suggestion, _search, highlightedDisplay) => {
            if (suggestion.id === 'more') {
              return (
                <Stack
                  direction={'row'}
                  alignItems={'center'}
                  justifyContent={'center'}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleSuggestionClick();
                    setShowModal(true);
                  }}
                >
                  <Typography
                    textColor="var(--text-secondary)"
                    level="b4"
                    lineHeight="18px"
                    width="100%"
                    textAlign="center"
                  >
                    {t.buttons.see_more}
                  </Typography>
                </Stack>
              );
            }
            return (
              <Stack direction={'row'} alignItems={'center'}>
                {(suggestion as SuggestionProps).avatar && (
                  <AvatarImg
                    variant="solid"
                    size={'sm'}
                    name={(suggestion as SuggestionProps).display!}
                    avatar={(suggestion as SuggestionProps).avatar!}
                    sx={{
                      height: '20px',
                      width: '20px',
                    }}
                  />
                )}
                <Box sx={{ ml: 1, maxWidth: '180px' }}>
                  <EllipsisText>
                    <Typography textColor="var(--text-primary)" level="b4" lineHeight="18px">
                      {highlightedDisplay}
                    </Typography>
                  </EllipsisText>
                  {(suggestion as SuggestionProps).description && (
                    <EllipsisText>
                      <Typography textColor="var(--text-secondary)" level="body-xs" lineHeight="18px">
                        {(suggestion as SuggestionProps).description}
                      </Typography>
                    </EllipsisText>
                  )}
                </Box>
              </Stack>
            );
          }}
        />
      </MentionsInput>
      {showModal && (
        <MemberSelectModal
          multiple
          selectedUnits={options.filter((item) => value === item.id)}
          onChange={(items) => {
            if (!items || items.length === 0) return;
            const addMembers = items.map((item) => `[${item.name}](${item.id})`).join('@');
            const isEndWithAt = value.endsWith('@');
            const newValue = `${value}${isEndWithAt ? '' : '@'}${addMembers}`;
            onChange(newValue);
            // 将光标移到输入框的最后
            setTimeout(() => {
              if (inputRef.current) {
                inputRef.current.focus(); // 然后重新聚焦到输入框并将光标移到最后
                const length = inputRef.current.value.length;
                inputRef.current?.setSelectionRange(length, length);
              }
            }, 0);
          }}
          setIsEdit={(isOpen) => {
            setShowModal(isOpen);
          }}
          onCancel={() => {
            setShowModal(false);
          }}
          api={{
            useRoles: () =>
              trpcQuery.role.list.useQuery({
                spaceId,
              }),
            getSearchUnits: (search: UnitSearch) =>
              trpc.unit.list.query({
                spaceId,
                ...search,
              }),
            getTeamSubList: (teamId: string) =>
              trpc.team.subList.query({
                spaceId,
                teamId,
                pageSize: 50,
              }),
          }}
          locale={locale}
        />
      )}
    </Box>
  );
};
