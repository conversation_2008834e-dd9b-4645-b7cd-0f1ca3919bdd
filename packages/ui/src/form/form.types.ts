import { SxProps } from '@mui/joy/styles/types';
import { AvatarLogo } from 'basenext/avatar';
import React, { InputHTMLAttributes, ReactElement } from 'react';
import { z } from 'zod';

export type ZodObjectOrWrapped = z.ZodObject<any, any>;
export type ZodArrayOrWrapped = z.Zod<PERSON>rray<any, any>;
export type ZodAnyOrWrapped = z.ZodAny;

export interface RecordFormObject {
  [fieldName: string]: any;
}

export type IFormCustomOptions = IFormOptionProps[] | Record<string, unknown>;

export enum DependencyType {
  DISABLES,
  REQUIRES,
  HIDES,
  SETS_OPTIONS,
}

type BaseDependency<SchemaType extends z.infer<z.ZodObject<any, any>>> = {
  sourceField: keyof SchemaType;
  type: DependencyType;
  targetField: keyof SchemaType;
  when: (sourceFieldValue: any, targetFieldValue: any) => boolean;
};

export type ValueDependency<SchemaType extends z.infer<z.ZodObject<any, any>>> = BaseDependency<SchemaType> & {
  type: DependencyType.DISABLES | DependencyType.REQUIRES | DependencyType.HIDES;
};

export type EnumValues = readonly [string, ...string[]];

export type OptionsDependency<SchemaType extends z.infer<z.ZodObject<any, any>>> = BaseDependency<SchemaType> & {
  type: DependencyType.SETS_OPTIONS;

  // Partial array of values from sourceField that will trigger the dependency
  options: EnumValues;
};

export type Dependency<SchemaType extends z.infer<z.ZodObject<any, any>>> =
  | ValueDependency<SchemaType>
  | OptionsDependency<SchemaType>;

export type DynamicLabel = (value: string) => ReactElement;
export type FieldConfigItem = {
  description?: string | DynamicLabel;
  label?: ReactElement | string;
  fieldType?:
    | 'checkbox'
    | 'textarea'
    | 'select'
    | 'number'
    | 'switch'
    | 'date'
    | 'datetime'
    | 'fallback'
    | 'array'
    | 'images'
    | React.FunctionComponent<AutoFormInputComponentProps>;
  options?: IFormCustomOptions;
  inputProps?: Omit<InputHTMLAttributes<HTMLInputElement>, 'defaultValue'> & {
    defaultValue?: boolean | AvatarLogo | InputHTMLAttributes<HTMLInputElement>['defaultValue'];
    showLabel?: boolean;
    hidden?: boolean;
    isHidden?: (values: RecordFormObject) => boolean;
    createEnabled?: boolean;
    inline?: boolean;
  };
};

export interface IFormOptionProps {
  value: string;
  label: string;
  color?: string;
  disabled?: boolean;
  prefixIcon?: React.ReactNode;
}

/**
 * A FormInput component can handle a specific Zod type (e.g. "ZodBoolean")
 */
export type AutoFormInputComponentProps = {
  zodInputProps: InputHTMLAttributes<HTMLInputElement>;
  // field: ControllerRenderProps<FieldValues, any>;
  fieldConfigItem: FieldConfigItem;
  error?: string;
  label?: ReactElement | string;
  isRequired: boolean;
  inline?: boolean;
  fieldProps: any;
  zodItem?: z.ZodAny;
  options?: IFormCustomOptions;
  slotProps?: {
    formControl?: {
      sx?: SxProps;
    };
    button?: {
      sx?: SxProps;
    };
    root?: {
      sx?: SxProps;
    };
  };
};

export type FieldConfig<SchemaType extends z.infer<z.ZodObject<any, any>>> = {
  // If SchemaType.key is an object, create a nested FieldConfig, otherwise FieldConfigItem
  [Key in keyof SchemaType]?: SchemaType[Key] extends object ? FieldConfig<z.infer<SchemaType[Key]>> : FieldConfigItem;
};
