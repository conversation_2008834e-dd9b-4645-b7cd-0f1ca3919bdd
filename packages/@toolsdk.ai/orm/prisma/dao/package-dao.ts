import { iString, iStringConcat } from 'basenext/i18n';
import { generateNanoID } from 'basenext/utils';
import { Package, Prisma } from '@toolsdk.ai/orm';
import { ToolApp } from '@toolsdk.ai/sdk-ts/types/bo';
import { PackageCreateDTO, PackageUpdateDTO } from '@toolsdk.ai/sdk-ts/types/dto';
import { db } from './db';
import { InputJsonObject } from '../prisma-client/runtime/library';
import { PackageSearchIndex } from '../../search';

export type PackageDetailPO = Prisma.PackageGetPayload<{
  include: {
    _count: {
      select: {
        favorites: true;
      };
    };
  };
}>;
export type PackagePO = Package;

interface PackageCacheParam {
  toolapp: ToolApp;
  cacheTime?: Date;
  cacheStar?: number;
}

export class PackageDAO {
  /**
   *
   * @param packageId
   * @param cacheTime if undefined then set as dirty
   * @returns
   */
  static async cache(packageId: string, param: PackageCacheParam) {
    const data = this.buildCacheInput(param);
    return db.prisma.package.update({
      where: {
        id: packageId,
      },
      data,
    });
  }

  private static buildCacheInput(param: Partial<PackageCacheParam>) {
    const { toolapp, cacheTime, cacheStar } = param;
    if (!toolapp) {
      return {};
    }
    const updateCacheTime = cacheTime || new Date();
    const cacheCount = toolapp.tools ? Object.keys(toolapp.tools).length : 0;
    return {
      cacheTime: updateCacheTime,
      // 先 string 再 parse，去掉function 们
      cacheAppData: JSON.parse(JSON.stringify(toolapp)) as object,
      cacheCount,
      cacheStar,
    };
  }

  static async create(developerId: string, dto: PackageCreateDTO) {
    return db.prisma.package.create({
      data: {
        id: generateNanoID('pkg'),
        packageType: dto.bo.type,
        key: dto.bo.key,
        packageData: dto.bo as InputJsonObject,
        name: dto.bo.name,
        description: dto.bo.description,
        visibility: dto.visibility,
        createdBy: developerId,
        updatedBy: developerId,
      },
    });
  }

  /**
   *  统计所有 tools，一共多少个 tools
   */
  static async calcTotalToolsCount() {
    const r = await db.prisma.package.aggregate({
      _sum: {
        cacheCount: true,
      },
    });
    return r._sum.cacheCount || 0;
  }

  static async upsert(
    developerId: string,
    dto: PackageCreateDTO & PackageUpdateDTO & Partial<PackageCacheParam>,
    validated: boolean = true,
  ) {
    const cacheData = this.buildCacheInput(dto);
    const logo = dto.bo.logo || undefined;
    const model: PackagePO = await db.prisma.package.upsert({
      where: {
        key_version: {
          key: dto.bo.key,
          version: dto.bo.version,
        },
      },
      update: {
        packageType: dto.bo.type,
        name: dto.bo.name,
        categoryId: dto.category,
        description: dto.bo.description,
        logo,
        validated,
        packageData: dto.bo as InputJsonObject,
        visibility: dto.visibility,
        updatedBy: developerId,
        ...cacheData,
      },
      create: {
        id: generateNanoID('srv'),
        packageType: dto.bo.type,
        categoryId: dto.category,
        key: dto.bo.key,
        name: dto.bo.name,
        description: dto.bo.description,
        logo,
        validated,
        packageData: dto.bo as InputJsonObject,
        visibility: dto.visibility,
        createdBy: developerId,
        updatedBy: developerId,
        ...cacheData,
      },
    });

    // store to elasticsearch
    await db.search.write({
      id: model.id,
      indexData: this.buildIndexData(model),
      createdAt: model.createdAt.toISOString(),
    });

    return model;
  }

  private static buildIndexData(model: PackagePO): PackageSearchIndex {
    const toolApp: ToolApp | undefined = model.cacheAppData ? (model.cacheAppData as ToolApp) : undefined;
    return {
      type: 'PACKAGE',
      id: model.id,
      packageKey: model.key,
      packageVersion: model.version,
      name: iStringConcat(model.name as iString),
      description: model.description ? iStringConcat(model.description as iString) : undefined,
      categoryId: model.categoryId ?? undefined,
      visibility: model.visibility,
      validated: model.validated ?? false,
      stars: model.cacheStar ?? 0,
      toolSummary: toolApp?.tools
        ? Object.entries(toolApp.tools).reduce((acc, [key, value]) => {
            const label = iStringConcat(value.display.label);
            const desc = value.display.description ? iStringConcat(value.display.description) : '';
            return desc ? `${acc}  ${key} ${label} ${desc}` : `${acc}  ${key} ${label}`;
          }, '')
        : undefined,
      createdBy: model.createdBy,
    };
  }
}
