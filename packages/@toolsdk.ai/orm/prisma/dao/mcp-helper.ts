import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import type { Tool } from '@modelcontextprotocol/sdk/types.js';
import { StdioClientTransport, StdioServerParameters } from '@modelcontextprotocol/sdk/client/stdio.js';
import { PluginManager } from 'live-plugin-manager';
import fs from 'fs';
import path from 'path';
import assert from 'assert';
import { z } from 'zod';
import { CategoryConfigSchema } from '@toolsdk.ai/orm/types';
import { experimental_createMCPClient } from 'ai';
import { Experimental_StdioMCPTransport } from 'ai/mcp-stdio';
import type {
  ActionTemplate,
  InputFieldBO,
  IntegrationTemplate,
  McpServerPackageDataBO,
  ToolApp,
} from '@toolsdk.ai/sdk-ts/types/bo';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import { type LocalMcpServerConfig, localMcpServerPackageNames } from '../../local-mcp-servers-config';
import McpServerApiClient from '../../api/mcp-server-api-client';

async function fetchCategories() {
  const mcpClient = new McpServerApiClient();
  const categories = await mcpClient.fetchCategoriesConfig();
  return categories;
}

export const packageCategories = (async () => {
  try {
    const categories = await fetchCategories();
    return z.array(CategoryConfigSchema).parse(categories);
  } catch (error) {
    console.warn('Failed to fetch package categories, using empty array:', error);
    return [];
  }
})();

const manager = new PluginManager();
type FindPackageMode = 'node_modules' | 'plugin';

function findPackageFilePath(mode: FindPackageMode, packageName: string, file: string = 'pacakge.json'): string {
  // 外5层，找到 node_modules 目录
  let resolvePath;
  outerLoop: for (let i = 5; i >= 0; i--) {
    for (const ext of ['', '.pnpm/node_modules/']) {
      resolvePath = __dirname + '/' + '../'.repeat(i) + 'node_modules/' + ext + packageName; // fallback to relative path
      if (fs.existsSync(resolvePath)) {
        console.log('Resolved package path:', resolvePath);
        break outerLoop;
      }
    }
  }
  assert(resolvePath);

  // try {
  //   resolvePath = require.resolve(packageName);
  // } catch (_e) {
  //   console.warn('Could not resolve package:', packageName, 'using fallback path');
  //   resolvePath = __dirname + '/../../../../../node_modules/' + packageName; // fallback to relative path
  // }

  // let modulePath ;
  // split 这个路径，找到 node_modules，
  // 如果 node_modules 的下一位，是@开头的，截取下两位
  // 如果 node_modules 的下一位不是@开头的，截取 到下一位
  // const parts = resolvePath.split('/');
  // const nodeModulesIndex = parts.indexOf('node_modules');
  // if (nodeModulesIndex === -1) {
  //   throw new Error('Could not find node_modules in path');
  // }

  // const nextPart = parts[nodeModulesIndex + 1];
  // if (nextPart.startsWith('@')) {
  //   // For scoped packages, take the next two parts
  //   modulePath = parts.slice(0, nodeModulesIndex + 3).join('/');
  // } else {
  //   // For unscoped packages, just take the next part
  //   modulePath = parts.slice(0, nodeModulesIndex + 2).join('/');
  // }

  return path.join(resolvePath, file);
}

interface PackageJson {
  name: string;
  version: string;
  main?: string;
  bin?: string | Record<string, string>;
  [key: string]: unknown;
}

function parseBinPath(packageJSON: PackageJson): string | undefined {
  let binPath: string | undefined;
  if (typeof packageJSON.bin === 'string') {
    binPath = packageJSON.bin;
  } else if (typeof packageJSON.bin === 'object') {
    binPath = Object.values(packageJSON.bin)[0];
  } else {
    binPath = packageJSON.main;
  }
  return binPath;
}

async function parseStdioParams(
  mode: FindPackageMode,
  mcpServerConfig: LocalMcpServerConfig,
  env?: Record<string, string>,
) {
  const { packageName, version, serverConfig } = mcpServerConfig;
  const { args } = serverConfig || {};

  // ignore 的话，用 npm，适合本地 workspace packages
  // 否则用 live-plugin-manager，网上下载
  let binFilePath;
  if (localMcpServerPackageNames.includes(packageName)) {
    const pkgName = '@toolsdk.ai/mcp-server'; // for const require (non-dynamic)

    const packageJSONFilePath = process.cwd() + '/node_modules/' + pkgName + '/package.json'; //  + require.resolve(pkgName + '/package.json');
    const packageJSONStr = fs.readFileSync(packageJSONFilePath, 'utf8');
    const packageJSON = JSON.parse(packageJSONStr);
    // const bin = packageJSON.bin || packageJSON.main;
    const bin = parseBinPath(packageJSON);
    binFilePath = process.cwd() + '/node_modules/' + pkgName + '/' + bin; // require.resolve(pkgName + '/' + bin);
  } else {
    if (mode === 'plugin') {
      await manager.install(packageName, version || 'latest');
      const packageJSONStr = fs.readFileSync(
        process.cwd() + '/plugin_packages/' + packageName + '/package.json',
        'utf8',
      );
      const packageJSON = JSON.parse(packageJSONStr);
      console.log('Using plugin manager to resolve package:', packageName);
      const binPath = parseBinPath(packageJSON);
      binFilePath = process.cwd() + '/plugin_packages/' + packageName + '/' + binPath;
    } else if (mode === 'node_modules') {
      const packageJSONStr = fs.readFileSync(findPackageFilePath(mode, packageName, 'package.json'), 'utf8');
      const packageJSON = JSON.parse(packageJSONStr);
      const binPath = parseBinPath(packageJSON);

      binFilePath = findPackageFilePath(mode, packageName, binPath);
    } else {
      throw new Error(`Unsupported mode: ${mode}. Use 'node_modules' or 'plugin'.`);
    }
  }

  console.log('MCP file path', binFilePath);

  const stdioParams: StdioServerParameters = {
    command: process.execPath,
    args: args ? [binFilePath, ...args] : [binFilePath],
    env: env,
  };
  return stdioParams;
}

export function buildToolApp({
  packageKey,
  displayName,
  tools,
  configuration,
}: {
  packageKey: string;
  displayName: string | Partial<Record<'en' | 'zh-CN' | 'zh-TW' | 'ja', string>>;
  tools: Record<string, ActionTemplate>;
  configuration: IntegrationTemplate;
}): ToolApp {
  return {
    key: packageKey,
    display: {
      label: displayName,
    },
    authentication: configuration,
    tools: tools,
  };
}

function convertJsonSchemaToInputFields(inputSchema: Tool['inputSchema']): InputFieldBO[] {
  const inputFields: InputFieldBO[] = [];

  if (inputSchema && inputSchema.properties) {
    Object.entries(inputSchema.properties).forEach(([key, itemData]) => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const item = itemData as any;

      const field: InputFieldBO = {
        key: key,
        label: item.title || key,
        type: item.type,
        required: Array.isArray(inputSchema.required) ? inputSchema.required.includes(key) : false,
        helpText: item.description,
        default: item.default,
        list: item.list,
        choices: item.choices,
        dynamic: item.dynamic,
        placeholder: item.placeholder,
      };
      inputFields.push(field);
    });
  }

  return inputFields;
}

export function convertMcpToolToActionTemplate(mcpTool: Tool): ActionTemplate {
  return {
    type: 'create',
    key: mcpTool.name,
    display: {
      label: mcpTool.name,
      description: mcpTool.description,
    },
    operation: {
      inputFields: convertJsonSchemaToInputFields(mcpTool.inputSchema),
      perform: {},
    },
  };
}

export async function getToolApp(packageDataBO: McpServerPackageDataBO, tools: Tool[]): Promise<ToolApp> {
  const mcpTools: Record<string, ActionTemplate> = {};
  for (const mcpTool of tools) {
    const cTool = convertMcpToolToActionTemplate(mcpTool);
    mcpTools[mcpTool.name] = cTool;
  }

  const configuration = convertMCPServerConfiguration(packageDataBO.env);
  const toolApp: ToolApp = buildToolApp({
    packageKey: packageDataBO.key,
    displayName: packageDataBO.name,
    tools: mcpTools,
    configuration,
  });
  return toolApp;
}

export function convertMCPServerConfiguration(env: McpServerPackageDataBO['env']): IntegrationTemplate {
  const fields: IntegrationTemplate['fields'] = env
    ? Object.entries(env).map(([key, value]) => ({
        key,
        type: 'string',
        label: key,
        required: value.required,
        description: value.description,
      }))
    : undefined;
  return {
    type: 'custom',
    fields,
    test: () => {},
  };
}

export function mockMCPServerEnv(config: LocalMcpServerConfig) {
  const mockEnv: Record<string, string> = {};
  if (config.env) {
    for (const [key, _value] of Object.entries(config.env)) {
      mockEnv[key] = 'XXX';
    }
  }
  return mockEnv;
}

export async function getAiSDKMcpClient(
  mode: FindPackageMode,
  mcpServerConfig: LocalMcpServerConfig,
  env?: Record<string, string>,
) {
  const stdioParams = await parseStdioParams(mode, mcpServerConfig, env);
  const transport = new Experimental_StdioMCPTransport(stdioParams);
  const stdioClient = await experimental_createMCPClient({
    transport,
  });
  return stdioClient;
}

export async function getAiSDKSseMcpClient(url: string) {
  return experimental_createMCPClient({
    transport: { type: 'sse', url },
  });
}

export async function getAiSDKHttpMcpClient(url: string) {
  const urlObj = new URL(url);
  const mcpClient = await experimental_createMCPClient({
    transport: new StreamableHTTPClientTransport(urlObj),
  });
  return mcpClient;
}

export async function getOfficialMcpClient(
  mode: FindPackageMode,
  mcpServerConfig: LocalMcpServerConfig,
  env?: Record<string, string>,
) {
  const { packageName } = mcpServerConfig;
  const stdioParams = await parseStdioParams(mode, mcpServerConfig, env);

  const transport = new StdioClientTransport(stdioParams);

  const client = new Client(
    {
      name: `mcp-server-${mcpServerConfig.name}-client`,
      version: '1.0.0',
    },
    {
      capabilities: {
        tools: {},
      },
    },
  );
  await client.connect(transport);

  const closeConnection = async () => {
    try {
      await client.close();
    } catch (e) {
      console.warn(`${packageName} mcp client close failure.`, e);
    }
  };

  return { client, transport, closeConnection };
}

export async function getToolAppFromMCPServer(
  mode: FindPackageMode,
  packageData: McpServerPackageDataBO,
  env?: Record<string, string>,
) {
  // 将 MCP tool 转  ToolApp Tool BO，再转 ToolApp ToolVO
  const retTools: Record<string, ActionTemplate> = {};

  const { client, closeConnection } = await getOfficialMcpClient(mode, packageData, env);
  const { tools: mcpTools } = await client.listTools();

  for (const mcpTool of mcpTools) {
    const cTool = convertMcpToolToActionTemplate(mcpTool);
    retTools[mcpTool.name] = cTool;
  }

  const pkgEnv = packageData.env;
  const configuration: IntegrationTemplate = convertMCPServerConfiguration(pkgEnv);
  const toolApp: ToolApp = buildToolApp({
    packageKey: packageData.key,
    displayName: packageData.name,
    tools: retTools,
    configuration,
  });
  return { client, closeConnection, tools: retTools, configuration, toolApp: toolApp };
}
