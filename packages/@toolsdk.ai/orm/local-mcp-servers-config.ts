import { McpServerPackageDataBO } from '@toolsdk.ai/sdk-ts/types/bo';

export type LocalMcpServerConfig = Omit<McpServerPackageDataBO, 'type' | 'key'> & { category?: string };
export const localMcpServerConfig: LocalMcpServerConfig[] = [
  {
    runtime: 'node',
    name: 'ToolSDK.ai',
    description: 'MCP Server for calling ToolSDK.ai',
    version: '',
    serverConfig: {},
    env: {
      TEST_ENV: {
        description: 'Test environment variable for MCP Server',
        required: true,
      },
    },
    packageName: '@toolsdk.ai/mcp-server',
    category: 'developer-tools',
    validated: true,
  },
];

export const localMcpServerPackageNames = localMcpServerConfig.map((config) => config.packageName);

export default localMcpServerConfig;
