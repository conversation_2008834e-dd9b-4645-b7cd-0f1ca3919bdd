import { z } from 'zod';
import { PaginationSchema } from 'basenext/pagination';
import {
  PackageInstanceRunBody,
  PackageToolRunBodyDTO,
  PackageListRequestScopeSchema,
} from '@toolsdk.ai/sdk-ts/types/dto';

export interface IAccountTokenMetadata {
  externalId?: string;
}

export interface IInstanceMetadata {
  // metadata 里面的 createdBy 和 updatedBy，对应的值是来自于 token 的 externalId
  createdBy?: string;
  updatedBy?: string;
}

export type IPackageToolRunFullBody = PackageToolRunBodyDTO & PackageInstanceRunBody;

const ICategorySchema = z.union([z.undefined(), z.literal('featured'), z.string()]);
export type ICategory = z.infer<typeof ICategorySchema>;

export const IPackageSearchDTOSchema = z.object({
  query: z.string().optional(),
  pagination: PaginationSchema.partial().optional(), // You may want to define a proper Pagination schema
  developerId: z.string().optional(),
  scope: PackageListRequestScopeSchema.optional(), // Assuming PackageListRequestScope is an enum
  category: ICategorySchema.optional(),
});

export type IPackageSearchDTO = z.infer<typeof IPackageSearchDTOSchema>;
