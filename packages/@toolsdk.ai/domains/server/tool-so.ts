import { iStringParse } from 'basenext/i18n';
import { ActionTemplate, InputFieldBO } from '@toolsdk.ai/sdk-ts/types/bo';
import { ActionTemplateVO } from '@toolsdk.ai/sdk-ts/types/vo';
import { FunctionsHelper } from './adapter/functions-helper';

export class ToolSO {
  private _bo: ActionTemplate;

  constructor(bo: ActionTemplate) {
    this._bo = bo;
  }

  get bo() {
    return this._bo;
  }

  get key() {
    return this._bo.key;
  }

  async runDynamicFields(inputData?: Record<string, unknown>) {
    const tool = this._bo;
    const fields: InputFieldBO[] = [];
    for (const dField of tool.operation.inputFields) {
      const field = await FunctionsHelper.runDynamicField(dField, inputData);
      fields.push(field);
    }
    return fields;
  }

  async toVO(): Promise<ActionTemplateVO> {
    const tool = this._bo;
    const inputFields = await this.runDynamicFields();

    return {
      key: tool.key,
      kind: 'TOOL',
      name: iStringParse(tool.display.label),
      description: iStringParse(tool.display.description),
      inputFields,
      logo: tool.logo,
    };
  }
}
