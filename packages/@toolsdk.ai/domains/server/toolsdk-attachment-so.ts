import { AvatarLogo, AvatarLogoSchema } from 'basenext/avatar';
import type { AttachmentVO, PresignedPutVO } from '@bika/types/attachment/vo';
import { isValidHttpUrl } from '@bika/types/utils';
import { ToolSDKAttachmentDAO } from './toolsdk-attachment-dao';

const dao = new ToolSDKAttachmentDAO();

export class ToolSDKAttachmentSO {
  static buildAvatarLogo(logo: unknown): AvatarLogo | undefined {
    if (!logo) {
      return undefined;
    }
    const { success, data } = AvatarLogoSchema.safeParse(logo);
    if (!success) {
      return undefined;
    }
    if (data.type !== 'ATTACHMENT') {
      return data;
    }
    // 其他平台展示 toolsdk.ai 的附件，需要完整路径
    const relativePath = ToolSDKAttachmentSO.getPublicUrlForKey(data.relativePath);
    return { ...data, relativePath };
  }

  static getPublicUrlForKey(relativePath: string): string {
    if (isValidHttpUrl(relativePath)) {
      return relativePath;
    }
    const endpoint = dao.getMinioPublicEndpoint();
    return `${endpoint}/${relativePath}`;
  }

  /**
   * 获取临时上传附件的签名地址,交由前端自己上传
   * Generate a pre-signed url for uploading attachment
   * The key prefix is `/tmp/${randomId}`
   * 通过此链接上传key为/tmp/${randomId}.{ext}
   */
  static async getPresignedPut(userId: string | undefined, ext?: string): Promise<PresignedPutVO> {
    const { presignedPutUrl, tmpStorePath } = await dao.getPresignedPut(ext);
    const tmpAttachmentModel = await dao.createTmpAttachmentModel(presignedPutUrl, tmpStorePath, userId);

    return dao.tmpAttachmentModelToVO(tmpAttachmentModel);
  }

  /**
   * Create an attachment via PreSignedURL (/tmp/XXX)
   *
   * 这个动作只能由用户完成, 系统不能自己完成这个流程
   *
   * 注意，默认都是/tmp，
   * 在创建附件的时候，进行“重命名操作”，命名规则：
   * /{首次引用的模块名}/{附件标识}.{扩展名}
   *
   * 会自动把/tmp/xxx临时文件删除，如果/tmp/XXX 有残留，就是异常、或被用户滥用了，因为上传完就肯定被移走，定期清理删除
   * 会自动生成缩略图
   *
   * @param user 创建者
   * @param tmpUploadPath 上传的临时文件路径
   * @param prefix 附件路径前缀，保存的bucket地方，默认是default/
   */
  static async createByPresignedPut(
    userId: string | undefined,
    tmpUploadPath: string,
    prefix: string = 'default/',
  ): Promise<AttachmentVO> {
    const tmpAttachmentModel = await dao.getTmpAttachmentModelByPath(tmpUploadPath);
    // 验证数据库是否有这个临时文件URL记录？
    // const tmpAttachment = await TmpAttachmentSO.init(tmpUploadPath);
    if (!tmpAttachmentModel) {
      throw new Error(`TmpAttachment not found: ${tmpUploadPath}`);
    }
    // const tmpAttachmentModel: ITmpAttachmentModel = tmpAttachment.model;
    const attachmentModel = await dao.getOrCreateAttachmentModel(tmpAttachmentModel, prefix, userId);

    // if (ImageSO.isImageExt(attachmentModel.ext)) {
    //   await attachment2.generateThumbnailAndPreview();
    // }
    return dao.attachmentModelToVO(attachmentModel);
  }
}
