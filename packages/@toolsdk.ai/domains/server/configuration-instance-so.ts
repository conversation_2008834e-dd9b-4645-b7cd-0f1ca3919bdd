import { Pagination, PaginationSchema } from 'basenext/pagination';
import { generateNanoID } from 'basenext/utils';
import { db, type Prisma } from '@toolsdk.ai/orm';
import { ConfigurationInstancePageVO, ConfigurationInstanceVO } from '@toolsdk.ai/sdk-ts/types/vo';
import { AccountSO } from './account-so';
import { PackageSO } from './package-so';
import { IInstanceMetadata } from './types';

type ConfigurationInstancePO = Prisma.ConfigurationInstanceGetPayload<{
  include: {
    package: false;
    account: false;
  };
}>;

export class ConfigurationInstanceSO {
  private _model: ConfigurationInstancePO;

  private _account?: AccountSO;

  private _package?: PackageSO;

  constructor(po: ConfigurationInstancePO, options?: { account?: AccountSO; package?: PackageSO }) {
    this._model = po;
    this._account = options?.account;
    this._package = options?.package;
  }

  get model() {
    return this._model;
  }

  get id() {
    return this._model.id;
  }

  get accountId() {
    return this._model.accountId;
  }

  get packageId() {
    return this._model.packageId;
  }

  get inputData() {
    return this._model.inputData as Record<string, unknown>;
  }

  get metadata() {
    return (this._model.metadata || {}) as IInstanceMetadata;
  }

  toVO(): ConfigurationInstanceVO {
    return {
      id: this.id,
      connectionLabel: this._model.connectionLabel || undefined,
    };
  }

  async getAccount() {
    if (this._account) {
      return this._account;
    }
    const account = await AccountSO.findById(this.accountId);
    this._account = account;
    return account;
  }

  async getPackage() {
    if (this._package) {
      return this._package;
    }
    const pkg = await PackageSO.getById(this.packageId);
    this._package = pkg;
    return pkg;
  }

  async update(body: { connectionLabel?: string; inputData?: Record<string, unknown> }, externalId?: string) {
    const { connectionLabel, inputData } = body;

    const data: Prisma.ConfigurationInstanceUpdateInput = {};
    if (connectionLabel && connectionLabel !== this._model.connectionLabel) {
      data.connectionLabel = connectionLabel;
    }
    if (inputData && inputData !== this._model.inputData) {
      data.inputData = inputData as object;
    }
    // 未传入值不更新
    if (Object.keys(data).length === 0) {
      return this;
    }
    if (externalId) {
      data.metadata = {
        ...this.metadata,
        updatedBy: externalId,
      };
    }
    const po = await db.prisma.configurationInstance.update({
      where: { id: this._model.id },
      data,
    });
    return new ConfigurationInstanceSO(po, {
      account: this._account,
      package: this._package,
    });
  }

  async delete() {
    await db.prisma.configurationInstance.delete({
      where: { id: this.id },
    });
  }

  async testConnection(externalId?: string) {
    // fetch the package
    const pkg = await this.getPackage();
    const configuration = await pkg.getConfiguration();

    if (!configuration) return;

    // confirm authentication
    const testResult = await configuration.confirmAuthentication(this.inputData);

    // update credential data
    const metadata = externalId ? { ...this.metadata, updatedBy: externalId } : undefined;
    try {
      // todo 重新渲染 connectionLabel?
      await db.prisma.configurationInstance.update({
        where: {
          id: this.id,
        },
        data: {
          testValues: { ...(testResult || {}) },
          metadata,
        },
      });
    } catch (_err) {
      // do nothing
    }

    return this._model.connectionLabel || undefined;
  }

  static async findById(credentialId: string, options?: { account?: boolean; package?: boolean }) {
    const po = await db.prisma.configurationInstance.findUnique({
      where: {
        id: credentialId,
      },
      include: {
        account: options?.account,
        package: options?.package,
      },
    });
    if (!po) {
      throw new Error(`Credential ${credentialId} not found`);
    }
    return new ConfigurationInstanceSO(po, {
      account: options?.account ? new AccountSO(po.account) : undefined,
      package: options?.package ? new PackageSO(po.package) : undefined,
    });
  }

  static async page(
    accountId: string,
    packageId: string,
    pagination?: Partial<Pagination>,
  ): Promise<ConfigurationInstancePageVO> {
    const { pageNo, pageSize } = PaginationSchema.parse(pagination ?? {});

    const where: Prisma.ConfigurationInstanceWhereInput = {
      accountId,
      packageId,
    };
    // query db
    const [rows, total] = await Promise.all([
      db.prisma.configurationInstance.findMany({
        where,
        skip: pageSize * (pageNo - 1),
        take: pageSize,
        orderBy: {
          createdAt: 'asc',
        },
      }),
      db.prisma.configurationInstance.count({ where }),
    ]);

    const credentials = rows.map((po) => new ConfigurationInstanceSO(po).toVO());
    return { pagination: { pageNo, pageSize, total }, data: credentials };
  }

  static async create(params: {
    account: AccountSO;
    pkg: PackageSO;
    inputData?: Record<string, unknown>;
    externalId?: string;
  }): Promise<ConfigurationInstanceSO> {
    const { pkg, account, inputData, externalId } = params;
    const credPO = await db.prisma.configurationInstance.create({
      data: {
        id: generateNanoID('crd'),
        accountId: account.id,
        packageId: pkg.id,
        inputData: (inputData || {}) as object,
        metadata: externalId ? { createdBy: externalId } : {},
        createdBy: account.developerId,
      },
    });

    return new ConfigurationInstanceSO(credPO, { account, package: pkg });
  }
}
