import { BaseAttachmentDAO } from '@bika/domains/attachment/server/dao/base-attachment-dao';
import type { IAttachmentModel, ITmpAttachmentModel } from '@bika/domains/attachment/server/dao/types';
import { db } from '@toolsdk.ai/orm/prisma/dao/db';
import { generateNanoID } from 'basenext/utils';
import type { CopyConditions } from 'minio';
import { mongo } from 'mongoose';
import type { Prisma } from '@toolsdk.ai/orm';

export class ToolSDKAttachmentDAO extends BaseAttachmentDAO {
  async statObject(storePath: string): Promise<{ metaData: Record<string, string>; etag: string; size: number }> {
    const attachStat = await db.minioClient.client.statObject(this.minioBucketName, storePath);
    return attachStat;
  }

  async deleteTmpAttachmentModel(id: string, _session: mongo.ClientSession | undefined): Promise<void> {
    await db.prisma.attachmentTmp.delete({ where: { id } });
  }
  get(etag: string): Promise<IAttachmentModel | null> {
    return db.prisma.attachment.findFirst({ where: { etag: etag } });
  }
  removeObject(path: string): Promise<void> {
    return db.minioClient.client.removeObject(this.minioBucketName, path);
  }
  get minioBucketName(): string {
    return db.minioClient.bucketName;
  }
  async delete(id: string): Promise<void> {
    await db.prisma.attachment.delete({ where: { id: id } });
  }
  async copyObject(newPath: string, sourceObject: string, copyCond: CopyConditions): Promise<void> {
    await db.minioClient.client.copyObject(this.minioBucketName, newPath, sourceObject, copyCond);
  }
  async createAttachmentFromTmp(args: {
    tmpAttachment: ITmpAttachmentModel;
    data: Prisma.AttachmentCreateInput;
  }): Promise<IAttachmentModel> {
    // 确保文件操作完成后再落库
    const [model] = await db.prisma.$transaction([
      // 保存附件记录
      db.prisma.attachment.create({ data: args.data }),

      // 删除临时附件记录
      db.prisma.attachmentTmp.delete({ where: { id: args.tmpAttachment.id } }),
    ]);

    return model;
  }

  async getTmpAttachmentModelByPath(path: string): Promise<ITmpAttachmentModel> {
    const model = await db.prisma.attachmentTmp.findFirst({ where: { path } });
    return model!;
  }
  async getTmpAttachmentModelById(id: string): Promise<ITmpAttachmentModel> {
    const model = await db.prisma.attachmentTmp.findFirst({ where: { id } });
    return model!;
  }
  async createTmpAttachmentModel(
    presignedPutUrl: string,
    storePath: string,
    userId: string | undefined,
  ): Promise<ITmpAttachmentModel> {
    return db.prisma.attachmentTmp.create({
      data: {
        id: generateNanoID('tmp'),
        presignedPutUrl: presignedPutUrl,
        // 保存的临时路径
        path: storePath,
        createdBy: userId,
        updatedBy: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });
  }
  getMinioInternalEndpoint(): string {
    return db.minioClient.endpoint;
  }
  minioGetPresignedPut(objectName: string, expiry: number): Promise<string> {
    return db.minioClient.client.presignedPutObject(db.minioClient.bucketName, objectName, expiry);
  }

  getMinioPublicEndpoint(): string {
    return db.minioClient.publicEndpoint;
  }
}
