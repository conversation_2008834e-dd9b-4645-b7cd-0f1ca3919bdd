import {
  convertMCPServerConfiguration,
  convertMcpToolToActionTemplate,
  buildToolApp,
  getToolAppFromMCPServer,
  mockMCPServerEnv,
} from '@toolsdk.ai/orm/prisma/dao/mcp-helper';
import type { ActionTemplate, IntegrationTemplate, McpServerPackageDataBO, ToolApp } from '@toolsdk.ai/sdk-ts/types/bo';
import { localMcpServerPackageNames } from '@toolsdk.ai/orm/local-mcp-servers-config';
import McpServerApiClient from '@toolsdk.ai/orm/api/mcp-server-api-client';
import assert from 'assert';
import { PackageDataAdapter } from './abstract-package-data-adapter';
import { IPackageToolRunFullBody } from '../types';

export class MCPServerAdapter extends PackageDataAdapter<McpServerPackageDataBO> {
  override async getToolApp(): Promise<ToolApp> {
    // 处理本地包
    if (localMcpServerPackageNames.includes(this.packageData.key)) {
      const mockEnv = mockMCPServerEnv(this.packageData);
      const { closeConnection, toolApp } = await getToolAppFromMCPServer('plugin', this.packageData, mockEnv);
      await closeConnection();
      return toolApp;
    }

    // 处理未验证的包
    if (!this.packageData.validated) {
      return buildToolApp({
        packageKey: this.packageData.key,
        displayName: this.packageData.name,
        configuration: convertMCPServerConfiguration(this.packageData.env),
        tools: {},
      });
    }

    // 处理已验证的包，需要更新缓存
    const apiClient = new McpServerApiClient();
    const packageDetail = await apiClient.fetchPackageDetail(this.packageData.key);

    const rTools: Record<string, ActionTemplate> = {};
    if (packageDetail.tools) {
      for (const mcpTool of packageDetail.tools) {
        const cTool = convertMcpToolToActionTemplate(mcpTool);
        rTools[mcpTool.name] = cTool;
      }
    }

    const toolApp: ToolApp = buildToolApp({
      packageKey: this.packageData.key,
      displayName: packageDetail.name || this.packageData.name,
      configuration: convertMCPServerConfiguration(packageDetail.env),
      tools: rTools,
    });

    return toolApp;
  }

  override async getUrl(): Promise<string | undefined> {
    assert(this.packageData.type, 'MCP_SERVER');
    return this.packageData.url;
  }

  override getReadme(): string {
    return this.packageData.readme || '';
  }

  override async findConfiguration(): Promise<IntegrationTemplate | undefined> {
    const packageData = this.packageData;
    const env = packageData.env;
    if (!env || Object.keys(env).length === 0) {
      return;
    }

    return convertMCPServerConfiguration(env);
  }

  /**
   * 整体逻辑与父类一致，区别是统一获取一次 client 连接，避免获取 listTools、callTool 时多次连接
   */
  override async runTool(body: IPackageToolRunFullBody, authData?: Record<string, unknown>): Promise<unknown> {
    const toolKey = body.toolKey;

    if (!this.packageData.validated) {
      throw new Error(`MCP Server package ${this._packageData.key} is not validated.`);
    }

    const configuration = await this.findConfiguration();
    if (configuration && !authData) {
      throw new Error('Configuration is required to run tool.');
    }

    const env = authData ? (authData as Record<string, string>) : undefined;

    try {
      const apiClient = new McpServerApiClient();
      const output = await apiClient.runPackage(this._packageData.packageName, toolKey, body.inputData, env);
      return output;
    } catch (error) {
      const errorMessage = `${this._packageData.packageName}:${toolKey}: ${(error as Error).message}`;
      console.error(`[runTool] Error running tool ${errorMessage}`);
      throw new Error(`Failed to run tool ${errorMessage}`);
    }
  }
}
