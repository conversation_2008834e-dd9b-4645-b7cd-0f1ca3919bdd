import { iString, iStringParse, Locale } from 'basenext/i18n';
import { db, PackageClient as PackageClientPO } from '@toolsdk.ai/orm';
import { PackageClientVO } from '@toolsdk.ai/sdk-ts/types/vo';
import { PackageClient } from '@toolsdk.ai/sdk-ts/types/bo';

export class PackageClientSO {
  private _model: PackageClientPO;
  public constructor(model: PackageClientPO) {
    this._model = model;
  }
  public get id() {
    return this._model.id;
  }

  public get key() {
    return this._model.key;
  }
  public get data() {
    return this._model.data as PackageClient;
  }

  public toVO(locale?: Locale): PackageClientVO {
    return {
      key: this._model.key,
      name: iStringParse(this._model.name as iString, locale),
      content: iStringParse(this._model.content as iString, locale),
      contentMode: this.data.contentMode,
      more: this.data.more,
    };
  }
  public static async list() {
    const packageClients = await db.prisma.packageClient.findMany({
      where: {
        isDeleted: false,
      },
    });
    return packageClients.map((model) => new PackageClientSO(model));
  }
}
