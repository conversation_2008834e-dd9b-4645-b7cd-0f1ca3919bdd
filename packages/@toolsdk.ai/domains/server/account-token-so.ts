import { db, type Prisma, AccountToken } from '@toolsdk.ai/orm';
import { generateNanoID } from 'basenext/utils';
import { AccountSO } from './account-so';
import { IAccountTokenMetadata } from './types';

type AccountTokenModel =
  | AccountToken
  | Prisma.AccountTokenGetPayload<{
      include: { account: true };
    }>;

export class AccountTokenSO {
  private _model: AccountTokenModel;

  private _account?: AccountSO;

  constructor(model: AccountTokenModel, account?: AccountSO) {
    this._model = model;
    this._account = account;
  }

  get token() {
    return this._model.token;
  }

  get metadata() {
    return (this._model.metadata || {}) as IAccountTokenMetadata;
  }

  async getAccount(): Promise<AccountSO> {
    if (this._account) {
      return this._account;
    }
    const account = await AccountSO.findById(this._model.accountId);
    this._account = account;
    return account;
  }

  static async findByToken(token: string): Promise<AccountTokenSO> {
    const po = await db.prisma.accountToken.findUnique({
      where: {
        token,
      },
      include: {
        account: true,
      },
    });
    if (!po) {
      throw new Error('Invalid token');
    }
    return new AccountTokenSO(po, new AccountSO(po.account));
  }

  static async findByAccountId(accountId: string): Promise<AccountTokenSO[]> {
    const pos = await db.prisma.accountToken.findMany({
      where: { accountId },
    });
    return pos.map((po) => new AccountTokenSO(po));
  }

  static async create(accountId: string, externalId?: string): Promise<AccountTokenSO> {
    const po = await db.prisma.accountToken.create({
      data: {
        token: generateNanoID('atk'),
        accountId,
        metadata: externalId ? { externalId } : {},
      },
    });
    return new AccountTokenSO(po);
  }
}
