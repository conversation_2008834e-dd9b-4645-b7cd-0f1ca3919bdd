import { iStringParse } from 'basenext/i18n';
import { ActionInput } from '@toolsdk.ai/sdk-ts/react';
import Editor from '@monaco-editor/react';
import DialogContent from '@mui/joy/DialogContent';
import DialogTitle from '@mui/joy/DialogTitle';
import Modal from '@mui/joy/Modal';
import ModalClose from '@mui/joy/ModalClose';
import ModalDialog from '@mui/joy/ModalDialog';
import React, { useEffect } from 'react';
import toast from 'react-hot-toast';
import { useLocale } from '@bika/contents/i18n/context';
import { AccordionGroup } from '@bika/ui/accordion-group';
import { Affix } from '@bika/ui/affix';
import { Button } from '@bika/ui/button';
import { Stack } from '@bika/ui/layouts';
import { AvatarLogoBOInput } from '@bika/ui/shared/types-form/avatar-logo-bo-input';
import { Typography } from '@bika/ui/texts';
import { ActionTemplateSchema, type ActionTemplate, type InputFieldBO } from '@toolsdk.ai/sdk-ts/types/bo';
import type { ToolAppConfigurationTemplateVO } from '@toolsdk.ai/sdk-ts/types/vo';
import { buildTrpcToolSDKApi } from '../api';
// import { trpcClient } from '../api-client';
import { ActionBOInputAPIConfiguration } from './action-bo-input-api-configuration';
import { ActionBOInputFieldsPanel } from './action-bo-input-fields-panel';
import { useAttachmentUpload } from '../attachment/use-attachment-upload';
import { IntegrationsSelectPanel } from './integrations-select-panel';
import { KeyDisplayInput } from './key-display-input';
import { SourceCodePanel } from './source-code-panel';
import { useUIFrameworkContext } from '@bika/ui/framework/context';

interface Props {
  value: ActionTemplate;
  onChange: (value: ActionTemplate) => void;
  fetchIntegrations?: (keys: Record<string, string>) => Promise<ToolAppConfigurationTemplateVO[]>;
  // 插入额外的底部组件
  customBottomComponent?: React.ReactNode;
}

export function ActionTemplateBOInput(props: Props) {
  const [inputData, setInputData] = React.useState<{ [key: string]: unknown }>({});
  const [integrationsData, setIntegrationsData] = React.useState<Record<string, string>>({});
  const [loading, setLoading] = React.useState(false);
  const [open, setOpen] = React.useState(false);
  const [ret, setRet] = React.useState<string>('');
  const [integrations, setIntegrations] = React.useState<ToolAppConfigurationTemplateVO[]>([]);
  const locale = useLocale();
  const { i } = locale;
  const { upload } = useAttachmentUpload();
  const { isMobile } = useUIFrameworkContext();

  useEffect(() => {
    if (props.fetchIntegrations && props.value.integrations) {
      props
        .fetchIntegrations(props.value.integrations)
        .then((data) => {
          setIntegrations(data);
        })
        .catch(() => {
          setIntegrations([]);
        });
    } else {
      setIntegrations([]);
    }
  }, [props, props.value.integrations]);

  // bundle.authData.integrationKey.access_token
  const authDataVariableList = React.useMemo(() => {
    // return integrations.map((integration) => ({
    //   label: `{{bundle.authData.${integration.key}.access_token}}`,
    //   value: `bundle.authData.${integration.key}.access_token`,
    //   description: iStringParse(integration.key),
    // }));
    return [];
  }, [integrations]);

  return (
    <Stack
      sx={{
        display: 'flex',
        flexDirection: isMobile ? 'column' : 'row',
        width: '100%',
      }}
    >
      <Stack my={5} direction={'column'} display={'flex'} width={'100%'}>
        <AccordionGroup
          defaultActive={0}
          items={[
            {
              title: 'Settings',
              content: (
                <>
                  <AvatarLogoBOInput
                    name={i(props.value.display.label)}
                    value={props.value.logo || { type: 'COLOR', color: '' }}
                    onChange={(newVal) => {
                      props.onChange({ ...props.value, logo: newVal! });
                    }}
                    locale={locale}
                    upload={(file: File) => {
                      return upload({ file: file, filePrefix: 'action' });
                    }}
                  />

                  <KeyDisplayInput
                    value={props.value}
                    onChange={(newVal) => {
                      props.onChange({ ...props.value, ...newVal });
                    }}
                  />
                </>
              ),
            },
            {
              title: 'Integrations',
              content: (
                <Stack spacing={2} direction="column">
                  <IntegrationsSelectPanel value={props.value} onChange={props.onChange} />
                </Stack>
              ),
            },
            {
              title: 'Form Inputs',
              content: <ActionBOInputFieldsPanel value={props.value} onChange={props.onChange} />,
            },
            {
              title: 'API Configuration',
              content: (
                <ActionBOInputAPIConfiguration
                  authDataVariableList={authDataVariableList}
                  value={props.value}
                  onChange={props.onChange}
                />
              ),
            },
            {
              title: 'Source Code',
              content: (
                <SourceCodePanel
                  code={JSON.stringify(props.value, null, 2)}
                  onApply={(newCode) => {
                    const newValue = ActionTemplateSchema.parse(JSON.parse(newCode));
                    props.onChange(newValue);

                    alert('Apply success');
                  }}
                />
              ),
            },
          ]}
        />
        <Stack mt={3}>{props.customBottomComponent}</Stack>
      </Stack>
      <Stack
        ml={isMobile ? 0 : 5}
        sx={{
          width: isMobile ? '100%' : '30%',
          minWidth: isMobile ? '100%' : '400px',
        }}
      >
        <Affix>
          <Typography
            level="b1"
            mb={1}
            pt={1}
            sx={{
              textAlign: 'center',
            }}
          >
            Preview
          </Typography>
          <Stack
            sx={{
              p: 3,
              border: '1px solid var(--border-default)',
              background: 'var(--bg-surface)',
              maxHeight: 'calc(100vh - 180px)',
              overflow: 'auto',
            }}
          >
            {/* 预览效果 */}
            <ActionInput
              api={buildTrpcToolSDKApi()}
              value={{
                instanceId: '',
                template: {
                  // @ts-expect-error ignore
                  integrations: integrations,
                  kind: 'TOOL',
                  key: 'preview',
                  // actionKey: 'preview',
                  name: iStringParse(props.value.display.label),
                  description: iStringParse(props.value.display.description),
                  logo: props.value.logo,
                  // todo: DynamicFields运行和生成
                  inputFields: props.value.operation.inputFields as InputFieldBO[],
                },
                integrationsData,
                inputData,
              }}
              onChange={(newVal) => {
                setInputData(newVal.inputData);
                setIntegrationsData(newVal.integrationsData || {});
              }}
            />
            <Stack mt={2} alignItems="center" justifyContent="center">
              <Button
                sx={{ width: '100px' }}
                loading={loading}
                onClick={async () => {
                  try {
                    setLoading(true);
                    // const ret = await trpcClient.actions.test.mutate({
                    //   inputData,
                    //   integrationsData,
                    //   bo: props.value,
                    // });
                    // setRet(typeof ret === 'string' ? ret : JSON.stringify(ret, null, 2));
                    setRet('');
                    toast.success('Test success');
                    setOpen(true);
                    // router.push(`/actions/${value.bo.key}`);
                  } catch (error) {
                    toast.error((error as Error).message);
                  } finally {
                    setLoading(false);
                  }
                }}
              >
                Test
              </Button>
              <Modal open={open} onClose={() => setOpen(false)}>
                <ModalDialog variant="plain">
                  <ModalClose />
                  <DialogTitle>Result</DialogTitle>
                  <DialogContent sx={{ mt: 3 }}>
                    <Editor
                      value={ret}
                      theme="vs-dark"
                      language="json"
                      height="300px"
                      width="500px"
                      options={{
                        minimap: {
                          enabled: false,
                        },
                        folding: false,
                        showUnused: true,
                        tabSize: 2,
                        fontSize: 13,
                        tabCompletion: 'on',
                        lineNumbersMinChars: 3,
                        scrollBeyondLastLine: false,
                        automaticLayout: true,
                        scrollbar: { verticalScrollbarSize: 10, verticalSliderSize: 10 },
                      }}
                    />
                  </DialogContent>
                </ModalDialog>
              </Modal>
            </Stack>
          </Stack>
        </Affix>
      </Stack>
    </Stack>
  );
}
