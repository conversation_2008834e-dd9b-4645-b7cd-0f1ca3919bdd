'use client';

import React from 'react';
import type { ActionTemplate, InputFieldBO } from '@toolsdk.ai/sdk-ts/types/bo';
import { iStringParse } from 'basenext/i18n';
import { OperationPerformInput } from './operation-perform-input';
import type { VariableList } from '@bika/ui/editor/variable-input/input';

interface Props {
  value: ActionTemplate;
  onChange: (value: ActionTemplate) => void;
  authDataVariableList?: VariableList[];
}

export function ActionBOInputAPIConfiguration(props: Props) {
  const perform = props.value.operation.perform;

  const variableList = React.useMemo(() => {
    const inputFields = props.value.operation.inputFields as InputFieldBO[];
    const inputData = inputFields.map((field) => ({
      label: `{{bundle.inputData.${field.key}}}`,
      value: `bundle.inputData.${field.key}`,
      description: iStringParse(field.label),
    }));
    return [...inputData, ...(props.authDataVariableList || [])];
  }, [props.value.operation.inputFields, props.authDataVariableList]);

  return (
    <OperationPerformInput
      title="API Endpoint"
      description="Enter the URL where FormApp.ai will make the request and send the input form data. This Create request must return an object. Learn More."
      variableList={variableList}
      value={perform}
      onChange={(newVal) => {
        props.onChange({ ...props.value, operation: { ...props.value.operation, perform: newVal } });
      }}
    />
  );
}
