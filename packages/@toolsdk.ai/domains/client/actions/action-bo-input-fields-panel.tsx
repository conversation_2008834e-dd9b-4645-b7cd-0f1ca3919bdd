import React from 'react';
import type { ActionTemplate, FieldChoices, InputFieldBO } from '@toolsdk.ai/sdk-ts/types/bo';
import { FormAppInputFieldsInput } from '../fields/form-app-input-fields-input';
import { type TableListArrayColumn } from '@bika/ui/shared/types-form/table-list-array-input/index';
import { iStringParse } from 'basenext/i18n';
import type { InputFieldTypeBO } from '@toolsdk.ai/sdk-ts/types/bo';
import type { FieldChoiceWithLabel } from '@toolsdk.ai/sdk-ts/types/bo';
import { Button, Table } from '@mui/joy';
import { StringInput } from '@bika/ui/shared/types-form/string-input';

interface Props {
  value: ActionTemplate;
  onChange: (value: ActionTemplate) => void;
}

export function ActionBOInputFieldsPanel(props: Props) {
  const inputFieldsColumns: TableListArrayColumn<InputFieldBO>[] = [
    {
      label: 'key',
      width: 80,
      required: true,
      property: {
        fieldType: 'string',
        cellValue: (item) => iStringParse(item.key),
        setCellValue: (item, newVal) => ({ ...item, key: newVal }),
      },
    },
    {
      label: 'Label',
      width: 120,
      required: true,
      property: {
        fieldType: 'string',
        cellValue: (item) => iStringParse(item.label),
        setCellValue: (item, newVal) => ({ ...item, label: newVal }),
      },
    },
    {
      label: 'Type',
      width: 80,
      required: true,
      property: {
        fieldType: 'select',
        options: [
          {
            label: 'String',
            value: 'string',
          },
          {
            label: 'Number',
            value: 'number',
          },
          {
            label: 'Boolean',
            value: 'boolean',
          },
          {
            label: 'Texttarea',
            value: 'text',
          },
          {
            label: 'Integer',
            value: 'integer',
          },
          {
            label: 'Password',
            value: 'password',
          },
          {
            label: 'Datetime',
            value: 'datetime',
          },
          {
            label: 'Select',
            value: 'select',
          },
        ],
        cellValue: (item) => item.type || 'string',
        setCellValue: (item, newVal) => ({ ...item, type: newVal as InputFieldTypeBO }),
      },
    },
    {
      label: 'Help Text',
      required: true,
      property: {
        fieldType: 'string',
        cellValue: (item) => iStringParse(item.helpText),
        setCellValue: (item, newVal) => ({ ...item, helpText: newVal }),
      },
    },

    {
      label: 'Default value',
      visible: false,
      property: {
        fieldType: 'string',
        cellValue: (item) => iStringParse(item.default),
        setCellValue: (item, newVal) => ({ ...item, default: newVal }),
      },
    },

    {
      label: 'Required',
      required: true,
      width: 60,
      property: {
        fieldType: 'boolean',
        cellValue: (item) => item.required || false,
        setCellValue: (item, newVal) => ({ ...item, required: newVal }),
      },
    },
    {
      label: 'Choices',
      // property: {
      //   fieldType: 'custom',
      //   cellValue: (item) => item,
      //   setCellValue: (item, newVal) => newVal,
      // },
      visible: (item) => item.type === 'select',
      render({ value, onChange }) {
        const ensureArrayChoices = (choices?: FieldChoices) => {
          if (!Array.isArray(choices)) {
            // 如果choices不是数组，则转换为数组
            const choicesArray: (string | FieldChoiceWithLabel)[] = [];
            if (choices && typeof choices === 'object') {
              Object.entries(choices).forEach(([val, label]) => {
                choicesArray.push({
                  value: val,
                  sample: val,
                  label: String(label),
                });
              });
            }
            return choicesArray;
          }
          return choices;
        };

        const choices = ensureArrayChoices(value.choices);

        return (
          <div>
            <Table>
              <thead>
                <tr>
                  <th>Label</th>
                  <th>Value</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                {(choices || []).map((choice, index) => {
                  const isString = typeof choice === 'string';
                  const choiceObj = isString ? { value: choice, label: choice, sample: choice } : choice;

                  return (
                    <tr key={index}>
                      <td>
                        {!isString && (
                          <StringInput
                            value={choiceObj.label}
                            onChange={(newVal) => {
                              const newChoices = [...choices];
                              newChoices[index] = {
                                ...choiceObj,
                                label: newVal,
                              };
                              const data: InputFieldBO = { ...value, choices: newChoices };
                              onChange(data);
                            }}
                          />
                        )}
                      </td>
                      <td>
                        <StringInput
                          value={choiceObj.value}
                          onChange={(newVal) => {
                            const newChoices = [...choices];
                            if (isString) {
                              newChoices[index] = newVal;
                            } else {
                              newChoices[index] = {
                                ...choiceObj,
                                value: newVal,
                                sample: newVal,
                              };
                            }
                            const data: InputFieldBO = { ...value, choices: newChoices };
                            onChange(data);
                          }}
                        />
                      </td>
                      <td>
                        <Button
                          color="danger"
                          onClick={() => {
                            const newChoices = [...choices];
                            newChoices.splice(index, 1);
                            const data: InputFieldBO = { ...value, choices: newChoices };
                            onChange(data);
                          }}
                        >
                          Delete
                        </Button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
              <tfoot>
                <tr>
                  <td colSpan={3} style={{ textAlign: 'center' }}>
                    <Button
                      onClick={() => {
                        const newChoices = [...(choices || [])];
                        newChoices.push({
                          value: '',
                          sample: '',
                          label: '',
                        });
                        const data: InputFieldBO = { ...value, choices: newChoices };
                        onChange(data);
                      }}
                    >
                      Add Choice
                    </Button>
                  </td>
                </tr>
              </tfoot>
            </Table>
          </div>
        );
      },
    },
  ];
  return (
    <>
      Form Inputs
      <FormAppInputFieldsInput
        columns={inputFieldsColumns}
        value={props.value.operation.inputFields as InputFieldBO[]}
        onChange={(newVal) => {
          props.onChange({ ...props.value, operation: { ...props.value.operation, inputFields: newVal } });
        }}
      />
    </>
  );
}
