'use server';

import { HomePageClient } from '@toolsdk.ai/domain/client/webpages/index/home-page-client';
import { PackageSO } from '@toolsdk.ai/domain/server/package-so';
import style from './index.module.css';
import { PackageDAO } from '@toolsdk.ai/orm/prisma/dao/package-dao';

interface Props {
  disableCover?: boolean;
  searchParams: Promise<{ category: string | undefined }>;
}

export async function HomePageServer(props: Props) {
  const { category: qsCategory } = await props.searchParams;

  const categories = await PackageSO.listCategories();
  const { data } = await PackageSO.search({
    category: qsCategory || 'featured',
  });
  const allToolsCount = await PackageDAO.calcTotalToolsCount();

  return (
    <div className={style.app}>
      <HomePageClient
        toolsCount={allToolsCount}
        categories={categories}
        disableCover={props.disableCover}
        initPackages={data}
      />
    </div>
  );
}
