import React from 'react';
import { generateNanoID } from 'basenext/utils';
import type { InputFieldBO } from '@toolsdk.ai/sdk-ts/types/bo';
import { defaultInputFieldBO } from '@toolsdk.ai/sdk-ts/types/default';
import {
  TableListArrayInput,
  type TableListArrayColumn,
} from '@bika/ui/shared/types-form/table-list-array-input/index';

interface Props {
  value: InputFieldBO[];
  onChange: (value: InputFieldBO[]) => void;
  label?: string;
  columns: TableListArrayColumn<InputFieldBO>[];
}

export function FormAppInputFieldsInput(props: Props) {
  return (
    <>
      <TableListArrayInput<InputFieldBO>
        label={props.label}
        value={props.value}
        onChange={(newVal) => {
          props.onChange(newVal);
        }}
        columns={props.columns}
        defaultValue={() => defaultInputFieldBO({ id: generateNanoID() })}
      />
    </>
  );
}
