'use client';

import type { InputFieldBO, ZapierAuthenticationBO } from '@toolsdk.ai/sdk-ts/types/bo';
import { FormAppInputFieldsInput } from '../fields/form-app-input-fields-input';
import { OperationPerformInput } from '../actions/operation-perform-input';
import type { IntegrationTemplate } from '@toolsdk.ai/sdk-ts/types/bo';
import { AccordionGroup } from '@bika/ui/accordion-group';
import { Typography } from '@bika/ui/texts';
import { Stack } from '@bika/ui/layouts';
import { type TableListArrayColumn } from '@bika/ui/shared/types-form/table-list-array-input/index';
import { iStringParse } from 'basenext/i18n';
import type { InputFieldTypeBO } from '@toolsdk.ai/sdk-ts/types/bo';
import React from 'react';

interface Props {
  value: IntegrationTemplate;
  onChange: (value: ZapierAuthenticationBO) => void;
}
export function AuthenticationCustomBOInput(props: Props) {
  const authentication = props.value as ZapierAuthenticationBO;

  const inputFieldsColumns: TableListArrayColumn<InputFieldBO>[] = [
    {
      label: 'key',
      property: {
        fieldType: 'string',
        cellValue: (item) => iStringParse(item.key),
        setCellValue: (item, newVal) => ({ ...item, key: newVal }),
      },
    },
    {
      label: 'Label',
      property: {
        fieldType: 'string',
        cellValue: (item) => iStringParse(item.label),
        setCellValue: (item, newVal) => ({ ...item, label: newVal }),
      },
    },
    {
      label: 'Type',
      property: {
        fieldType: 'select',
        options: [
          {
            label: 'String',
            value: 'string',
          },
          {
            label: 'Password',
            value: 'password',
          },
        ],
        cellValue: (item) => item.type || 'string',
        setCellValue: (item, newVal) => ({ ...item, type: newVal as InputFieldTypeBO }),
      },
    },
    {
      label: 'Help Text',
      visible: false,
      property: {
        fieldType: 'string',
        cellValue: (item) => iStringParse(item.helpText),
        setCellValue: (item, newVal) => ({ ...item, helpText: newVal }),
      },
    },
    {
      label: 'Required',
      property: {
        fieldType: 'boolean',
        cellValue: (item) => item.required || false,
        setCellValue: (item, newVal) => ({ ...item, required: newVal }),
      },
    },
  ];

  return (
    <AccordionGroup
      items={[
        {
          title: 'Configure your Fields',
          content: (
            <>
              <Stack
                sx={{
                  p: 2,
                  border: '1px solid var(--border-default)',
                }}
              >
                <Typography level="h2">Authentication Fields</Typography>
                <Typography level="b2">
                  Build a form with fields for each item your API requires for authentication, including a field for
                  your API key and additional field for any other data needed. Zapier does not include any fields by
                  default. Learn More. You must define at least one field where your users can enter API credentials.
                  Your authentication configuration is not complete until you have done so. Note: Fields that are added
                  here will automatically get added to the Request Template.
                </Typography>
              </Stack>

              <FormAppInputFieldsInput
                columns={inputFieldsColumns}
                value={authentication.fields as InputFieldBO[]}
                onChange={(newVal) => {
                  props.onChange({ ...authentication, fields: newVal });
                }}
              />
            </>
          ),
        },
        {
          title: 'Configure a Test Request',
          content: (
            <Stack spacing={2} direction="column">
              <Stack
                sx={{
                  p: 2,
                  border: '1px solid var(--border-default)',
                }}
              >
                <Typography level="b2">
                  The only things needed for Basic auth are a test API call to verify users’ credentials, and a
                  connection label to identify accounts. Zapier automatically configures everything else, and includes
                  the username and password in API calls automatically.
                </Typography>
              </Stack>

              {/* test */}
              <OperationPerformInput
                title="Test API Call (required)"
                description="Make a test API call to verify your authentication."
                value={authentication.test || { method: 'GET' }}
                onChange={(newVal) => {
                  props.onChange({ ...authentication, test: newVal });
                }}
                variableList={[]}
              />
            </Stack>
          ),
        },
      ]}
    />
  );
}
