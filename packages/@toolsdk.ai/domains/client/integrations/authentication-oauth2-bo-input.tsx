'use client';

import { useEffect, useState } from 'react';
import type {
  InputFieldBO,
  ZapierAuthenticationBO,
  IntegrationTemplate,
  InputFieldTypeBO,
} from '@toolsdk.ai/sdk-ts/types/bo';
import assert from 'assert';
import React from 'react';
import { iStringParse } from 'basenext/i18n';
import { AccordionGroup } from '@bika/ui/accordion-group';
import { Stack } from '@bika/ui/layouts';
import { RadiosInput } from '@bika/ui/shared/types-form/radio-input';
import { StringInput } from '@bika/ui/shared/types-form/string-input';
import { type TableListArrayColumn } from '@bika/ui/shared/types-form/table-list-array-input/index';
import { Typography } from '@bika/ui/texts';
import { OperationPerformInput } from '../actions/operation-perform-input';
import { FormAppInputFieldsInput } from '../fields/form-app-input-fields-input';
import { Icon<PERSON>utton } from '@bika/ui/button';
import CopyOutlined from '@bika/ui/icons/components/copy_outlined';
import toast from 'react-hot-toast';
import { copyText } from '@bika/ui/utils';

interface Props {
  value: IntegrationTemplate;
  onChange: (value: ZapierAuthenticationBO) => void;
}
export function AuthenticationOAuth2BOInput(props: Props) {
  const authentication = props.value as ZapierAuthenticationBO;
  const key = 'AUTHENTICATION';
  const [redirect_uri, setRedirect_uri] = useState<string>('');
  useEffect(() => {
    setRedirect_uri(`${window.location.origin}/api/callbacks/integrations/${key}/oauth`);
    // async function fetchAuthorizeUrl() {
    //   const integrationKey = buildBusinessKey(props.value.key, props.value.version);
    //   // todo 表单待补
    //   const url = await buildTrpcToolSDKApi().integrations.fetchAuthorizeUrl({
    //     integrationKey,
    //     // inputData,
    //   });
    //   setRedirect_uri(url);
    // }
    // fetchAuthorizeUrl();
  }, [key]);

  const inputFieldsColumns: TableListArrayColumn<InputFieldBO>[] = [
    {
      label: 'key',
      property: {
        fieldType: 'string',
        cellValue: (item) => iStringParse(item.key),
        setCellValue: (item, newVal) => ({ ...item, key: newVal }),
      },
    },
    {
      label: 'Label',
      property: {
        fieldType: 'string',
        cellValue: (item) => iStringParse(item.label),
        setCellValue: (item, newVal) => ({ ...item, label: newVal }),
      },
    },
    {
      label: 'Type',
      property: {
        fieldType: 'select',
        options: [
          {
            label: 'String',
            value: 'string',
          },
          {
            label: 'Password',
            value: 'password',
          },
        ],
        cellValue: (item) => item.type || 'string',
        setCellValue: (item, newVal) => ({ ...item, type: newVal as InputFieldTypeBO }),
      },
    },
    {
      label: 'Required',
      property: {
        fieldType: 'boolean',
        cellValue: (item) => item.required || false,
        setCellValue: (item, newVal) => ({ ...item, required: newVal }),
      },
    },
  ];

  assert(props.value !== undefined && !Array.isArray(props.value), 'props.value.authentication is required');

  return (
    <AccordionGroup
      items={[
        {
          title: 'Configure your Fields',
          content: (
            <>
              <Stack
                sx={{
                  p: 2,
                  border: '1px solid var(--border-default)',
                }}
              >
                <Typography level="h2"> Use Authentication Fields to:</Typography>
                <Typography level="b2">
                  If your API’s OAuth Authorization URL does not require details about the user, click Continue to skip
                  this step. Otherwise, add input fields for each item—such as subdomain or team name—that your API
                  requires to show the Authorization page. Learn More. If your OAuth v2 implementation is standard you
                  don’t need to make any changes here! Note: Fields that are added here will automatically get added to
                  the Request Template.
                </Typography>
              </Stack>

              <FormAppInputFieldsInput
                columns={inputFieldsColumns}
                value={authentication.fields as InputFieldBO[]}
                onChange={(newVal) => {
                  props.onChange({ ...authentication, fields: newVal });
                }}
              />
            </>
          ),
        },
        {
          title: 'Copy your OAuth Redirect URL',
          content: (
            <>
              <StringInput
                readOnly
                onChange={() => {}}
                value={redirect_uri}
                required={true}
                helpText="Copy FormApp's OAuth Redirect URL below, and add it to the allowed list in your app’s admin console if needed."
                label={'OAuth Redirect URL'}
                // 复制按钮 一键复制
                endDecorator={
                  <IconButton
                    onClick={async () => {
                      await copyText(redirect_uri);
                      toast.success('Copied');
                    }}
                  >
                    <CopyOutlined />
                  </IconButton>
                }
              />
            </>
          ),
        },
        {
          title: 'Enter your Application Credentials',
          content: (
            <>
              <Stack p={1}>
                <RadiosInput<'on' | 'off'>
                  label={'Auth Type'}
                  value={props.value.oauth2Config?.enablePkce ? 'on' : 'off'}
                  onChange={(newVal) => {
                    const authentication = {
                      ...props.value,
                      oauth2Config: {
                        ...props.value.oauth2Config,
                        enablePkce: newVal === 'on',
                      },
                    } as ZapierAuthenticationBO;
                    props.onChange(authentication);
                  }}
                  options={[
                    {
                      label: 'OAuth v2',
                      value: 'off',
                      description:
                        'From your app’s API or developer settings, copy the Client ID and Secret from your app and paste them below. Learn More.',
                    },
                    {
                      label: 'OAuth v2 with PKCE Extension',
                      value: 'on',
                      description:
                        'PKCE (Proof Key for Code Exchange) is an extension of OAuth v2. It helps prevent certain types of attacks, such as authorization code interception. Learn More.',
                    },
                  ]}
                />
              </Stack>

              <StringInput
                value={props.value.oauth2Config?.clientId || ''}
                onChange={(newVal) => {
                  const authentication = {
                    ...props.value,
                    oauth2Config: {
                      ...props.value.oauth2Config,
                      clientId: newVal,
                    },
                  } as ZapierAuthenticationBO;
                  props.onChange(authentication);
                }}
                required={true}
                helpText="Copy Client ID from your app and enter here; may also be called Consumer Key or API Key."
                label={'Client ID'}
              />
              <StringInput
                value={props.value.oauth2Config?.clientSecret || ''}
                onChange={(newVal) => {
                  const authentication = {
                    ...props.value,
                    oauth2Config: {
                      ...props.value.oauth2Config,
                      clientSecret: newVal,
                    },
                  } as ZapierAuthenticationBO;
                  props.onChange(authentication);
                }}
                required={true}
                helpText="Copy Client Secret from your app and enter here; may also be called Consumer Secret or API Secret. "
                label={'Client Secret'}
              />
            </>
          ),
        },
        {
          title: 'Add OAuth v2 Endpoint Configuration',
          content: (
            <Stack spacing={2} direction="column">
              <Stack
                sx={{
                  p: 2,
                  border: '1px solid var(--border-default)',
                }}
              >
                <Typography level="b2">
                  Add the Authorization URL from your API—no additional settings are typically needed—optionally with
                  comma-separated scopes. Then add your API’s Access Token request and refresh endpoints. FormApp
                  includes the default fields, though click Show options to customize if needed. Finally add a test API
                  call to test your authentication, and a connection label to identify accounts. Learn More.
                </Typography>
              </Stack>
              <OperationPerformInput
                title="Authorization URL (required)"
                description="Specify where to send users to authenticate with your API."
                value={authentication.oauth2Config?.authorizeUrl || { method: 'GET' }}
                onChange={(newVal) => {
                  const authentication = {
                    ...props.value,
                    oauth2Config: {
                      ...props.value.oauth2Config,
                      authorizeUrl: newVal,
                    },
                  } as ZapierAuthenticationBO;
                  props.onChange(authentication);
                }}
                variableList={[]}
              />
              {/* Scope */}
              <StringInput
                value={authentication.oauth2Config?.scope || ''}
                onChange={(newVal) => {
                  const authentication = {
                    ...props.value,
                    oauth2Config: {
                      ...props.value.oauth2Config,
                      scope: newVal,
                    },
                  } as ZapierAuthenticationBO;
                  props.onChange(authentication);
                }}
                helpText="Enter the scope of the access request. This is a space-delimited list of permissions that the application requires."
                label={'Scope (optional)'}
              />
              {/* Access Token Reques */}
              <OperationPerformInput
                title="Access Token Request (required)"
                description="Specify how to exchange the authorization code for an access token."
                value={authentication.oauth2Config?.getAccessToken || { method: 'POST' }}
                onChange={(newVal) => {
                  const authentication = {
                    ...props.value,
                    oauth2Config: {
                      ...props.value.oauth2Config,
                      getAccessToken: newVal,
                    },
                  } as ZapierAuthenticationBO;
                  props.onChange(authentication);
                }}
                variableList={[]}
              />
              {/* test */}
              <OperationPerformInput
                title="Test API Call (required)"
                description="Make a test API call to verify your authentication."
                value={authentication.test || { method: 'GET' }}
                onChange={(newVal) => {
                  props.onChange({ ...authentication, test: newVal });
                }}
                variableList={[]}
              />
              <StringInput
                value={authentication.connectionLabel || ''}
                onChange={(newVal) => {
                  props.onChange({ ...authentication, connectionLabel: newVal });
                }}
                helpText="Customize the connection label users see in Formapp.ai to help differentiate between multiple connected accounts for your app. If included, do not use sensitive information. Include info from authentication input fields with {{bundle.authData.field}} or from test request output fields with {{bundle.inputData.field}}, replacing field with the field key you wish to use. Learn More."
                label={'Connection Label (optional)'}
              />
            </Stack>
          ),
        },
      ]}
    />
  );
}
