import { AuthObject } from '@clerk/backend';
import { TRPCError } from '@trpc/server';
import { FetchCreateContextFnOptions } from '@trpc/server/adapters/fetch';
// import { LOCALE_HEADER } from '@bika/types/shared';
import { Locale } from 'basenext/i18n';
import { trpc } from './trpc-instance';
import { AuthSO } from '../../server/auth-so';
import { DeveloperSO } from '../../server/developer-so';

export interface ApiFetchRequestContext {
  req: Request;
  resHeaders?: Headers;
  auth: AuthObject;
  user: DeveloperSO | null;
  // session: SessionVO | null;
  locale: Locale;
}

export async function createFetchRequestContext({
  req,
  resHeaders,
}: FetchCreateContextFnOptions): Promise<ApiFetchRequestContext> {
  // const { headers } = req;
  const auth = await AuthSO.auth();
  const user = await AuthSO.currentUser();
  // const sessionId = SessionSO.parseSessionIdFromHeader(headers);
  // const locale = (headers.get(LOCALE_HEADER) || 'en') as Locale;
  const locale = 'en' as Locale;

  return {
    req,
    resHeaders,
    auth,
    user,
    locale,
  };
}

export type CreateFetchContext = Awaited<ReturnType<typeof createFetchRequestContext>>;

/**
 * 审计日志中间件
 */
const loggingMiddleware = trpc.middleware(async (opts) => {
  const { ctx, next } = opts;
  return next({ ctx });
});

/**
 * 鉴权中间件
 */
const authorizeMiddleware = trpc.middleware(async (opts) => {
  const { ctx, next } = opts;

  if (ctx.user === null) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }
  return next({ ctx });
});
export const publicProcedure = trpc.procedure.use(loggingMiddleware);

// With Auth
// check if the user is signed in, otherwise throw a UNAUTHORIZED CODE
export const protectedProcedure = trpc.procedure.use(authorizeMiddleware).use(loggingMiddleware);
