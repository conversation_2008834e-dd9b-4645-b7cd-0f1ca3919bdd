import { generateNanoID } from 'basenext/utils';
import { test, expect } from 'vitest';
import { createMockToolSDKAIApi, createMockToolSDKAIAccountApi } from './mock-toolsdk-http-client';
import { TestContext } from './test-context';
import { DeveloperSO } from '../server/developer-so';

test('openapi api client test', async () => {
  //
  const { developer } = await TestContext.initMockDeveloper();
  const secretKey = await developer.getApiSecretKey();
  const api = createMockToolSDKAIApi(secretKey);

  const pkg = await TestContext.getTestMCPServer(developer.id);
  // const newAction = await ActionTemplateSO.create(developer.id, {
  //   packageId: pkg.id,
  //   componentData: {
  //     type: 'TOOLAPP_JSON',
  //     data: {
  //       componentType: 'TOOL',
  //       ...defaultActionBO({ id: generateNanoID() }),
  //     },
  //   },
  // });
  // const newActionVO = newAction.toDetailVO();

  // start open api
  const mcpServersList = await api.packages.pages();
  expect(mcpServersList.data.length).toBeGreaterThan(0);

  const mcpServer = await api.packages.get(pkg.key, pkg.version);
  expect(mcpServer).toBeDefined();

  // get mcp server tools
  const toolsList = mcpServer.tools;
  expect(Object.keys(toolsList).length).toBeGreaterThan(0);

  // after star it, check my servers
  // const myStarsMcpServers = await api.servers.my();
  // expect(myStarsMcpServers.data.length).toBeGreaterThan(0);

  // run server tools, directly

  // run account's server tools

  // const myActions = await api.tools.my();
  // expect(myActions.length).toBeGreaterThan(0);

  // expect(newActionVO).toBeDefined();
  //   const action = myActions[0];
  //   expect(action.id).toBe(newActionVO.id);
  //   api.actions.run('key', { params: 'params' });
});

test('openapi account client test', async () => {
  //
  const superDeveloper = await DeveloperSO.super.upsert();
  expect(superDeveloper).toBeDefined();
  const secretKey = await superDeveloper.getApiSecretKey();
  const secretApi = createMockToolSDKAIApi(secretKey);
  const accountKey = 'mockVault';
  const accountToken = await secretApi.account(accountKey).fetchToken();
  expect(accountToken.length).toBeGreaterThan(0);

  const pkg = await TestContext.getTestMCPServer(superDeveloper.id);

  // Create a tool.
  // const newTool = await ActionTemplateSO.create(superDeveloper.id, {
  //   packageId: pkg.id,
  //   componentData: {
  //     type: 'TOOLAPP_JSON',
  //     data: {
  //       componentType: 'TOOL',
  //       ...defaultActionBO({ id: generateNanoID() }),
  //     },
  //   },
  // });
  // expect(newTool).toBeDefined();

  const consumerKey = `mock_${generateNanoID()}`;

  const publicApi = createMockToolSDKAIAccountApi(accountToken);
  const newPkgIns = await publicApi.packages.putInstance(consumerKey, {
    packageKey: pkg.key,
    inputData: {
      message: 'hello world',
    },
  });
  expect(newPkgIns.instanceId).toBeDefined();
  expect(newPkgIns.consumerKey).toBe(consumerKey);
  expect(newPkgIns.package).toBeDefined();
  expect(newPkgIns.package!.key).toBe(pkg.key);
  expect(newPkgIns.inputData).toBeDefined();
  expect(newPkgIns.inputData.message).toBe('hello world');

  const packageInstance = await publicApi.packages.getInstance(newPkgIns.instanceId);
  expect(packageInstance).toBeDefined();
  expect(packageInstance.instanceId).toBe(newPkgIns.instanceId);

  expect(packageInstance.package).toBeDefined();
  expect(packageInstance.package!.key).toBe(pkg.key);
  expect(packageInstance.consumerKey).toBe(consumerKey);
  expect(packageInstance.inputData).toBeDefined();
  expect(packageInstance.inputData.message).toBe('hello world');

  // 看看有什么tools可以选的
  const listComponents = packageInstance.package?.tools;
  expect(listComponents?.length).toBeGreaterThan(0);

  // Dynamic Input Fields
  // 选择tool
  const updatedPkgIns = await publicApi.packageInstance(newPkgIns.instanceId).update({
    toolKey: 'add', // newTool.key,
    inputData: {
      message: 'bingo',
    },
  });
  expect(updatedPkgIns).toBeDefined();
  expect(updatedPkgIns.package).toBeDefined();
  expect(updatedPkgIns.package!.key).toBe(pkg.key);
  expect(updatedPkgIns.toolKey).toBe('add');
  expect(updatedPkgIns.inputData).toBeDefined();
  expect(updatedPkgIns.inputData.message).toBe('bingo');

  // TODO: run test

  // expect(inputFields2[0].key).toBe('message'); // follow defaultActionBO的数据，第一个 field

  // const inputFields = await publicApi.runDynamicFields({ actionKey: actionTemplate.key, consumerKey }, {});
  // expect(inputFields).toBeDefined();
});
