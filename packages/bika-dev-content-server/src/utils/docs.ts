import fs from 'fs';
import path from 'node:path';
import matter from 'gray-matter';
import { mdxToHtml } from 'sharelib/mdx-utils';

const BLOG_PATH = path.join(process.cwd(), '../..', 'contents/docs/blog');
const HELP_PATH = path.join(process.cwd(), '../..', 'contents/docs/help');

const PATH = {
  blog: BLOG_PATH,
  help: HELP_PATH,
};

export interface MarkdownRenderer {
  slug: string;
  meta: Record<string, string>;
  content: string;
  source?: boolean;
}

export async function fsMdxData(
  type: 'help' | 'blog' | string,
  lang: string,
  slug: string[],
): Promise<MarkdownRenderer> {
  // @ts-expect-error 没关系 异常会出去
  const basePath = PATH[type];

  let fileExt = '.mdx';
  let filePath = path.join(basePath, lang, `${slug.join('/')}${fileExt}`);
  if (!fs.existsSync(filePath)) {
    fileExt = '.md';
    filePath = path.join(basePath, lang, `${slug.join('/')}.md`);
  }
  // console.log(filePath);
  // assert(fs.existsSync(filePath), `File not found: ${filePath}`);

  try {
    console.warn(`Reading local file .mdx...${filePath}`);
    const source = await fs.readFileSync(filePath, 'utf8');
    const { data } = matter(source);
    // const mdx = await BLOG_MDX_CHUNKS[lang][`${slug.join('/')}.mdx`];
    // 不要太动态下面字符串，webpack会根据完整字面量做转换
    const mdx =
      type === 'blog'
        ? await import(`@bika/contents/docs/blog/${lang}/${slug.join('/')}${fileExt}`)
        : await import(`@bika/contents/docs/help/${lang}/${slug.join('/')}${fileExt}`);

    const html = await mdxToHtml(mdx.default);

    // const ReactDOMServer = (await import('react-dom/server')).default;
    // let html = ReactDOMServer.renderToStaticMarkup(React.createElement(mdx.default));
    // // 过滤掉 <hr/>任意换行空格<h2> 到 </h2> 之间的内容
    // const reg = /<hr\/>[\s\n]*<h2>([\s\S]*?)<\/h2>/;
    // const match = html.match(reg);
    // if (match) {
    //   html = html.replace(match[0], '');
    // }

    const fullSlug = `${lang}/${type}/${slug.join('/')}`;
    return {
      slug: fullSlug,
      meta: data,
      content: html,
    };
  } catch (e) {
    console.error(e);
    throw new Error(`help not found or compile error ${filePath}`);
  }
}
