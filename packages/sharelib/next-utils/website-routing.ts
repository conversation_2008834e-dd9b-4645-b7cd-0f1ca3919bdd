// import { isFromCNHost } from '@bika/domains/shared/server/utils/http';
// import { db } from '@bika/server-orm';
import { i18n } from 'basenext/i18n'; // 注释掉，因为不再使用自动语言跳转
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
// import { LOCALE_HEADER } from '@bika/types/shared';
// import { checkAuth } from './check-auth';

/**
 * 静态网站如何处理
 *
 * @param request
 * @param headers
 */
export async function parseWebsiteRouting(request: NextRequest, headers: Headers, LOCALE_HEADER: string) {
  const { pathname, searchParams, search } = request.nextUrl;

  // Check if there is any supported locale in the pathname
  const pathnameIsMissingLocale = i18n.extendLocales.every(
    (item) => !pathname.startsWith(`/${item}/`) && pathname !== `/${item}`,
  );

  const locale = pathnameIsMissingLocale ? 'en' : pathname.split('/')[1]; // 默认是英文;
  headers.set(LOCALE_HEADER, locale);

  // 如果是 en 语言 用户带了 /en 帮他跳转走 其他的原封不动显示
  if (locale === 'en') {
    if (pathname.startsWith('/en')) {
      const url = pathname.replace('/en', '');
      return NextResponse.redirect(new URL(url || '/', request.url), {
        status: 301,
        headers,
      });
    }
    // 重写
    const url = new URL(`/${locale}${pathname.startsWith('/') ? '' : '/'}${pathname}${search}`, request.url);
    return NextResponse.rewrite(url, { headers });
  }

  return null;
}
