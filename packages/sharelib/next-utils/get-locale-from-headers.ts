import { i18n, matchLocale } from 'basenext/i18n'; // 注释掉，因为不再使用自动语言跳转
import Negotiator from 'negotiator';

/**
 * 从header中获取语言，即默认浏览器语言
 *
 * @param requestHeaders
 * @returns
 */
export function getLocaleByHeaders(requestHeaders: Headers): string | undefined {
  // Negotiator expects plain object so we need to transform headers
  const negotiatorHeaders: Record<string, string> = {};
  requestHeaders.forEach((value: string, key: string) => {
    negotiatorHeaders[key] = value;
  });

  const { locales } = i18n;

  // Use negotiator and intl-localematcher to get best locale
  const languages = new Negotiator({ headers: negotiatorHeaders }).languages(Array.from(locales));

  const locale = matchLocale(languages);

  return locale;
}
