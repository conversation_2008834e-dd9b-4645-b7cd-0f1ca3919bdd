import assert from 'assert';
import fs from 'fs';
import path from 'path';
import { $Enums, Prisma } from '../prisma-client';
import { TemplatesVerified } from '@bika/contents/config/server/template/templates-init-data';
import { LocalContentLoader } from '../../content';
import { db } from '../../db';
import { generateNanoID } from 'basenext/utils';

async function doSyncTemplateAssets(templateId: string) {
  const templateAssetDir = LocalContentLoader.template.getLocalTemplateFullDir(path.join(templateId, 'assets'));
  if (!templateAssetDir) {
    return;
  }
  if (!fs.existsSync(templateAssetDir)) {
    return;
  }
  const isImage = (ext: string) =>
    ['.png', '.jpg', 'jpeg', '.gif', '.svg', '.psd', 'bmp', '.tiff', '.webp'].indexOf(ext.toLocaleLowerCase()) !== -1;

  const syncAsset = async (attachmentId: string, localFilePath: string) => {
    const attachmentPO = await db.prisma.attachment.findUnique({ where: { id: attachmentId } });
    if (attachmentPO) {
      // exists do nothing
      return;
    }
    const ext = path.extname(localFilePath);
    const objectName = `template/${attachmentId}${ext}`;
    // 直接上传
    await db.minio.fPutObject(db.minioBucketName, objectName, localFilePath);
    // 获取文件信息
    const attachStat = await db.minio.statObject(db.minioBucketName, objectName);
    const attachmentCreateInput: Prisma.AttachmentCreateInput = {
      id: attachmentId,
      path: objectName,
      etag: attachStat.etag,
      size: attachStat.size,
      ext,
    };
    if (isImage(ext)) {
      // todo 缩略图？
    }
    await db.prisma.attachment.create({ data: attachmentCreateInput });
    console.log(`sync template [${templateId}] asset: ${attachmentId}`);
  };

  const entries = fs.readdirSync(templateAssetDir, { withFileTypes: true });
  for (const entry of entries) {
    // 附件都是平铺的
    if (!entry.isDirectory()) {
      const attachmentId = entry.name.split('.')[0];
      await syncAsset(attachmentId, path.join(templateAssetDir, entry.name));
    }
  }
}

async function doSeedTemplate(
  templateId: string,
  onEndTemplateId: (templateId: string) => void,
  onEndReleaseId: (releaseId: string) => void,
  syncAssets: (templateId: string) => void,
) {
  const customTemplate = (await import(`@bika/contents/templates/${templateId}/template`)).default;
  const localTplRepo = await LocalContentLoader.template.parseCustomTemplate(customTemplate);
  const currentTpl = localTplRepo.current.data;
  assert(currentTpl.templateId, `templateId is required: ${templateId}`);
  assert(currentTpl.version, `version is required: ${templateId}`);
  assert(currentTpl.templateId === templateId, `templateId not match: ${currentTpl.templateId}:${templateId}`);

  console.log(`Writing Template: ${currentTpl.templateId}:${currentTpl.version}`);

  const verifiedTemplate = TemplatesVerified[currentTpl.templateId] || false;

  const updateFieldsOfStoreTemplate = {
    source: $Enums.StoreTemplateSource.OFFICIAL,
    name: currentTpl.name,
    description: currentTpl.description,
    readme: localTplRepo.readme!,
    cover: currentTpl.cover,
    category: currentTpl.category,
    currentVersion: currentTpl.version,
    visibility: currentTpl.visibility || 'PUBLIC',
    verified: verifiedTemplate,
    keywords: localTplRepo.keywords,
    personas: localTplRepo.personas,
    useCases: localTplRepo.useCases,
    installOnce: localTplRepo.installOnce,
    detachable: localTplRepo.detach,
  };

  // TemplateRepo(BO) -> PO
  await db.prisma.storeTemplate.upsert({
    where: {
      templateId: currentTpl.templateId,
    },
    update: updateFieldsOfStoreTemplate,
    create: {
      templateId: currentTpl.templateId,
      ...updateFieldsOfStoreTemplate,
    },
  });
  onEndTemplateId(currentTpl.templateId);

  // 插入releases

  for (const localRelease of localTplRepo.releases) {
    const localReleaseTemplate = localRelease.data;
    console.log(`Writing Template Release: ${localRelease.data.templateId}:${localRelease.version}`);

    const updatedFieldsOfStoreTemplateReleases = {
      data: localReleaseTemplate as unknown as Prisma.JsonObject,
      changelog: localRelease.changelog,
      releaseNotes: localRelease.releaseNotes,
      version: localReleaseTemplate.version,
    };

    // 插入release
    const upsertRelease = await db.prisma.storeTemplateRelease.upsert({
      where: {
        templateId_version: {
          templateId: localReleaseTemplate.templateId,
          version: localReleaseTemplate.version,
        },
      },
      update: updatedFieldsOfStoreTemplateReleases,
      create: {
        id: generateNanoID('tpr'),
        templateId: localReleaseTemplate.templateId,
        ...updatedFieldsOfStoreTemplateReleases,
      },
    });

    onEndReleaseId(upsertRelease.id);
  }

  // handle asset
  syncAssets(templateId);
}

export async function seedTemplates() {
  // 模板
  const localTemplates = await LocalContentLoader.template.fsTemplatesList();
  const upsertTemplateIds: string[] = [];
  const upsertReleaseIds: string[] = []; // 记录有什么release DB ID
  // 异步批量
  await Promise.all(
    localTemplates.map((lt) =>
      doSeedTemplate(
        lt.templateId,
        (templateId) => {
          // 记录变化
          upsertTemplateIds.push(templateId);
        },
        (releaseId) => {
          // 记录变化
          upsertReleaseIds.push(releaseId);
        },
        (templateId) => {
          doSyncTemplateAssets(templateId);
        },
      ),
    ),
  );

  // 开始做冗余清理...

  const tplsToDelete = await db.prisma.storeTemplate.findMany({
    where: {
      source: $Enums.StoreTemplateSource.OFFICIAL,
      NOT: {
        templateId: {
          in: upsertTemplateIds,
        },
      },
    },
  });

  const releasesToDelete = await db.prisma.storeTemplateRelease.findMany({
    where: {
      NOT: {
        id: {
          in: upsertReleaseIds,
        },
      },
      templateId: {
        in: tplsToDelete.map((tpl) => tpl.templateId),
      },
    },
  });

  // 反删多余的release
  const deletedReleases = await db.prisma.storeTemplateRelease.deleteMany({
    where: {
      id: {
        in: releasesToDelete.map((rel) => rel.id),
      },
    },
  });
  console.log('Deleted Unnecessary Template Release: ', deletedReleases.count);

  // 反删除多余的template
  await db.prisma.storeTemplateRelease.deleteMany({
    where: {
      templateId: {
        in: tplsToDelete.map((tpl) => tpl.templateId),
      },
    },
  });
  const deletedTpls = await db.prisma.storeTemplate.deleteMany({
    where: {
      templateId: {
        in: tplsToDelete.map((tpl) => tpl.templateId),
      },
    },
  });

  console.log('Deleted Unnecessary Template: ', deletedTpls.count, deletedTpls);
}
