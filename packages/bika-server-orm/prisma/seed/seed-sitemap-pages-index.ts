// 将网页内容，索引到搜索引擎，方便搜索帮助中心
import { i18n, type Locale } from 'basenext/i18n';
import { getFeatureInfo } from '@bika/contents/config/client/system/features';
import { getServerLocaleContext } from '@bika/contents/i18n/server';
import type { FeatureType } from '@bika/types/website/bo';
import { LocalContentLoader, type PageProps } from '../../content';
import { db } from '../../db';
import { AutoHelpGenSO } from '../../content/auto-help-gen-so';

async function doSeedAutoHelpPagesIndex(hp: PageProps, onEnd: (url: string) => void) {
  //
  const helpRoute = hp.slugs[1] as FeatureType;
  const helpName = hp.slugs[2];
  const url = `/${hp.lang}/help/${hp.slugs.join('/')}`;
  let label: string;
  let description: string;

  if (helpName === 'features-list') {
    // features-list
    const localeContext = getServerLocaleContext(hp.lang as Locale);
    const listConfig = getFeatureInfo(helpRoute, localeContext);
    label = listConfig.label;
    description = '';
  } else {
    // features-detail
    const helpConfig = await AutoHelpGenSO.getAutoHelpConfig(hp.lang as Locale, helpRoute, helpName);
    label = helpConfig.label;
    description = helpConfig.description;
  }

  console.log(`Writing Auto Help Page into Search Index: ${url}, length: ${description.length}`);
  await db.search.write({
    id: url,
    indexData: {
      type: 'WEB_PAGE',
      url,
      title: label,
      content: description
        .replace(/<[^>]*>/g, '') // 去掉 HTML 标签
        .replace(/\n/g, ' ') // 替换换行符为一个空格
        .replace(/\s+/g, ' ') // 替换多个空格为一个空格
        .trim(), // 去掉首尾空格,
    },
  });
  onEnd(url);
}
/**
 * 由于tsx不能使用mdx读取，这里是用dbHelp方法读取帮助中心内容
 *
 * 这意味着，帮助中心的内容，必须在数据库中存在 seed-tsx要在seed-bun之后执行
 *
 * @param hp
 * @param onEnd
 */
async function doSeedHelpPagesIndex(hp: PageProps, onEnd: (url: string) => void) {
  const url = `/${hp.lang}/${hp.slugs.join('/')}`;
  const helpMdx = await LocalContentLoader.help.dbHelp(hp.lang, hp.slugs);
  const content = helpMdx.content;
  console.log(`Writing Help Page into Search Index: ${url}, length: ${content.length}`);

  await db.search.write({
    id: url,
    indexData: {
      type: 'WEB_PAGE',
      url,
      title: helpMdx.meta.title || helpMdx.meta.sidebar_label,
      content: content
        .replace(/<[^>]*>/g, '') // 去掉 HTML 标签
        .replace(/\n/g, ' ') // 替换换行符为一个空格
        .replace(/\s+/g, ' ') // 替换多个空格为一个空格
        .trim(), // 去掉首尾空格,
    },
  });
  onEnd(url);
}
export async function seedSitemapPagesIndex() {
  // help
  // 帮助

  const helpPaths = await LocalContentLoader.help.dbHelpsList();

  const upsertUrls: string[] = [];

  await Promise.all(
    helpPaths.map((hp) =>
      doSeedHelpPagesIndex(hp, (url) => {
        upsertUrls.push(url);
      }),
    ),
  );

  for (const locale of i18n.locales) {
    const autoHelps = AutoHelpGenSO.getMenuItems(locale);
    // allPaths = [...allPaths, ...autoHelps];
    await Promise.all(
      autoHelps.map((hp) =>
        doSeedAutoHelpPagesIndex(hp, (url) => {
          upsertUrls.push(url);
        }),
      ),
    );
  }
}
