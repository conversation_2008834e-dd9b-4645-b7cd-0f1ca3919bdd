import { i18n, type Locale } from 'basenext/i18n';
import { getFeatureAbilityConfig, getFeatureInfo } from '@bika/contents/config/client/system/features';
import { getServerLocaleContext } from '@bika/contents/i18n/server';
import { FeatureTypes, FeatureType, IFeatureAbilityConfigDetail } from '@bika/types/website/bo';
import { db } from '../../db';

async function doWriteIndex(
  abilityConfig: IFeatureAbilityConfigDetail,
  featureType: FeatureType,
  featureName: string,
  locale: Locale,
) {
  const id = `${featureType}-${featureName}-${locale}`.toLowerCase().replaceAll('_', '-');

  console.log(
    `Writing feature type to search index: ${featureType}, name: ${featureName}, locale: ${locale}, id: ${id}`,
  );

  await db.search.write({
    // indexName: 'PRODUCT_FEATURE',
    id,
    indexData: {
      type: 'PRODUCT_FEATURE',
      locale,
      ...abilityConfig,
    },
  });
}

async function doIndexTemplate() {
  await db.search.initTemplateIndexSchema('PRODUCT_FEATURE', '*.description');
}

export async function seedProductFeaturesConfigIndex() {
  await doIndexTemplate();

  await loopAllFeatureAbilities(
    (abilityConfig: IFeatureAbilityConfigDetail, featureType: FeatureType, featureName: string, locale: Locale) => {
      return doWriteIndex(abilityConfig, featureType, featureName, locale);
    },
  );
}

export async function loopAllFeatureAbilities(
  handler: (
    abilityConfig: IFeatureAbilityConfigDetail,
    featureType: FeatureType,
    featureName: string,
    locale: Locale,
  ) => Promise<void>,
) {
  const promises: Promise<void>[] = [];

  for (const locale of i18n.locales) {
    const localeContext = getServerLocaleContext(locale);
    for (const featureType of FeatureTypes) {
      const { abilities: names } = getFeatureInfo(featureType, localeContext);
      for (const featureName of names) {
        const featureConfig = getFeatureAbilityConfig(localeContext, featureType, featureName);
        promises.push(handler(featureConfig, featureType, featureName, locale));
      }
    }
  }
  return Promise.all(promises);
}
