import { z } from 'zod';
import { RecordChangeLogSchema, SpaceTrashLogModelSchema } from '@bika/types/change/bo';
import { AppsumoEventLogSchema } from '@bika/types/pricing/bo';
import { SpaceAuditLogSchema, AccessLogSchema } from '@bika/types/system';
import { TrackLogSchema } from '@bika/types/system/track';
import { TemplatePreviewLogSchema } from '@bika/types/template/vo';
import { AdminLogSchema } from './admin-log';
import { AILogBOSchema } from './ai-log';
import { KeyValueLogSchema } from './key-value-log';
import { ApiRequestLogSchema } from './openapi-log';
import { SearchLogSchema } from './search-log';

export const LogValueSchema = z.discriminatedUnion('kind', [
  // Site Admin Log
  ...AdminLogSchema.options,

  // 用户搜索日志
  SearchLogSchema,

  // AB测试key-value日志
  KeyValueLogSchema,

  // API调用LOG
  AccessLogSchema,

  // 前端点击
  ...TrackLogSchema.options,

  // Space空间站日志
  SpaceAuditLogSchema,

  SpaceTrashLogModelSchema,

  AppsumoEventLogSchema,

  RecordChangeLogSchema,

  // 模板预览日志
  TemplatePreviewLogSchema,

  AILogBOSchema,

  // OpenAPI请求日志
  ApiRequestLogSchema,
]);
export type LogValue = z.infer<typeof LogValueSchema>;

// 有什么类型的日志呀
export type LogNameType = LogValue['kind'];

export const LogModelSchema = LogValueSchema.and(
  z.object({
    createdAt: z.string().datetime(),
  }),
);
// 持久化的，多了个createdAt自动刷入
export type LogModel = z.infer<typeof LogModelSchema>;
