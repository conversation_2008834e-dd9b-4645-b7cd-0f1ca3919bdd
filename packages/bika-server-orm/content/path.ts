import fs from 'fs';
import path from 'path';

export const BLOG_PATH = path.join(process.cwd(), '../..', 'contents/docs/blog');
// export const BLOG_AIGC_PATH = path.join(process.cwd(), '../..', 'contents/docs/blog_aigc');
export const HELP_PATH = path.join(process.cwd(), '../..', 'contents/docs/help');
export const PAGE_PATH = path.join(process.cwd(), '../..', 'contents/pages');

/**
 * 寻找本地模板内容存放路径
 * @returns
 */
export function getLocalTemplatePath() {
  const parentParentPath = path.join(process.cwd(), '../..', 'contents/templates');
  const parentPath = path.join(process.cwd(), '..', 'contents/templates');
  const samePath = path.join(process.cwd(), 'contents/templates');
  if (fs.existsSync(parentParentPath)) {
    return parentParentPath;
  }
  if (fs.existsSync(parentPath)) {
    return parentPath;
  }
  if (fs.existsSync(samePath)) {
    return samePath;
  }
  return null;
}

// export const BLOG_MDX_CHUNKS: Record<string, Record<string, Promise<any>>> = {};

// i18n.locales.forEach((lang) => {
//   // 获取文件夹下所有mdx文件 非_开头
//   function getBlogChunks() {
//     const fullPath = path.join(BLOG_PATH, lang);
//     // 判断路径存在防止CI
//     if (!fs.existsSync(fullPath)) {
//       return [];
//     }
//     return fs.readdirSync(fullPath).filter((file) => !file.startsWith('_') && file.endsWith('.mdx'));
//   }
//   BLOG_MDX_CHUNKS[lang] = {};
//   getBlogChunks().forEach((file) => {
//     BLOG_MDX_CHUNKS[lang][file] = import(`@bika/contents/docs/blog/${lang}/${file}`);
//   });
// });
