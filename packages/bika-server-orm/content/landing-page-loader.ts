import * as fs from 'fs';
import { $Enums } from '../prisma/prisma-client';
import { i18n, Locale } from 'basenext/i18n';
import type { LandingPage } from '@bika/contents/pages/type';
import { getAppEnv } from 'sharelib/app-env';
import { PAGE_PATH } from './path';
import { PageProps } from './types';
import { mapDirectoriesToFiles } from 'sharelib/fs-utils';
import { db } from '../db';
import { StoreTemplateCateogryEnums } from '@bika/types/template/vo';

export class LocalContentLandingPageLoader {
  /**
   * 列出所有落地页，用于sitemap
   * @returns
   */
  async fsLandingPagesList(langFilter?: Locale): Promise<PageProps[]> {
    const pagePaths = await mapDirectoriesToFiles(PAGE_PATH);
    const ret: PageProps[] = [];
    for (const pagePath of pagePaths) {
      const urlSegs = pagePath.split('/');
      const lang = urlSegs[0];
      if (langFilter === undefined || langFilter == lang) {
        const slugs = urlSegs.slice(1);

        const ignoresSlug = ['README'];

        if (
          i18n.locales.includes(lang as Locale) && // 必须是支持的语言
          !ignoresSlug.includes(slugs[0]) && // 忽略index和README
          !slugs[0].startsWith('_') // 忽略下划线开头
        ) {
          const rPage: PageProps = {
            lang,
            slugs,
          };
          ret.push(rPage);
        }
      }
    }
    return ret;
  }

  public async dbLandingPagesList(langFilter?: Locale): Promise<PageProps[]> {
    const pages = await db.prisma.content.findMany({
      where: {
        type: $Enums.ContentType.LANDING_PAGE,
      },
    });
    const ret: PageProps[] = [];
    for (const page of pages) {
      const spilts = page.slug.split('/');
      const sLang = spilts[0];
      const slugs = spilts.slice(1);
      if (langFilter === undefined || langFilter === sLang) {
        ret.push({
          lang: sLang,
          slugs,
        });
      }
    }
    return ret;
  }

  /**
   *  静态网页配置，
   *
   * @returns
   */
  private getStaticLandingPages(langFilter?: Locale): PageProps[] {
    const result: PageProps[] = [];
    for (const locale of i18n.locales) {
      if (langFilter === undefined || langFilter == locale) {
        result.push({
          lang: locale,
          slugs: ['integration'],
        });
        result.push({
          lang: locale,
          slugs: ['template'],
        });
        for (const cate of StoreTemplateCateogryEnums) {
          result.push({
            lang: locale,
            slugs: ['template', 'category', cate],
          });
          result.push({
            lang: locale,
            slugs: ['blog'],
          });
        }
        result.push({
          lang: locale,
          slugs: ['ai-app-builder', 'ai/replay'],
        });
      }
    }

    return result;
  }

  public async autoLandingPagesList(lang?: Locale): Promise<PageProps[]> {
    const staticPages = this.getStaticLandingPages(lang);
    if (getAppEnv() === 'LOCAL' && fs.existsSync(PAGE_PATH)) {
      return [...(await this.fsLandingPagesList(lang)), ...staticPages];
    }
    // 数组合并
    return [...(await this.dbLandingPagesList(lang)), ...staticPages];
  }

  public async dbLandingPage(lang: string, slugs: string[]): Promise<LandingPage | null> {
    const slug = `${lang}/${slugs.join('/')}`;
    const pagePO = await db.prisma.content.findUnique({
      where: {
        type_slug: {
          slug,
          type: $Enums.ContentType.LANDING_PAGE,
        },
      },
    });
    if (pagePO) return pagePO.data as unknown as LandingPage;
    return null;
  }

  public async autoLandingPage(lang: string, slugs: string[]): Promise<LandingPage | null> {
    const localFilePath = `${PAGE_PATH}/${lang}/${slugs.join('/')}.ts`;
    if (getAppEnv() === 'LOCAL' && fs.existsSync(localFilePath)) {
      return this.fetchLocalPage(lang, slugs);
    }
    return this.dbLandingPage(lang, slugs);
  }

  public async fetchLocalPage(lang: string, slugs: string[]): Promise<LandingPage | null> {
    // fetch localhost 4006 /pages/zh-CN/xxx
    const content = await fetch(`http://localhost:4006/pages/${lang}/${slugs.join('/')}`).then((res) => res.json());
    return content as LandingPage;
  }
}
