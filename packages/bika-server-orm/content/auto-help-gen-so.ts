import { getAIIntentTypesConfig } from '@bika/contents/config/client/ai/wizard';
import { getActionTypesConfig } from '@bika/contents/config/client/automation/actions';
import { getTriggerTypesConfig } from '@bika/contents/config/client/automation/triggers';
import { getWidgetTypesConfig } from '@bika/contents/config/client/dashboard/widgets';
import { getFieldTypesConfig } from '@bika/contents/config/client/database/fields';
import { getFormulaTypesConfig } from '@bika/contents/config/client/database/formulas';
import { getDatabaseViewTypesConfig } from '@bika/contents/config/client/database/views';
import { getIntegrationTypesConfig } from '@bika/contents/config/client/integration/integrations';
import { getMissionsTypesConfig } from '@bika/contents/config/client/mission/missions';
import { getResourcesTypesConfig } from '@bika/contents/config/client/node/node-resources';
import { getSpaceFeatureTypesConfig } from '@bika/contents/config/client/space/space-features';
import { getFeatureAbilityConfig, getFeatureInfo } from '@bika/contents/config/client/system/features';
import type { ILocaleContext } from '@bika/contents/i18n/context';
import { getServerLocaleContext } from '@bika/contents/i18n/server';
import { getTranslationsByLocale } from '@bika/contents/i18n/translate';
import { iStringParse, type Locale } from 'basenext/i18n';
import type { FeatureType, IFeatureAbilityConfigDetail } from '@bika/types/website/bo';
import { PageProps } from './types';

/**
 * 自动根据程序配置、config，自动生成帮助文档
 */
export class AutoHelpGenSO {
  /**
   * 根据feature的类型、路由，获取自动帮助(auto help)配置
   *
   * @param localeContext
   * @param featureCategory
   * @param featureName
   * @returns
   */
  static async getAutoHelpConfig(
    locale: Locale,
    featureCategory: FeatureType,
    featureName: string,
  ): Promise<IFeatureAbilityConfigDetail> {
    // TIPS: 确保枚举化(因为除了 ai-models，其它 feature 都是大写下划线区分定义的)
    const enumFeatureName =
      featureCategory !== 'ai-model' ? featureName.toUpperCase().replaceAll('-', '_') : featureName;

    const localeContext: ILocaleContext = {
      lang: locale,
      t: await getTranslationsByLocale(locale),
      i: (s) => iStringParse(s, locale),
    };

    return getFeatureAbilityConfig(localeContext, featureCategory, enumFeatureName);
  }

  /**
   * 获取自动生成的帮助文档的menus items
   */
  static getMenuItems(locale: Locale): PageProps[] {
    const autoMenuItems: PageProps[] = [];
    const localeContext = getServerLocaleContext(locale);

    // node resources
    autoMenuItems.push({
      lang: locale as string,
      slugs: ['reference', 'node-resource', 'features-list'],
      metadata: {
        sidebar_position: '1000',
        sidebar_label: getFeatureInfo('node-resource', localeContext).label,
      },
    });
    for (const [key, value] of Object.entries(getResourcesTypesConfig(localeContext))) {
      if (value.display !== 'HIDDEN') {
        autoMenuItems.push({
          lang: locale as string,
          slugs: ['reference', 'node-resource', key.toLowerCase().replaceAll('_', '-')],
          metadata: {
            sidebar_position: '1000',
            sidebar_label: value.label,
          },
        });
      }
    }
    // automation trigger
    autoMenuItems.push({
      lang: locale as string,
      slugs: ['reference', 'automation-trigger', 'features-list'],
      metadata: {
        sidebar_position: '1000',
        sidebar_label: getFeatureInfo('automation-trigger', localeContext).label,
      },
    });
    for (const [key, value] of Object.entries(getTriggerTypesConfig(localeContext))) {
      if (value.display !== 'HIDDEN') {
        autoMenuItems.push({
          lang: locale as string,
          slugs: ['reference', 'automation-trigger', key.toLowerCase().replaceAll('_', '-')],
          metadata: {
            sidebar_position: '1000',
            sidebar_label: value.label,
          },
        });
      }
    }
    // automation actions
    autoMenuItems.push({
      lang: locale as string,
      slugs: ['reference', 'automation-action', 'features-list'],
      metadata: {
        sidebar_position: '1000',
        sidebar_label: getFeatureInfo('automation-action', localeContext).label,
      },
    });
    for (const [key, value] of Object.entries(getActionTypesConfig(localeContext))) {
      if (value.display !== 'HIDDEN') {
        autoMenuItems.push({
          lang: locale as string,
          slugs: ['reference', 'automation-action', key.toLowerCase().replaceAll('_', '-')],
          metadata: {
            sidebar_position: '1000',
            sidebar_label: value.label,
          },
        });
      }
    }
    // database fields
    autoMenuItems.push({
      lang: locale as string,
      slugs: ['reference', 'database-field', 'features-list'],
      metadata: {
        sidebar_position: '1000',
        sidebar_label: getFeatureInfo('database-field', localeContext).label,
      },
    });
    for (const [key, value] of Object.entries(getFieldTypesConfig(localeContext))) {
      if (value.display !== 'HIDDEN') {
        autoMenuItems.push({
          lang: locale as string,
          slugs: ['reference', 'database-field', key.toLowerCase().replaceAll('_', '-')],
          metadata: {
            sidebar_position: '1000',
            sidebar_label: value.label,
          },
        });
      }
    }
    // database view
    autoMenuItems.push({
      lang: locale as string,
      slugs: ['reference', 'database-view', 'features-list'],
      metadata: {
        sidebar_position: '1000',
        sidebar_label: getFeatureInfo('database-view', localeContext).label,
      },
    });
    for (const [key, value] of Object.entries(getDatabaseViewTypesConfig(localeContext))) {
      if (value.display !== 'HIDDEN') {
        autoMenuItems.push({
          lang: locale as string,
          slugs: ['reference', 'database-view', key.toLowerCase().replaceAll('_', '-')],
          metadata: {
            sidebar_position: '1000',
            sidebar_label: value.label,
          },
        });
      }
    }

    // dashboard widgeets
    autoMenuItems.push({
      lang: locale as string,
      slugs: ['reference', 'dashboard-widget', 'features-list'],
      metadata: {
        sidebar_position: '1000',
        sidebar_label: getFeatureInfo('dashboard-widget', localeContext).label,
      },
    });
    for (const [key, value] of Object.entries(getWidgetTypesConfig(localeContext))) {
      if (value.display !== 'HIDDEN') {
        autoMenuItems.push({
          lang: locale as string,
          slugs: ['reference', 'dashboard-widget', key.toLowerCase().replaceAll('_', '-')],
          metadata: {
            sidebar_position: '1000',
            sidebar_label: value.label,
          },
        });
      }
    }

    // integrations
    autoMenuItems.push({
      lang: locale as string,
      slugs: ['reference', 'integration', 'features-list'],
      metadata: {
        sidebar_position: '1000',
        sidebar_label: getFeatureInfo('integration', localeContext).label,
      },
    });
    for (const [key, value] of Object.entries(getIntegrationTypesConfig(localeContext))) {
      if (value.displayConfig.display !== 'HIDDEN') {
        autoMenuItems.push({
          lang: locale as string,
          slugs: ['reference', 'integration', key.toLowerCase().replaceAll('_', '-')],
          metadata: {
            sidebar_position: '1000',
            sidebar_label: value.displayConfig.label,
          },
        });
      }
    }

    // mission
    autoMenuItems.push({
      lang: locale as string,
      slugs: ['reference', 'mission', 'features-list'],
      metadata: {
        sidebar_position: '1000',
        sidebar_label: getFeatureInfo('mission', localeContext).label,
      },
    });
    for (const [key, value] of Object.entries(getMissionsTypesConfig(localeContext))) {
      if (value.display !== 'HIDDEN') {
        autoMenuItems.push({
          lang: locale as string,
          slugs: ['reference', 'mission', key.toLowerCase().replaceAll('_', '-')],
          metadata: {
            sidebar_position: '1000',
            sidebar_label: value.label,
          },
        });
      }
    }

    // 其它的，开发环境显示only
    // if (getAppEnv() !== 'PRODUCTION') {
    // 字段公式读取
    autoMenuItems.push({
      lang: locale as string,
      slugs: ['reference', 'formula', 'features-list'],
      metadata: {
        sidebar_position: '1000',
        sidebar_label: getFeatureInfo('formula', localeContext).label,
      },
    });
    for (const [key, value] of Object.entries(getFormulaTypesConfig(localeContext))) {
      // for (const aiWizard of aiWizards) {
      if (value.display !== 'HIDDEN') {
        autoMenuItems.push({
          lang: locale as string,
          slugs: ['reference', 'formula', key.toLowerCase().replaceAll('_', '-')],
          metadata: {
            sidebar_position: '1000',
            sidebar_label: value.label,
          },
        });
      }
    }

    // AI Wizard
    autoMenuItems.push({
      lang: locale as string,
      slugs: ['reference', 'ai-wizard', 'features-list'],
      metadata: {
        sidebar_position: '1000',
        sidebar_label: getFeatureInfo('ai-wizard', localeContext).label,
      },
    });
    for (const [key, value] of Object.entries(getAIIntentTypesConfig(localeContext))) {
      if (value.display !== 'HIDDEN') {
        autoMenuItems.push({
          lang: locale as string,
          slugs: ['reference', 'ai-wizard', key.toLowerCase().replaceAll('_', '-')],
          metadata: {
            sidebar_position: '1000',
            sidebar_label: value.label,
          },
        });
      }
    }
    // }

    // Space
    autoMenuItems.push({
      lang: locale as string,
      slugs: ['reference', 'space', 'features-list'],
      metadata: {
        sidebar_position: '1000',
        sidebar_label: getFeatureInfo('space', localeContext).label,
      },
    });
    for (const [key, value] of Object.entries(getSpaceFeatureTypesConfig(localeContext))) {
      if (value.display !== 'HIDDEN') {
        autoMenuItems.push({
          lang: locale as string,
          slugs: ['reference', 'space', key.toLowerCase().replaceAll('_', '-')],
          metadata: {
            sidebar_position: '1000',
            sidebar_label: value.label,
          },
        });
      }
    }

    return autoMenuItems;
  }
}
