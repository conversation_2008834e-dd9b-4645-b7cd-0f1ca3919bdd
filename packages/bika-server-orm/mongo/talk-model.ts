/**
 * MongoDB Mongoose Models for Database
 */

import mongoose, { InferSchemaType } from 'mongoose';
import { TalkBOSchema } from '@bika/types/space/bo';
import { modNanoId } from 'basenext/utils';
import { getAppEnv } from 'sharelib/app-env';
import { RecipientPropsSchema } from '@bika/types/system/recipient';

/**
 * AI Intent Wizard Dialog
 */
export const TalkSchema = new mongoose.Schema(
  {
    id: { type: String, required: true, index: true },

    createdBy: { type: String, index: true },
    updatedBy: { type: String }, // user id

    recipient: {
      type: Object,
      required: true,
      validate: {
        validator: (v: object) => {
          RecipientPropsSchema.parse(v);
          return true;
        },
        message: (props: mongoose.ValidatorProps) =>
          `${props.value} is not valid RecipientPropsSchema: ${JSON.stringify(props)}`,
      },
    },
    // BO
    data: {
      type: Object,
      required: true,
      validate: {
        validator: (v: object) => {
          TalkBOSchema.parse(v);
          return true;
        },
        message: (props: mongoose.ValidatorProps) =>
          `${props.value} is not valid FeedBOSchema: ${JSON.stringify(props)}`,
      },
    },
    count: { type: Number, default: 0 },
    // 是否置顶
    isPinned: { type: Boolean, default: false },
  },
  {
    timestamps: true,
  },
);

export type TalkModel = InferSchemaType<typeof TalkSchema>;

export function TalkDAO(recipientId: string) {
  // LOCAL  开发模式，单个 collection 方便调试
  const maxCollectionCount = getAppEnv() === 'LOCAL' ? 1 : 128;
  const collectionHashNum = modNanoId(recipientId, maxCollectionCount);
  const daoName = `Talk_${collectionHashNum}`;

  const dao: mongoose.Model<TalkModel> = mongoose.models[daoName] || mongoose.model(daoName, TalkSchema);
  return dao;
}
