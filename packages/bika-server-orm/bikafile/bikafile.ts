import path from 'path';
import AdmZip from 'adm-zip';
import { i18n, iString } from 'basenext/i18n';
import { BikafileData, BikafileMetadataSchema } from '@bika/types/bikafile/bo';
import { tsCode } from './ts-code-gen';

/**
 *
 * Bikafile文件格式，一个zip格式包，适用以下场景：
 *
 * - 导出Bika文件夹(Resources)，内置数据，导入时导入数据
 * - 导出Template文件(template.json)，导入时相当于安装模板（不link）
 * - 开发环境template.ts和template.json，如果传入js或ts文件，就进入纯开发编辑模式，修改template.ts和
 * - 作为一种中间化的文件格式，Excel导入、CSV导入等等，先转成Bikafile，再进行导入，格式化转换围绕Bikafile即可
 *
 */
export class Bikafile {
  private _zip: AdmZip;

  private _filePath: string | undefined;

  public get format() {
    return this.metadata.format;
  }

  constructor(filePath?: string | BikafileData) {
    if (typeof filePath === 'string') {
      this._zip = new AdmZip(filePath);
    } else {
      this._zip = new AdmZip();
      if (filePath) {
        this.setData(filePath);
      }
    }
  }

  public get assets() {
    return this._zip
      .getEntries()
      .map((entry) => {
        const zipPath = entry.entryName;

        if (zipPath.startsWith('assets/')) {
          return zipPath.slice(7);
        }
        return undefined;
      })
      .filter((item) => item !== undefined);
  }

  public addAsset(name: string, data: Buffer) {
    this._zip.addFile(`assets/${name}`, data);
  }

  public addLocalFileAsAsset(name: string, filePath: string) {
    this._zip.addLocalFile(filePath, 'assets', name);
  }

  public getAssets(): { buffer: Buffer; fileName: string }[] {
    const zip = this._zip.getEntries();
    const assets = zip.filter((entry) => entry.entryName.startsWith('assets/'));
    return assets.map((asset) => ({
      buffer: asset.getData(),
      fileName: asset.entryName.slice(7),
    }));
  }

  public hasAsset(name: string) {
    return this._zip.getEntry(`assets/${name}`) !== null;
  }

  public async setData(data: BikafileData) {
    const zip = this._zip;
    const metadata = {
      format: data.format,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    zip.addFile('Bikafile', Buffer.from(JSON.stringify(metadata), 'utf-8'));

    if (data.format === 'TEMPLATE') {
      zip.addFile('template.json', Buffer.from(JSON.stringify(data.template, null, 2), 'utf-8'));

      const mTsCode = await tsCode(data.template);
      zip.addFile('template.ts', Buffer.from(mTsCode, 'utf-8'));

      if (data.readme) {
        if (typeof data.readme === 'string') {
          zip.addFile('README.en.md', Buffer.from(data.readme || '', 'utf-8'));
        } else {
          for (const [lang, rme] of Object.entries(data.readme)) {
            zip.addFile(`README.${lang}.md`, Buffer.from(rme || '', 'utf-8'));
          }
        }
      }

      if (data.releases.length > 0) {
        for (const release of data.releases) {
          zip.addFile(
            path.join('release', release.version, 'template.json'),
            Buffer.from(JSON.stringify(release.data, null, 2), 'utf-8'),
          );
          zip.addFile(
            path.join('release', release.version, 'change-log.md'),
            Buffer.from(release.changelog || '', 'utf-8'),
          );
          zip.addFile(
            path.join('release', release.version, 'release-notes.md'),
            Buffer.from(release.releaseNotes || '', 'utf-8'),
          );
        }
      }
    } else if (data.format === 'RESOURCES') {
      zip.addFile('resources.json', Buffer.from(JSON.stringify(data.resources, null, 2), 'utf-8'));
    } else {
      throw new Error(`not support: ${metadata.format}`);
    }
  }

  public get data() {
    let data: BikafileData;
    const metadata = this.metadata;
    const zip = this._zip;

    if (metadata.format === 'TEMPLATE') {
      const mainTemplateJSON = JSON.parse(zip.readAsText('template.json'));

      const readme: iString = {};
      for (const locale of i18n.locales) {
        if (zip.getEntry(`README.${locale}.md`)) {
          const zReadme = zip.readAsText(`README.${locale}.md`);
          readme[locale] = zReadme;
        }
      }

      data = {
        format: 'TEMPLATE',
        template: mainTemplateJSON,
        readme,
        releases: [], // todo
      };
    } else if (metadata.format === 'RESOURCES') {
      data = {
        format: 'RESOURCES',
        resources: JSON.parse(zip.readAsText('resources.json')),
      };
    } else {
      throw new Error(`not support: ${metadata.format}`);
    }
    return data;
  }

  public get metadata() {
    const strBikaFile = this._zip.readAsText('Bikafile');
    const bikaFileObj = JSON.parse(strBikaFile);
    const metadata = BikafileMetadataSchema.parse(bikaFileObj);
    return metadata;
  }

  async writeFile(filePath: string) {
    this._zip.writeZip(filePath);
    return path.resolve(filePath);
  }
}
